package com.example.aiagent.core

import android.content.Context
import android.util.Log
import com.example.aiagent.data.model.*
import com.example.aiagent.data.repository.FirebaseVideoRepository
import com.example.aiagent.data.repository.VideoProjectRepository
import com.example.aiagent.data.repository.GeminiRepository
import com.example.aiagent.service.VideoProcessingService
import com.example.aiagent.utils.SchedulerManager
import com.example.aiagent.utils.NotificationHelper
import com.example.aiagent.utils.SmartContentManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.*
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير الوكيل الذكي
 * يدير العملية الكاملة لإنشاء ونشر الفيديوهات تلقائياً
 */
@Singleton
class AiAgentManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val firebaseVideoRepository: FirebaseVideoRepository,
    private val videoProjectRepository: VideoProjectRepository,
    private val geminiRepository: GeminiRepository,
    private val schedulerManager: SchedulerManager,
    private val notificationHelper: NotificationHelper,
    private val smartContentManager: SmartContentManager
) {
    
    private val scope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    companion object {
        private const val TAG = "AiAgentManager"
    }

    /**
     * بدء الوكيل الذكي
     */
    fun startAgent(settings: UserSettings) {
        scope.launch {
            try {
                Log.d(TAG, "Starting AI Agent with settings: ${settings.channelName}")
                
                // إعداد الجدولة الدورية
                if (settings.autoUpload) {
                    schedulerManager.setupPeriodicSchedule(settings.uploadSchedule)
                }
                
                // إنشاء مشروع فوري إذا لم يكن هناك مشاريع معلقة
                val pendingProjects = videoProjectRepository.getProjectsByStatus(VideoStatus.PENDING)
                if (pendingProjects.isEmpty()) {
                    createImmediateProject(settings)
                }
                
                notificationHelper.showSuccess(
                    "تم بدء الوكيل الذكي",
                    "الوكيل يعمل الآن وسيقوم بنشر الفيديوهات تلقائياً"
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Error starting AI Agent", e)
                notificationHelper.showError(
                    "خطأ في بدء الوكيل",
                    "فشل في بدء الوكيل الذكي: ${e.message}"
                )
            }
        }
    }

    /**
     * إيقاف الوكيل الذكي
     */
    fun stopAgent() {
        scope.launch {
            try {
                Log.d(TAG, "Stopping AI Agent")
                
                // إلغاء الجدولة الدورية
                schedulerManager.cancelPeriodicSchedule()
                
                notificationHelper.showInfo(
                    "تم إيقاف الوكيل الذكي",
                    "الوكيل متوقف الآن"
                )
                
            } catch (e: Exception) {
                Log.e(TAG, "Error stopping AI Agent", e)
            }
        }
    }

    /**
     * إنشاء مشروع فوري
     */
    fun createImmediateProject(settings: UserSettings) {
        scope.launch {
            try {
                Log.d(TAG, "Creating immediate project")
                
                // إنشاء معايير البحث بناءً على الإعدادات
                val criteria = VideoSearchCriteria(
                    category = VideoCategory.FUNNY, // يمكن تخصيصه
                    mood = VideoMood.FUNNY,
                    language = "ar",
                    maxDuration = 60,
                    minRating = 3.0f,
                    excludeUsedVideos = true,
                    limit = 1
                )
                
                // إنشاء مشروع من Firebase
                val projectResult = firebaseVideoRepository.createVideoProjectFromFirebase(
                    channelName = settings.channelName,
                    criteria = criteria
                )
                
                if (projectResult.isSuccess) {
                    val project = projectResult.getOrNull()!!
                    
                    // تحسين المحتوى بـ Gemini AI
                    val enhancedProject = enhanceProjectWithAI(project, settings)
                    
                    // حفظ المشروع في قاعدة البيانات
                    videoProjectRepository.createProject(enhancedProject)
                    
                    // بدء معالجة الفيديو
                    VideoProcessingService.startProcessing(context, enhancedProject.id)
                    
                    notificationHelper.showSuccess(
                        "تم إنشاء مشروع جديد",
                        "بدء معالجة فيديو: ${enhancedProject.title}"
                    )
                } else {
                    throw projectResult.exceptionOrNull() ?: Exception("فشل في إنشاء المشروع")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error creating immediate project", e)
                notificationHelper.showError(
                    "خطأ في إنشاء المشروع",
                    "فشل في إنشاء مشروع جديد: ${e.message}"
                )
            }
        }
    }

    /**
     * إنشاء مشروع مجدول
     */
    fun createScheduledProject(settings: UserSettings, scheduledTime: Long) {
        scope.launch {
            try {
                Log.d(TAG, "Creating scheduled project for: ${Date(scheduledTime)}")
                
                val criteria = VideoSearchCriteria(
                    category = VideoCategory.FUNNY,
                    mood = VideoMood.FUNNY,
                    language = "ar",
                    maxDuration = 60,
                    minRating = 3.0f,
                    excludeUsedVideos = true,
                    limit = 1
                )
                
                // إنشاء مشروع مجدول
                val projectResult = firebaseVideoRepository.createScheduledVideoProject(
                    channelName = settings.channelName,
                    scheduledTime = scheduledTime,
                    criteria = criteria
                )
                
                if (projectResult.isSuccess) {
                    val project = projectResult.getOrNull()!!
                    
                    // تحسين المحتوى بـ Gemini AI
                    val enhancedProject = enhanceProjectWithAI(project, settings)
                    
                    // حفظ المشروع
                    videoProjectRepository.createProject(enhancedProject)
                    
                    // جدولة المشروع
                    schedulerManager.scheduleProject(enhancedProject)
                    
                    notificationHelper.showSuccess(
                        "تم جدولة مشروع جديد",
                        "سيتم نشر الفيديو في: ${Date(scheduledTime)}"
                    )
                } else {
                    throw projectResult.exceptionOrNull() ?: Exception("فشل في إنشاء المشروع المجدول")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error creating scheduled project", e)
                notificationHelper.showError(
                    "خطأ في جدولة المشروع",
                    "فشل في جدولة مشروع جديد: ${e.message}"
                )
            }
        }
    }

    /**
     * إنشاء عدة مشاريع مجدولة
     */
    fun createBatchScheduledProjects(
        settings: UserSettings,
        count: Int,
        intervalHours: Int = 24
    ) {
        scope.launch {
            try {
                Log.d(TAG, "Creating $count scheduled projects")
                
                val criteria = VideoSearchCriteria(
                    category = VideoCategory.FUNNY,
                    mood = VideoMood.FUNNY,
                    language = "ar",
                    maxDuration = 60,
                    minRating = 3.0f,
                    excludeUsedVideos = true,
                    limit = count
                )
                
                // إنشاء مشاريع متعددة
                val projectsResult = firebaseVideoRepository.createMultipleVideoProjects(
                    channelName = settings.channelName,
                    count = count,
                    criteria = criteria
                )
                
                if (projectsResult.isSuccess) {
                    val projects = projectsResult.getOrNull()!!
                    var currentTime = System.currentTimeMillis() + (intervalHours * 60 * 60 * 1000L)
                    
                    for (project in projects) {
                        // تحسين المحتوى
                        val enhancedProject = enhanceProjectWithAI(
                            project.copy(scheduledUploadTime = currentTime),
                            settings
                        )
                        
                        // حفظ المشروع
                        videoProjectRepository.createProject(enhancedProject)
                        
                        // جدولة المشروع
                        schedulerManager.scheduleProject(enhancedProject)
                        
                        // زيادة الوقت للمشروع التالي
                        currentTime += (intervalHours * 60 * 60 * 1000L)
                    }
                    
                    notificationHelper.showSuccess(
                        "تم إنشاء المشاريع المجدولة",
                        "تم جدولة $count مشروع للنشر التلقائي"
                    )
                } else {
                    throw projectsResult.exceptionOrNull() ?: Exception("فشل في إنشاء المشاريع")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error creating batch scheduled projects", e)
                notificationHelper.showError(
                    "خطأ في إنشاء المشاريع",
                    "فشل في إنشاء المشاريع المجدولة: ${e.message}"
                )
            }
        }
    }

    /**
     * تحسين المشروع باستخدام Gemini AI الذكي
     */
    private suspend fun enhanceProjectWithAI(
        project: VideoProject,
        settings: UserSettings
    ): VideoProject {
        return try {
            if (settings.geminiApiKey.isNullOrEmpty()) {
                Log.w(TAG, "Gemini API key not set, skipping AI enhancement")
                return project
            }

            // تهيئة Gemini إذا لم يكن مهيأً
            geminiRepository.initializeGemini(settings.geminiApiKey)

            // إنشاء فيديو Firebase وهمي للمعالجة
            val firebaseVideo = com.example.aiagent.data.model.FirebaseVideo(
                title = project.title,
                description = project.description,
                tags = project.hashtags,
                category = VideoCategory.FUNNY,
                mood = VideoMood.FUNNY
            )

            // استخدام مدير المحتوى الذكي
            val contentResult = smartContentManager.generateSmartContent(
                firebaseVideo = firebaseVideo,
                channelName = settings.channelName,
                settings = settings
            )

            if (contentResult.isSuccess) {
                val content = contentResult.getOrNull()!!
                project.copy(
                    title = content.title.ifEmpty { project.title },
                    description = content.description.ifEmpty { project.description },
                    hashtags = if (content.hashtags.isNotEmpty()) content.hashtags else project.hashtags
                )
            } else {
                Log.w(TAG, "Failed to enhance with smart AI: ${contentResult.exceptionOrNull()?.message}")

                // العودة للطريقة التقليدية كبديل
                fallbackToBasicAI(project, settings)
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error enhancing project with smart AI", e)

            // العودة للطريقة التقليدية كبديل
            fallbackToBasicAI(project, settings)
        }
    }

    /**
     * طريقة احتياطية للتحسين بـ AI الأساسي
     */
    private suspend fun fallbackToBasicAI(
        project: VideoProject,
        settings: UserSettings
    ): VideoProject {
        return try {
            val contentResult = geminiRepository.generateVideoContent(
                videoDescription = project.description,
                channelName = settings.channelName,
                keywords = project.hashtags
            )

            if (contentResult.isSuccess) {
                val content = contentResult.getOrNull()!!
                project.copy(
                    title = content.title.ifEmpty { project.title },
                    description = content.description.ifEmpty { project.description },
                    hashtags = if (content.hashtags.isNotEmpty()) content.hashtags else project.hashtags
                )
            } else {
                project
            }
        } catch (e: Exception) {
            Log.w(TAG, "Fallback AI enhancement also failed", e)
            project
        }
    }

    /**
     * تنظيف الموارد
     */
    fun cleanup() {
        scope.cancel()
        firebaseVideoRepository.cleanupTempFiles()
    }
}
