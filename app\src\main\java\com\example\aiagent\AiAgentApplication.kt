package com.example.aiagent

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
import com.example.aiagent.utils.FirebaseInitializer
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject

/**
 * فئة التطبيق الرئيسية مع إعداد Hilt و Work Manager
 */
@HiltAndroidApp
class AiAgentApplication : Application(), Configuration.Provider {

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @Inject
    lateinit var firebaseInitializer: FirebaseInitializer

    override fun onCreate() {
        super.onCreate()

        // تهيئة Firebase
        initializeFirebase()

        // إنشاء قنوات الإشعارات
        createNotificationChannels()

        // إعداد Work Manager
        setupWorkManager()
    }

    /**
     * إعداد Work Manager مع Hilt
     */
    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .setMinimumLoggingLevel(android.util.Log.INFO)
            .build()

    private fun setupWorkManager() {
        WorkManager.initialize(this, workManagerConfiguration)
    }

    /**
     * تهيئة Firebase
     */
    private fun initializeFirebase() {
        try {
            val success = firebaseInitializer.initialize()
            if (success) {
                android.util.Log.d("AiAgentApplication", "Firebase initialized successfully")
            } else {
                android.util.Log.e("AiAgentApplication", "Failed to initialize Firebase")
            }
        } catch (e: Exception) {
            android.util.Log.e("AiAgentApplication", "Error initializing Firebase", e)
        }
    }

    /**
     * إنشاء قنوات الإشعارات المطلوبة
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // قناة خدمة معالجة الفيديو
            val videoProcessingChannel = NotificationChannel(
                CHANNEL_VIDEO_PROCESSING,
                "معالجة الفيديو",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "إشعارات معالجة وتحرير الفيديوهات"
                setShowBadge(false)
            }

            // قناة رفع الفيديوهات
            val uploadChannel = NotificationChannel(
                CHANNEL_UPLOAD,
                "رفع الفيديوهات",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "إشعارات رفع الفيديوهات على يوتيوب"
                setShowBadge(false)
            }

            // قناة الأخطاء
            val errorChannel = NotificationChannel(
                CHANNEL_ERROR,
                "الأخطاء",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "إشعارات الأخطاء والمشاكل"
                setShowBadge(true)
            }

            // قناة النجاح
            val successChannel = NotificationChannel(
                CHANNEL_SUCCESS,
                "العمليات الناجحة",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "إشعارات العمليات المكتملة بنجاح"
                setShowBadge(true)
            }

            // تسجيل القنوات
            notificationManager.createNotificationChannels(
                listOf(
                    videoProcessingChannel,
                    uploadChannel,
                    errorChannel,
                    successChannel
                )
            )
        }
    }

    companion object {
        // معرفات قنوات الإشعارات
        const val CHANNEL_VIDEO_PROCESSING = "video_processing"
        const val CHANNEL_UPLOAD = "upload"
        const val CHANNEL_ERROR = "error"
        const val CHANNEL_SUCCESS = "success"
        
        // معرفات الإشعارات
        const val NOTIFICATION_ID_VIDEO_PROCESSING = 1001
        const val NOTIFICATION_ID_UPLOAD = 1002
        const val NOTIFICATION_ID_ERROR = 1003
        const val NOTIFICATION_ID_SUCCESS = 1004
    }
}
