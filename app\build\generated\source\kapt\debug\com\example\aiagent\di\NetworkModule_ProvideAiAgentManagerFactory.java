package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.core.AiAgentManager;
import com.example.aiagent.data.repository.FirebaseVideoRepository;
import com.example.aiagent.data.repository.GeminiRepository;
import com.example.aiagent.data.repository.VideoProjectRepository;
import com.example.aiagent.utils.NotificationHelper;
import com.example.aiagent.utils.SchedulerManager;
import com.example.aiagent.utils.SmartContentManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideAiAgentManagerFactory implements Factory<AiAgentManager> {
  private final Provider<Context> contextProvider;

  private final Provider<FirebaseVideoRepository> firebaseVideoRepositoryProvider;

  private final Provider<VideoProjectRepository> videoProjectRepositoryProvider;

  private final Provider<GeminiRepository> geminiRepositoryProvider;

  private final Provider<SchedulerManager> schedulerManagerProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  private final Provider<SmartContentManager> smartContentManagerProvider;

  public NetworkModule_ProvideAiAgentManagerFactory(Provider<Context> contextProvider,
      Provider<FirebaseVideoRepository> firebaseVideoRepositoryProvider,
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<GeminiRepository> geminiRepositoryProvider,
      Provider<SchedulerManager> schedulerManagerProvider,
      Provider<NotificationHelper> notificationHelperProvider,
      Provider<SmartContentManager> smartContentManagerProvider) {
    this.contextProvider = contextProvider;
    this.firebaseVideoRepositoryProvider = firebaseVideoRepositoryProvider;
    this.videoProjectRepositoryProvider = videoProjectRepositoryProvider;
    this.geminiRepositoryProvider = geminiRepositoryProvider;
    this.schedulerManagerProvider = schedulerManagerProvider;
    this.notificationHelperProvider = notificationHelperProvider;
    this.smartContentManagerProvider = smartContentManagerProvider;
  }

  @Override
  public AiAgentManager get() {
    return provideAiAgentManager(contextProvider.get(), firebaseVideoRepositoryProvider.get(), videoProjectRepositoryProvider.get(), geminiRepositoryProvider.get(), schedulerManagerProvider.get(), notificationHelperProvider.get(), smartContentManagerProvider.get());
  }

  public static NetworkModule_ProvideAiAgentManagerFactory create(Provider<Context> contextProvider,
      Provider<FirebaseVideoRepository> firebaseVideoRepositoryProvider,
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<GeminiRepository> geminiRepositoryProvider,
      Provider<SchedulerManager> schedulerManagerProvider,
      Provider<NotificationHelper> notificationHelperProvider,
      Provider<SmartContentManager> smartContentManagerProvider) {
    return new NetworkModule_ProvideAiAgentManagerFactory(contextProvider, firebaseVideoRepositoryProvider, videoProjectRepositoryProvider, geminiRepositoryProvider, schedulerManagerProvider, notificationHelperProvider, smartContentManagerProvider);
  }

  public static AiAgentManager provideAiAgentManager(Context context,
      FirebaseVideoRepository firebaseVideoRepository,
      VideoProjectRepository videoProjectRepository, GeminiRepository geminiRepository,
      SchedulerManager schedulerManager, NotificationHelper notificationHelper,
      SmartContentManager smartContentManager) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideAiAgentManager(context, firebaseVideoRepository, videoProjectRepository, geminiRepository, schedulerManager, notificationHelper, smartContentManager));
  }
}
