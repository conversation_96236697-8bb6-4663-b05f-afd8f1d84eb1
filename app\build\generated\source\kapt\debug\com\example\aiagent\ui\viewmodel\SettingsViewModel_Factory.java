package com.example.aiagent.ui.viewmodel;

import com.example.aiagent.data.repository.UserSettingsRepository;
import com.example.aiagent.utils.NotificationHelper;
import com.example.aiagent.utils.YouTubeSetupManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SettingsViewModel_Factory implements Factory<SettingsViewModel> {
  private final Provider<UserSettingsRepository> userSettingsRepositoryProvider;

  private final Provider<YouTubeSetupManager> youTubeSetupManagerProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  public SettingsViewModel_Factory(Provider<UserSettingsRepository> userSettingsRepositoryProvider,
      Provider<YouTubeSetupManager> youTubeSetupManagerProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    this.userSettingsRepositoryProvider = userSettingsRepositoryProvider;
    this.youTubeSetupManagerProvider = youTubeSetupManagerProvider;
    this.notificationHelperProvider = notificationHelperProvider;
  }

  @Override
  public SettingsViewModel get() {
    return newInstance(userSettingsRepositoryProvider.get(), youTubeSetupManagerProvider.get(), notificationHelperProvider.get());
  }

  public static SettingsViewModel_Factory create(
      Provider<UserSettingsRepository> userSettingsRepositoryProvider,
      Provider<YouTubeSetupManager> youTubeSetupManagerProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    return new SettingsViewModel_Factory(userSettingsRepositoryProvider, youTubeSetupManagerProvider, notificationHelperProvider);
  }

  public static SettingsViewModel newInstance(UserSettingsRepository userSettingsRepository,
      YouTubeSetupManager youTubeSetupManager, NotificationHelper notificationHelper) {
    return new SettingsViewModel(userSettingsRepository, youTubeSetupManager, notificationHelper);
  }
}
