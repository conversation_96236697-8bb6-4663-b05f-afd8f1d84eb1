package com.example.aiagent.service;

import com.example.aiagent.data.repository.VideoProjectRepository;
import com.example.aiagent.data.repository.YouTubeRepository;
import com.example.aiagent.utils.NotificationHelper;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class YouTubeUploadService_MembersInjector implements MembersInjector<YouTubeUploadService> {
  private final Provider<VideoProjectRepository> videoProjectRepositoryProvider;

  private final Provider<YouTubeRepository> youTubeRepositoryProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  public YouTubeUploadService_MembersInjector(
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<YouTubeRepository> youTubeRepositoryProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    this.videoProjectRepositoryProvider = videoProjectRepositoryProvider;
    this.youTubeRepositoryProvider = youTubeRepositoryProvider;
    this.notificationHelperProvider = notificationHelperProvider;
  }

  public static MembersInjector<YouTubeUploadService> create(
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<YouTubeRepository> youTubeRepositoryProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    return new YouTubeUploadService_MembersInjector(videoProjectRepositoryProvider, youTubeRepositoryProvider, notificationHelperProvider);
  }

  @Override
  public void injectMembers(YouTubeUploadService instance) {
    injectVideoProjectRepository(instance, videoProjectRepositoryProvider.get());
    injectYouTubeRepository(instance, youTubeRepositoryProvider.get());
    injectNotificationHelper(instance, notificationHelperProvider.get());
  }

  @InjectedFieldSignature("com.example.aiagent.service.YouTubeUploadService.videoProjectRepository")
  public static void injectVideoProjectRepository(YouTubeUploadService instance,
      VideoProjectRepository videoProjectRepository) {
    instance.videoProjectRepository = videoProjectRepository;
  }

  @InjectedFieldSignature("com.example.aiagent.service.YouTubeUploadService.youTubeRepository")
  public static void injectYouTubeRepository(YouTubeUploadService instance,
      YouTubeRepository youTubeRepository) {
    instance.youTubeRepository = youTubeRepository;
  }

  @InjectedFieldSignature("com.example.aiagent.service.YouTubeUploadService.notificationHelper")
  public static void injectNotificationHelper(YouTubeUploadService instance,
      NotificationHelper notificationHelper) {
    instance.notificationHelper = notificationHelper;
  }
}
