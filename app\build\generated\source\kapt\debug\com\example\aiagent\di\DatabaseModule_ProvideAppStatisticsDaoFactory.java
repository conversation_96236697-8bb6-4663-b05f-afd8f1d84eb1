package com.example.aiagent.di;

import com.example.aiagent.data.database.AiAgentDatabase;
import com.example.aiagent.data.database.AppStatisticsDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvideAppStatisticsDaoFactory implements Factory<AppStatisticsDao> {
  private final Provider<AiAgentDatabase> databaseProvider;

  public DatabaseModule_ProvideAppStatisticsDaoFactory(Provider<AiAgentDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public AppStatisticsDao get() {
    return provideAppStatisticsDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideAppStatisticsDaoFactory create(
      Provider<AiAgentDatabase> databaseProvider) {
    return new DatabaseModule_ProvideAppStatisticsDaoFactory(databaseProvider);
  }

  public static AppStatisticsDao provideAppStatisticsDao(AiAgentDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideAppStatisticsDao(database));
  }
}
