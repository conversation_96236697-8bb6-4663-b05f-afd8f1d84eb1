package com.example.aiagent.di;

import com.example.aiagent.data.database.UserSettingsDao;
import com.example.aiagent.data.repository.UserSettingsRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideUserSettingsRepositoryFactory implements Factory<UserSettingsRepository> {
  private final Provider<UserSettingsDao> userSettingsDaoProvider;

  public NetworkModule_ProvideUserSettingsRepositoryFactory(
      Provider<UserSettingsDao> userSettingsDaoProvider) {
    this.userSettingsDaoProvider = userSettingsDaoProvider;
  }

  @Override
  public UserSettingsRepository get() {
    return provideUserSettingsRepository(userSettingsDaoProvider.get());
  }

  public static NetworkModule_ProvideUserSettingsRepositoryFactory create(
      Provider<UserSettingsDao> userSettingsDaoProvider) {
    return new NetworkModule_ProvideUserSettingsRepositoryFactory(userSettingsDaoProvider);
  }

  public static UserSettingsRepository provideUserSettingsRepository(
      UserSettingsDao userSettingsDao) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideUserSettingsRepository(userSettingsDao));
  }
}
