  Activity android.app  AlarmManager android.app  Application android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  Bundle android.app.Activity  
Configuration android.app.Application  FirebaseInitializer android.app.Application  HiltWorkerFactory android.app.Application  Inject android.app.Application  android android.app.Application  Context android.app.Service  IBinder android.app.Service  Inject android.app.Service  Int android.app.Service  Intent android.app.Service  Notification android.app.Service  NotificationHelper android.app.Service  String android.app.Service  VideoProcessor android.app.Service  VideoProjectRepository android.app.Service  YouTubeRepository android.app.Service  BroadcastReceiver android.content  Context android.content  Intent android.content  Context !android.content.BroadcastReceiver  Inject !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  SchedulerManager !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  
ALARM_SERVICE android.content.Context  Bundle android.content.Context  
Configuration android.content.Context  Context android.content.Context  FirebaseInitializer android.content.Context  HiltWorkerFactory android.content.Context  IBinder android.content.Context  Inject android.content.Context  Int android.content.Context  Intent android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  NotificationHelper android.content.Context  String android.content.Context  VideoProcessor android.content.Context  VideoProjectRepository android.content.Context  YouTubeRepository android.content.Context  android android.content.Context  getSystemService android.content.Context  Bundle android.content.ContextWrapper  
Configuration android.content.ContextWrapper  Context android.content.ContextWrapper  FirebaseInitializer android.content.ContextWrapper  HiltWorkerFactory android.content.ContextWrapper  IBinder android.content.ContextWrapper  Inject android.content.ContextWrapper  Int android.content.ContextWrapper  Intent android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationHelper android.content.ContextWrapper  String android.content.ContextWrapper  VideoProcessor android.content.ContextWrapper  VideoProjectRepository android.content.ContextWrapper  YouTubeRepository android.content.ContextWrapper  android android.content.ContextWrapper  ServiceInfo android.content.pm  Bitmap android.graphics  Canvas android.graphics  Color android.graphics  Paint android.graphics  Typeface android.graphics  Uri android.net  Build 
android.os  Bundle 
android.os  IBinder 
android.os  Log android.util  INFO android.util.Log  Bundle  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  
Composable androidx.compose.animation.core  
background androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  	Analytics "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  	Dashboard "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  Settings "androidx.compose.foundation.layout  VideoLibrary "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  	Analytics ,androidx.compose.material.icons.Icons.Filled  	Dashboard ,androidx.compose.material.icons.Icons.Filled  Settings ,androidx.compose.material.icons.Icons.Filled  VideoLibrary ,androidx.compose.material.icons.Icons.Filled  	Analytics &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  	Dashboard &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  VideoLibrary &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  	Analytics androidx.compose.material3  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  	Dashboard androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  Settings androidx.compose.material3  Surface androidx.compose.material3  
Typography androidx.compose.material3  VideoLibrary androidx.compose.material3  androidx androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  	Analytics androidx.compose.runtime  
Composable androidx.compose.runtime  	Dashboard androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  Settings androidx.compose.runtime  
SideEffect androidx.compose.runtime  VideoLibrary androidx.compose.runtime  listOf androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  invoke ,androidx.compose.ui.graphics.Color.Companion  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  Font androidx.compose.ui.text.font  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  invoke ,androidx.compose.ui.text.font.Font.Companion  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  invoke 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextOverflow androidx.compose.ui.text.style  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  NotificationCompat androidx.core.app  Bundle #androidx.core.app.ComponentActivity  WindowCompat androidx.core.view  
hiltViewModel  androidx.hilt.navigation.compose  
HiltWorker androidx.hilt.work  HiltWorkerFactory androidx.hilt.work  LifecycleService androidx.lifecycle  	ViewModel androidx.lifecycle  lifecycleScope androidx.lifecycle  viewModelScope androidx.lifecycle  Context #androidx.lifecycle.LifecycleService  IBinder #androidx.lifecycle.LifecycleService  Inject #androidx.lifecycle.LifecycleService  Int #androidx.lifecycle.LifecycleService  Intent #androidx.lifecycle.LifecycleService  Notification #androidx.lifecycle.LifecycleService  NotificationHelper #androidx.lifecycle.LifecycleService  String #androidx.lifecycle.LifecycleService  VideoProcessor #androidx.lifecycle.LifecycleService  VideoProjectRepository #androidx.lifecycle.LifecycleService  YouTubeRepository #androidx.lifecycle.LifecycleService  AiAgentManager androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  DashboardUiState androidx.lifecycle.ViewModel  Inject androidx.lifecycle.ViewModel  Int androidx.lifecycle.ViewModel  Long androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  NotificationHelper androidx.lifecycle.ViewModel  SettingsUiState androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  UserSettingsRepository androidx.lifecycle.ViewModel  VideoProjectRepository androidx.lifecycle.ViewModel  YouTubeSetupManager androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  
NavController androidx.navigation  NavDestination androidx.navigation  NavGraph androidx.navigation  	Companion "androidx.navigation.NavDestination  	hierarchy ,androidx.navigation.NavDestination.Companion  	Companion androidx.navigation.NavGraph  findStartDestination &androidx.navigation.NavGraph.Companion  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  currentBackStackEntryAsState androidx.navigation.compose  rememberNavController androidx.navigation.compose  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  
TypeConverter 
androidx.room  TypeConverters 
androidx.room  Update 
androidx.room  com 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  AiAgentDatabase androidx.room.RoomDatabase  AppStatisticsDao androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  OperationLogDao androidx.room.RoomDatabase  UserSettingsDao androidx.room.RoomDatabase  VideoProjectDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  
Configuration 
androidx.work  Context 
androidx.work  CoroutineWorker 
androidx.work  ExistingWorkPolicy 
androidx.work  OneTimeWorkRequestBuilder 
androidx.work  WorkManager 
androidx.work  WorkerParameters 
androidx.work  
workDataOf 
androidx.work  Builder androidx.work.Configuration  Provider androidx.work.Configuration  build #androidx.work.Configuration.Builder  setMinimumLoggingLevel #androidx.work.Configuration.Builder  setWorkerFactory #androidx.work.Configuration.Builder  Builder %androidx.work.Configuration.Companion  Assisted androidx.work.CoroutineWorker  AssistedInject androidx.work.CoroutineWorker  Context androidx.work.CoroutineWorker  NotificationHelper androidx.work.CoroutineWorker  Result androidx.work.CoroutineWorker  String androidx.work.CoroutineWorker  VideoProjectRepository androidx.work.CoroutineWorker  WorkerParameters androidx.work.CoroutineWorker  Assisted androidx.work.ListenableWorker  AssistedInject androidx.work.ListenableWorker  Context androidx.work.ListenableWorker  NotificationHelper androidx.work.ListenableWorker  Result androidx.work.ListenableWorker  String androidx.work.ListenableWorker  VideoProjectRepository androidx.work.ListenableWorker  WorkerParameters androidx.work.ListenableWorker  getInstance androidx.work.WorkManager  getInstance #androidx.work.WorkManager.Companion  	arthenica com  AiAgentApplication com.example.aiagent  
Configuration com.example.aiagent  MainActivity com.example.aiagent  R com.example.aiagent  android com.example.aiagent  
Configuration &com.example.aiagent.AiAgentApplication  FirebaseInitializer &com.example.aiagent.AiAgentApplication  HiltWorkerFactory &com.example.aiagent.AiAgentApplication  Inject &com.example.aiagent.AiAgentApplication  android &com.example.aiagent.AiAgentApplication  
getANDROID &com.example.aiagent.AiAgentApplication  
getAndroid &com.example.aiagent.AiAgentApplication  
workerFactory &com.example.aiagent.AiAgentApplication  
Configuration 0com.example.aiagent.AiAgentApplication.Companion  FirebaseInitializer 0com.example.aiagent.AiAgentApplication.Companion  HiltWorkerFactory 0com.example.aiagent.AiAgentApplication.Companion  Inject 0com.example.aiagent.AiAgentApplication.Companion  android 0com.example.aiagent.AiAgentApplication.Companion  
getANDROID 0com.example.aiagent.AiAgentApplication.Companion  
getAndroid 0com.example.aiagent.AiAgentApplication.Companion  Bundle  com.example.aiagent.MainActivity  font com.example.aiagent.R  roboto_bold com.example.aiagent.R.font  
roboto_medium com.example.aiagent.R.font  roboto_regular com.example.aiagent.R.font  AiAgentManager com.example.aiagent.core  CoroutineScope com.example.aiagent.core  Dispatchers com.example.aiagent.core  Int com.example.aiagent.core  Long com.example.aiagent.core  
SupervisorJob com.example.aiagent.core  UserSettings com.example.aiagent.core  VideoProject com.example.aiagent.core  ApplicationContext 'com.example.aiagent.core.AiAgentManager  Context 'com.example.aiagent.core.AiAgentManager  CoroutineScope 'com.example.aiagent.core.AiAgentManager  Dispatchers 'com.example.aiagent.core.AiAgentManager  FirebaseVideoRepository 'com.example.aiagent.core.AiAgentManager  GeminiRepository 'com.example.aiagent.core.AiAgentManager  Inject 'com.example.aiagent.core.AiAgentManager  Int 'com.example.aiagent.core.AiAgentManager  Long 'com.example.aiagent.core.AiAgentManager  NotificationHelper 'com.example.aiagent.core.AiAgentManager  SchedulerManager 'com.example.aiagent.core.AiAgentManager  SmartContentManager 'com.example.aiagent.core.AiAgentManager  
SupervisorJob 'com.example.aiagent.core.AiAgentManager  UserSettings 'com.example.aiagent.core.AiAgentManager  VideoProject 'com.example.aiagent.core.AiAgentManager  VideoProjectRepository 'com.example.aiagent.core.AiAgentManager  ApplicationContext 1com.example.aiagent.core.AiAgentManager.Companion  Context 1com.example.aiagent.core.AiAgentManager.Companion  CoroutineScope 1com.example.aiagent.core.AiAgentManager.Companion  Dispatchers 1com.example.aiagent.core.AiAgentManager.Companion  FirebaseVideoRepository 1com.example.aiagent.core.AiAgentManager.Companion  GeminiRepository 1com.example.aiagent.core.AiAgentManager.Companion  Inject 1com.example.aiagent.core.AiAgentManager.Companion  Int 1com.example.aiagent.core.AiAgentManager.Companion  Long 1com.example.aiagent.core.AiAgentManager.Companion  NotificationHelper 1com.example.aiagent.core.AiAgentManager.Companion  SchedulerManager 1com.example.aiagent.core.AiAgentManager.Companion  SmartContentManager 1com.example.aiagent.core.AiAgentManager.Companion  
SupervisorJob 1com.example.aiagent.core.AiAgentManager.Companion  UserSettings 1com.example.aiagent.core.AiAgentManager.Companion  VideoProject 1com.example.aiagent.core.AiAgentManager.Companion  VideoProjectRepository 1com.example.aiagent.core.AiAgentManager.Companion  Body com.example.aiagent.data.api  Boolean com.example.aiagent.data.api  GET com.example.aiagent.data.api  GeminiApiService com.example.aiagent.data.api  Header com.example.aiagent.data.api  Int com.example.aiagent.data.api  Json com.example.aiagent.data.api  List com.example.aiagent.data.api  	Multipart com.example.aiagent.data.api  POST com.example.aiagent.data.api  PUT com.example.aiagent.data.api  Part com.example.aiagent.data.api  Query com.example.aiagent.data.api  Result com.example.aiagent.data.api  String com.example.aiagent.data.api  VideoContentResponse com.example.aiagent.data.api  YouTubeApiService com.example.aiagent.data.api  YouTubeChannelListResponse com.example.aiagent.data.api  YouTubeChannelResponse com.example.aiagent.data.api  YouTubeChannelSnippet com.example.aiagent.data.api  YouTubeChannelStatistics com.example.aiagent.data.api  YouTubeSearchItem com.example.aiagent.data.api  YouTubeSearchResponse com.example.aiagent.data.api  YouTubeThumbnail com.example.aiagent.data.api  YouTubeThumbnails com.example.aiagent.data.api  YouTubeVideoId com.example.aiagent.data.api  YouTubeVideoListResponse com.example.aiagent.data.api  YouTubeVideoResponse com.example.aiagent.data.api  YouTubeVideoSnippet com.example.aiagent.data.api  YouTubeVideoStatus com.example.aiagent.data.api  YouTubeVideoUpdateRequest com.example.aiagent.data.api  Context -com.example.aiagent.data.api.GeminiApiService  GenerativeModel -com.example.aiagent.data.api.GeminiApiService  Inject -com.example.aiagent.data.api.GeminiApiService  Int -com.example.aiagent.data.api.GeminiApiService  Json -com.example.aiagent.data.api.GeminiApiService  List -com.example.aiagent.data.api.GeminiApiService  Result -com.example.aiagent.data.api.GeminiApiService  String -com.example.aiagent.data.api.GeminiApiService  VideoContentResponse -com.example.aiagent.data.api.GeminiApiService  invoke -com.example.aiagent.data.api.GeminiApiService  List 1com.example.aiagent.data.api.VideoContentResponse  String 1com.example.aiagent.data.api.VideoContentResponse  List ;com.example.aiagent.data.api.VideoContentResponse.Companion  String ;com.example.aiagent.data.api.VideoContentResponse.Companion  Body .com.example.aiagent.data.api.YouTubeApiService  Boolean .com.example.aiagent.data.api.YouTubeApiService  GET .com.example.aiagent.data.api.YouTubeApiService  Header .com.example.aiagent.data.api.YouTubeApiService  Int .com.example.aiagent.data.api.YouTubeApiService  	Multipart .com.example.aiagent.data.api.YouTubeApiService  
MultipartBody .com.example.aiagent.data.api.YouTubeApiService  POST .com.example.aiagent.data.api.YouTubeApiService  PUT .com.example.aiagent.data.api.YouTubeApiService  Part .com.example.aiagent.data.api.YouTubeApiService  Query .com.example.aiagent.data.api.YouTubeApiService  RequestBody .com.example.aiagent.data.api.YouTubeApiService  Response .com.example.aiagent.data.api.YouTubeApiService  String .com.example.aiagent.data.api.YouTubeApiService  YouTubeChannelListResponse .com.example.aiagent.data.api.YouTubeApiService  YouTubeSearchResponse .com.example.aiagent.data.api.YouTubeApiService  YouTubeVideoListResponse .com.example.aiagent.data.api.YouTubeApiService  YouTubeVideoResponse .com.example.aiagent.data.api.YouTubeApiService  YouTubeVideoUpdateRequest .com.example.aiagent.data.api.YouTubeApiService  List 7com.example.aiagent.data.api.YouTubeChannelListResponse  YouTubeChannelResponse 7com.example.aiagent.data.api.YouTubeChannelListResponse  String 3com.example.aiagent.data.api.YouTubeChannelResponse  YouTubeChannelSnippet 3com.example.aiagent.data.api.YouTubeChannelResponse  YouTubeChannelStatistics 3com.example.aiagent.data.api.YouTubeChannelResponse  String 2com.example.aiagent.data.api.YouTubeChannelSnippet  YouTubeThumbnails 2com.example.aiagent.data.api.YouTubeChannelSnippet  String 5com.example.aiagent.data.api.YouTubeChannelStatistics  YouTubeVideoId .com.example.aiagent.data.api.YouTubeSearchItem  YouTubeVideoSnippet .com.example.aiagent.data.api.YouTubeSearchItem  List 2com.example.aiagent.data.api.YouTubeSearchResponse  YouTubeSearchItem 2com.example.aiagent.data.api.YouTubeSearchResponse  Int -com.example.aiagent.data.api.YouTubeThumbnail  String -com.example.aiagent.data.api.YouTubeThumbnail  YouTubeThumbnail .com.example.aiagent.data.api.YouTubeThumbnails  String +com.example.aiagent.data.api.YouTubeVideoId  List 5com.example.aiagent.data.api.YouTubeVideoListResponse  YouTubeVideoResponse 5com.example.aiagent.data.api.YouTubeVideoListResponse  String 1com.example.aiagent.data.api.YouTubeVideoResponse  YouTubeVideoSnippet 1com.example.aiagent.data.api.YouTubeVideoResponse  YouTubeVideoStatus 1com.example.aiagent.data.api.YouTubeVideoResponse  List 0com.example.aiagent.data.api.YouTubeVideoSnippet  String 0com.example.aiagent.data.api.YouTubeVideoSnippet  Boolean /com.example.aiagent.data.api.YouTubeVideoStatus  String /com.example.aiagent.data.api.YouTubeVideoStatus  String 6com.example.aiagent.data.api.YouTubeVideoUpdateRequest  YouTubeVideoSnippet 6com.example.aiagent.data.api.YouTubeVideoUpdateRequest  YouTubeVideoStatus 6com.example.aiagent.data.api.YouTubeVideoUpdateRequest  AiAgentDatabase !com.example.aiagent.data.database  
AppStatistics !com.example.aiagent.data.database  AppStatisticsDao !com.example.aiagent.data.database  Boolean !com.example.aiagent.data.database  
Converters !com.example.aiagent.data.database  Dao !com.example.aiagent.data.database  Delete !com.example.aiagent.data.database  Double !com.example.aiagent.data.database  Insert !com.example.aiagent.data.database  Int !com.example.aiagent.data.database  Json !com.example.aiagent.data.database  List !com.example.aiagent.data.database  Long !com.example.aiagent.data.database  OnConflictStrategy !com.example.aiagent.data.database  OperationLog !com.example.aiagent.data.database  OperationLogDao !com.example.aiagent.data.database  Query !com.example.aiagent.data.database  SingletonComponent !com.example.aiagent.data.database  String !com.example.aiagent.data.database  Update !com.example.aiagent.data.database  UploadSchedule !com.example.aiagent.data.database  UserSettings !com.example.aiagent.data.database  UserSettingsDao !com.example.aiagent.data.database  VideoProject !com.example.aiagent.data.database  VideoProjectDao !com.example.aiagent.data.database  VideoQuality !com.example.aiagent.data.database  VideoStatus !com.example.aiagent.data.database  Volatile !com.example.aiagent.data.database  WatermarkStyle !com.example.aiagent.data.database  com !com.example.aiagent.data.database  AiAgentDatabase 1com.example.aiagent.data.database.AiAgentDatabase  AppStatisticsDao 1com.example.aiagent.data.database.AiAgentDatabase  Context 1com.example.aiagent.data.database.AiAgentDatabase  OperationLogDao 1com.example.aiagent.data.database.AiAgentDatabase  UserSettingsDao 1com.example.aiagent.data.database.AiAgentDatabase  VideoProjectDao 1com.example.aiagent.data.database.AiAgentDatabase  Volatile 1com.example.aiagent.data.database.AiAgentDatabase  AiAgentDatabase ;com.example.aiagent.data.database.AiAgentDatabase.Companion  AppStatisticsDao ;com.example.aiagent.data.database.AiAgentDatabase.Companion  Context ;com.example.aiagent.data.database.AiAgentDatabase.Companion  OperationLogDao ;com.example.aiagent.data.database.AiAgentDatabase.Companion  UserSettingsDao ;com.example.aiagent.data.database.AiAgentDatabase.Companion  VideoProjectDao ;com.example.aiagent.data.database.AiAgentDatabase.Companion  Volatile ;com.example.aiagent.data.database.AiAgentDatabase.Companion  Flow 2com.example.aiagent.data.database.AppStatisticsDao  Insert 2com.example.aiagent.data.database.AppStatisticsDao  Long 2com.example.aiagent.data.database.AppStatisticsDao  OnConflictStrategy 2com.example.aiagent.data.database.AppStatisticsDao  Query 2com.example.aiagent.data.database.AppStatisticsDao  Update 2com.example.aiagent.data.database.AppStatisticsDao  com 2com.example.aiagent.data.database.AppStatisticsDao  Json ,com.example.aiagent.data.database.Converters  List ,com.example.aiagent.data.database.Converters  String ,com.example.aiagent.data.database.Converters  
TypeConverter ,com.example.aiagent.data.database.Converters  UploadSchedule ,com.example.aiagent.data.database.Converters  VideoQuality ,com.example.aiagent.data.database.Converters  VideoStatus ,com.example.aiagent.data.database.Converters  WatermarkStyle ,com.example.aiagent.data.database.Converters  invoke ,com.example.aiagent.data.database.Converters  Flow 1com.example.aiagent.data.database.OperationLogDao  Insert 1com.example.aiagent.data.database.OperationLogDao  Int 1com.example.aiagent.data.database.OperationLogDao  List 1com.example.aiagent.data.database.OperationLogDao  Long 1com.example.aiagent.data.database.OperationLogDao  Query 1com.example.aiagent.data.database.OperationLogDao  String 1com.example.aiagent.data.database.OperationLogDao  com 1com.example.aiagent.data.database.OperationLogDao  Boolean 1com.example.aiagent.data.database.UserSettingsDao  Flow 1com.example.aiagent.data.database.UserSettingsDao  Insert 1com.example.aiagent.data.database.UserSettingsDao  OnConflictStrategy 1com.example.aiagent.data.database.UserSettingsDao  Query 1com.example.aiagent.data.database.UserSettingsDao  String 1com.example.aiagent.data.database.UserSettingsDao  Update 1com.example.aiagent.data.database.UserSettingsDao  UserSettings 1com.example.aiagent.data.database.UserSettingsDao  Delete 1com.example.aiagent.data.database.VideoProjectDao  Double 1com.example.aiagent.data.database.VideoProjectDao  Flow 1com.example.aiagent.data.database.VideoProjectDao  Insert 1com.example.aiagent.data.database.VideoProjectDao  Int 1com.example.aiagent.data.database.VideoProjectDao  List 1com.example.aiagent.data.database.VideoProjectDao  Long 1com.example.aiagent.data.database.VideoProjectDao  OnConflictStrategy 1com.example.aiagent.data.database.VideoProjectDao  Query 1com.example.aiagent.data.database.VideoProjectDao  String 1com.example.aiagent.data.database.VideoProjectDao  Update 1com.example.aiagent.data.database.VideoProjectDao  VideoProject 1com.example.aiagent.data.database.VideoProjectDao  VideoStatus 1com.example.aiagent.data.database.VideoProjectDao  Boolean !com.example.aiagent.data.firebase  FirebaseFirestore !com.example.aiagent.data.firebase  
FirebaseVideo !com.example.aiagent.data.firebase  FirebaseVideoService !com.example.aiagent.data.firebase  Int !com.example.aiagent.data.firebase  List !com.example.aiagent.data.firebase  Result !com.example.aiagent.data.firebase  String !com.example.aiagent.data.firebase  Unit !com.example.aiagent.data.firebase  VideoSearchCriteria !com.example.aiagent.data.firebase  
VideoStats !com.example.aiagent.data.firebase  Boolean 6com.example.aiagent.data.firebase.FirebaseVideoService  FirebaseFirestore 6com.example.aiagent.data.firebase.FirebaseVideoService  
FirebaseVideo 6com.example.aiagent.data.firebase.FirebaseVideoService  Inject 6com.example.aiagent.data.firebase.FirebaseVideoService  Int 6com.example.aiagent.data.firebase.FirebaseVideoService  List 6com.example.aiagent.data.firebase.FirebaseVideoService  Result 6com.example.aiagent.data.firebase.FirebaseVideoService  String 6com.example.aiagent.data.firebase.FirebaseVideoService  Unit 6com.example.aiagent.data.firebase.FirebaseVideoService  VideoSearchCriteria 6com.example.aiagent.data.firebase.FirebaseVideoService  
VideoStats 6com.example.aiagent.data.firebase.FirebaseVideoService  	firestore 6com.example.aiagent.data.firebase.FirebaseVideoService  Boolean @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  FirebaseFirestore @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  
FirebaseVideo @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  Inject @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  Int @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  List @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  Result @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  String @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  Unit @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  VideoSearchCriteria @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  
VideoStats @com.example.aiagent.data.firebase.FirebaseVideoService.Companion  	AgeRating com.example.aiagent.data.model  
AppStatistics com.example.aiagent.data.model  Boolean com.example.aiagent.data.model  
Converters com.example.aiagent.data.model  CoroutineScope com.example.aiagent.data.model  Dispatchers com.example.aiagent.data.model  FirebaseFirestore com.example.aiagent.data.model  
FirebaseVideo com.example.aiagent.data.model  Float com.example.aiagent.data.model  Int com.example.aiagent.data.model  Json com.example.aiagent.data.model  List com.example.aiagent.data.model  Long com.example.aiagent.data.model  Map com.example.aiagent.data.model  OperationLog com.example.aiagent.data.model  Result com.example.aiagent.data.model  String com.example.aiagent.data.model  
SupervisorJob com.example.aiagent.data.model  UploadSchedule com.example.aiagent.data.model  UserSettings com.example.aiagent.data.model  
VideoCategory com.example.aiagent.data.model  	VideoMood com.example.aiagent.data.model  VideoProject com.example.aiagent.data.model  VideoQuality com.example.aiagent.data.model  VideoSearchCriteria com.example.aiagent.data.model  
VideoStats com.example.aiagent.data.model  VideoStatus com.example.aiagent.data.model  
VideoUsageLog com.example.aiagent.data.model  Volatile com.example.aiagent.data.model  WatermarkStyle com.example.aiagent.data.model  	Companion ,com.example.aiagent.data.model.AppStatistics  Int ,com.example.aiagent.data.model.AppStatistics  Long ,com.example.aiagent.data.model.AppStatistics  
PrimaryKey ,com.example.aiagent.data.model.AppStatistics  Int 6com.example.aiagent.data.model.AppStatistics.Companion  Long 6com.example.aiagent.data.model.AppStatistics.Companion  
PrimaryKey 6com.example.aiagent.data.model.AppStatistics.Companion  	AgeRating ,com.example.aiagent.data.model.FirebaseVideo  Boolean ,com.example.aiagent.data.model.FirebaseVideo  
DocumentId ,com.example.aiagent.data.model.FirebaseVideo  Float ,com.example.aiagent.data.model.FirebaseVideo  Int ,com.example.aiagent.data.model.FirebaseVideo  List ,com.example.aiagent.data.model.FirebaseVideo  Long ,com.example.aiagent.data.model.FirebaseVideo  PropertyName ,com.example.aiagent.data.model.FirebaseVideo  String ,com.example.aiagent.data.model.FirebaseVideo  
VideoCategory ,com.example.aiagent.data.model.FirebaseVideo  	VideoMood ,com.example.aiagent.data.model.FirebaseVideo  	AgeRating 6com.example.aiagent.data.model.FirebaseVideo.Companion  Boolean 6com.example.aiagent.data.model.FirebaseVideo.Companion  
DocumentId 6com.example.aiagent.data.model.FirebaseVideo.Companion  Float 6com.example.aiagent.data.model.FirebaseVideo.Companion  Int 6com.example.aiagent.data.model.FirebaseVideo.Companion  List 6com.example.aiagent.data.model.FirebaseVideo.Companion  Long 6com.example.aiagent.data.model.FirebaseVideo.Companion  PropertyName 6com.example.aiagent.data.model.FirebaseVideo.Companion  String 6com.example.aiagent.data.model.FirebaseVideo.Companion  
VideoCategory 6com.example.aiagent.data.model.FirebaseVideo.Companion  	VideoMood 6com.example.aiagent.data.model.FirebaseVideo.Companion  	Companion +com.example.aiagent.data.model.OperationLog  Long +com.example.aiagent.data.model.OperationLog  
PrimaryKey +com.example.aiagent.data.model.OperationLog  String +com.example.aiagent.data.model.OperationLog  Long 5com.example.aiagent.data.model.OperationLog.Companion  
PrimaryKey 5com.example.aiagent.data.model.OperationLog.Companion  String 5com.example.aiagent.data.model.OperationLog.Companion  Boolean -com.example.aiagent.data.model.UploadSchedule  Int -com.example.aiagent.data.model.UploadSchedule  String -com.example.aiagent.data.model.UploadSchedule  Boolean 7com.example.aiagent.data.model.UploadSchedule.Companion  Int 7com.example.aiagent.data.model.UploadSchedule.Companion  String 7com.example.aiagent.data.model.UploadSchedule.Companion  Boolean +com.example.aiagent.data.model.UserSettings  	Companion +com.example.aiagent.data.model.UserSettings  Int +com.example.aiagent.data.model.UserSettings  
PrimaryKey +com.example.aiagent.data.model.UserSettings  String +com.example.aiagent.data.model.UserSettings  UploadSchedule +com.example.aiagent.data.model.UserSettings  VideoQuality +com.example.aiagent.data.model.UserSettings  WatermarkStyle +com.example.aiagent.data.model.UserSettings  Boolean 5com.example.aiagent.data.model.UserSettings.Companion  Int 5com.example.aiagent.data.model.UserSettings.Companion  
PrimaryKey 5com.example.aiagent.data.model.UserSettings.Companion  String 5com.example.aiagent.data.model.UserSettings.Companion  UploadSchedule 5com.example.aiagent.data.model.UserSettings.Companion  VideoQuality 5com.example.aiagent.data.model.UserSettings.Companion  WatermarkStyle 5com.example.aiagent.data.model.UserSettings.Companion  	Companion +com.example.aiagent.data.model.VideoProject  Int +com.example.aiagent.data.model.VideoProject  List +com.example.aiagent.data.model.VideoProject  Long +com.example.aiagent.data.model.VideoProject  
PrimaryKey +com.example.aiagent.data.model.VideoProject  String +com.example.aiagent.data.model.VideoProject  VideoStatus +com.example.aiagent.data.model.VideoProject  Int 5com.example.aiagent.data.model.VideoProject.Companion  List 5com.example.aiagent.data.model.VideoProject.Companion  Long 5com.example.aiagent.data.model.VideoProject.Companion  
PrimaryKey 5com.example.aiagent.data.model.VideoProject.Companion  String 5com.example.aiagent.data.model.VideoProject.Companion  VideoStatus 5com.example.aiagent.data.model.VideoProject.Companion  Boolean 2com.example.aiagent.data.model.VideoSearchCriteria  Float 2com.example.aiagent.data.model.VideoSearchCriteria  Int 2com.example.aiagent.data.model.VideoSearchCriteria  List 2com.example.aiagent.data.model.VideoSearchCriteria  String 2com.example.aiagent.data.model.VideoSearchCriteria  
VideoCategory 2com.example.aiagent.data.model.VideoSearchCriteria  	VideoMood 2com.example.aiagent.data.model.VideoSearchCriteria  Boolean <com.example.aiagent.data.model.VideoSearchCriteria.Companion  Float <com.example.aiagent.data.model.VideoSearchCriteria.Companion  Int <com.example.aiagent.data.model.VideoSearchCriteria.Companion  List <com.example.aiagent.data.model.VideoSearchCriteria.Companion  String <com.example.aiagent.data.model.VideoSearchCriteria.Companion  
VideoCategory <com.example.aiagent.data.model.VideoSearchCriteria.Companion  	VideoMood <com.example.aiagent.data.model.VideoSearchCriteria.Companion  Float )com.example.aiagent.data.model.VideoStats  Int )com.example.aiagent.data.model.VideoStats  List )com.example.aiagent.data.model.VideoStats  Map )com.example.aiagent.data.model.VideoStats  String )com.example.aiagent.data.model.VideoStats  
VideoCategory )com.example.aiagent.data.model.VideoStats  	VideoMood )com.example.aiagent.data.model.VideoStats  Float 3com.example.aiagent.data.model.VideoStats.Companion  Int 3com.example.aiagent.data.model.VideoStats.Companion  List 3com.example.aiagent.data.model.VideoStats.Companion  Map 3com.example.aiagent.data.model.VideoStats.Companion  String 3com.example.aiagent.data.model.VideoStats.Companion  
VideoCategory 3com.example.aiagent.data.model.VideoStats.Companion  	VideoMood 3com.example.aiagent.data.model.VideoStats.Companion  Boolean ,com.example.aiagent.data.model.VideoUsageLog  
DocumentId ,com.example.aiagent.data.model.VideoUsageLog  Long ,com.example.aiagent.data.model.VideoUsageLog  PropertyName ,com.example.aiagent.data.model.VideoUsageLog  String ,com.example.aiagent.data.model.VideoUsageLog  Boolean 6com.example.aiagent.data.model.VideoUsageLog.Companion  
DocumentId 6com.example.aiagent.data.model.VideoUsageLog.Companion  Long 6com.example.aiagent.data.model.VideoUsageLog.Companion  PropertyName 6com.example.aiagent.data.model.VideoUsageLog.Companion  String 6com.example.aiagent.data.model.VideoUsageLog.Companion  Boolean #com.example.aiagent.data.repository  
FirebaseVideo #com.example.aiagent.data.repository  FirebaseVideoRepository #com.example.aiagent.data.repository  GeminiRepository #com.example.aiagent.data.repository  Int #com.example.aiagent.data.repository  List #com.example.aiagent.data.repository  Long #com.example.aiagent.data.repository  ProjectStatistics #com.example.aiagent.data.repository  Result #com.example.aiagent.data.repository  String #com.example.aiagent.data.repository  Unit #com.example.aiagent.data.repository  UserSettingsRepository #com.example.aiagent.data.repository  VideoProject #com.example.aiagent.data.repository  VideoProjectRepository #com.example.aiagent.data.repository  VideoSearchCriteria #com.example.aiagent.data.repository  
VideoStats #com.example.aiagent.data.repository  YouTubeAuthManager #com.example.aiagent.data.repository  YouTubeRepository #com.example.aiagent.data.repository  com #com.example.aiagent.data.repository  ApplicationContext ;com.example.aiagent.data.repository.FirebaseVideoRepository  Context ;com.example.aiagent.data.repository.FirebaseVideoRepository  
FirebaseVideo ;com.example.aiagent.data.repository.FirebaseVideoRepository  FirebaseVideoService ;com.example.aiagent.data.repository.FirebaseVideoRepository  Inject ;com.example.aiagent.data.repository.FirebaseVideoRepository  Int ;com.example.aiagent.data.repository.FirebaseVideoRepository  List ;com.example.aiagent.data.repository.FirebaseVideoRepository  Long ;com.example.aiagent.data.repository.FirebaseVideoRepository  Result ;com.example.aiagent.data.repository.FirebaseVideoRepository  String ;com.example.aiagent.data.repository.FirebaseVideoRepository  Unit ;com.example.aiagent.data.repository.FirebaseVideoRepository  VideoDownloader ;com.example.aiagent.data.repository.FirebaseVideoRepository  VideoProject ;com.example.aiagent.data.repository.FirebaseVideoRepository  VideoSearchCriteria ;com.example.aiagent.data.repository.FirebaseVideoRepository  
VideoStats ;com.example.aiagent.data.repository.FirebaseVideoRepository  ApplicationContext Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  Context Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  
FirebaseVideo Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  FirebaseVideoService Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  Inject Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  Int Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  List Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  Long Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  Result Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  String Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  Unit Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  VideoDownloader Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  VideoProject Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  VideoSearchCriteria Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  
VideoStats Ecom.example.aiagent.data.repository.FirebaseVideoRepository.Companion  Boolean 4com.example.aiagent.data.repository.GeminiRepository  GeminiApiService 4com.example.aiagent.data.repository.GeminiRepository  Inject 4com.example.aiagent.data.repository.GeminiRepository  Int 4com.example.aiagent.data.repository.GeminiRepository  List 4com.example.aiagent.data.repository.GeminiRepository  Result 4com.example.aiagent.data.repository.GeminiRepository  String 4com.example.aiagent.data.repository.GeminiRepository  VideoContentResponse 4com.example.aiagent.data.repository.GeminiRepository  Boolean >com.example.aiagent.data.repository.GeminiRepository.Companion  GeminiApiService >com.example.aiagent.data.repository.GeminiRepository.Companion  Inject >com.example.aiagent.data.repository.GeminiRepository.Companion  Int >com.example.aiagent.data.repository.GeminiRepository.Companion  List >com.example.aiagent.data.repository.GeminiRepository.Companion  Result >com.example.aiagent.data.repository.GeminiRepository.Companion  String >com.example.aiagent.data.repository.GeminiRepository.Companion  VideoContentResponse >com.example.aiagent.data.repository.GeminiRepository.Companion  Int 5com.example.aiagent.data.repository.ProjectStatistics  Boolean :com.example.aiagent.data.repository.UserSettingsRepository  Flow :com.example.aiagent.data.repository.UserSettingsRepository  Inject :com.example.aiagent.data.repository.UserSettingsRepository  Result :com.example.aiagent.data.repository.UserSettingsRepository  String :com.example.aiagent.data.repository.UserSettingsRepository  Unit :com.example.aiagent.data.repository.UserSettingsRepository  UploadSchedule :com.example.aiagent.data.repository.UserSettingsRepository  UserSettings :com.example.aiagent.data.repository.UserSettingsRepository  UserSettingsDao :com.example.aiagent.data.repository.UserSettingsRepository  VideoQuality :com.example.aiagent.data.repository.UserSettingsRepository  WatermarkStyle :com.example.aiagent.data.repository.UserSettingsRepository  Flow :com.example.aiagent.data.repository.VideoProjectRepository  Inject :com.example.aiagent.data.repository.VideoProjectRepository  Int :com.example.aiagent.data.repository.VideoProjectRepository  List :com.example.aiagent.data.repository.VideoProjectRepository  Long :com.example.aiagent.data.repository.VideoProjectRepository  OperationLogDao :com.example.aiagent.data.repository.VideoProjectRepository  ProjectStatistics :com.example.aiagent.data.repository.VideoProjectRepository  Result :com.example.aiagent.data.repository.VideoProjectRepository  String :com.example.aiagent.data.repository.VideoProjectRepository  Unit :com.example.aiagent.data.repository.VideoProjectRepository  VideoProject :com.example.aiagent.data.repository.VideoProjectRepository  VideoProjectDao :com.example.aiagent.data.repository.VideoProjectRepository  VideoStatus :com.example.aiagent.data.repository.VideoProjectRepository  ApplicationContext 5com.example.aiagent.data.repository.YouTubeRepository  Boolean 5com.example.aiagent.data.repository.YouTubeRepository  Context 5com.example.aiagent.data.repository.YouTubeRepository  File 5com.example.aiagent.data.repository.YouTubeRepository  Inject 5com.example.aiagent.data.repository.YouTubeRepository  Int 5com.example.aiagent.data.repository.YouTubeRepository  List 5com.example.aiagent.data.repository.YouTubeRepository  Result 5com.example.aiagent.data.repository.YouTubeRepository  String 5com.example.aiagent.data.repository.YouTubeRepository  Unit 5com.example.aiagent.data.repository.YouTubeRepository  VideoProject 5com.example.aiagent.data.repository.YouTubeRepository  YouTubeApiService 5com.example.aiagent.data.repository.YouTubeRepository  YouTubeAuthManager 5com.example.aiagent.data.repository.YouTubeRepository  com 5com.example.aiagent.data.repository.YouTubeRepository  context 5com.example.aiagent.data.repository.YouTubeRepository  invoke 5com.example.aiagent.data.repository.YouTubeRepository  ApplicationContext ?com.example.aiagent.data.repository.YouTubeRepository.Companion  Boolean ?com.example.aiagent.data.repository.YouTubeRepository.Companion  Context ?com.example.aiagent.data.repository.YouTubeRepository.Companion  File ?com.example.aiagent.data.repository.YouTubeRepository.Companion  Inject ?com.example.aiagent.data.repository.YouTubeRepository.Companion  Int ?com.example.aiagent.data.repository.YouTubeRepository.Companion  List ?com.example.aiagent.data.repository.YouTubeRepository.Companion  Result ?com.example.aiagent.data.repository.YouTubeRepository.Companion  String ?com.example.aiagent.data.repository.YouTubeRepository.Companion  Unit ?com.example.aiagent.data.repository.YouTubeRepository.Companion  VideoProject ?com.example.aiagent.data.repository.YouTubeRepository.Companion  YouTubeApiService ?com.example.aiagent.data.repository.YouTubeRepository.Companion  YouTubeAuthManager ?com.example.aiagent.data.repository.YouTubeRepository.Companion  com ?com.example.aiagent.data.repository.YouTubeRepository.Companion  invoke ?com.example.aiagent.data.repository.YouTubeRepository.Companion  AiAgentDatabase com.example.aiagent.di  AppStatisticsDao com.example.aiagent.di  DatabaseModule com.example.aiagent.di  
NetworkModule com.example.aiagent.di  OperationLogDao com.example.aiagent.di  SingletonComponent com.example.aiagent.di  UserSettingsDao com.example.aiagent.di  VideoProjectDao com.example.aiagent.di  com com.example.aiagent.di  AiAgentDatabase %com.example.aiagent.di.DatabaseModule  AppStatisticsDao %com.example.aiagent.di.DatabaseModule  ApplicationContext %com.example.aiagent.di.DatabaseModule  Context %com.example.aiagent.di.DatabaseModule  OperationLogDao %com.example.aiagent.di.DatabaseModule  Provides %com.example.aiagent.di.DatabaseModule  	Singleton %com.example.aiagent.di.DatabaseModule  UserSettingsDao %com.example.aiagent.di.DatabaseModule  VideoProjectDao %com.example.aiagent.di.DatabaseModule  AiAgentManager $com.example.aiagent.di.NetworkModule  ApplicationContext $com.example.aiagent.di.NetworkModule  Context $com.example.aiagent.di.NetworkModule  FirebaseVideoRepository $com.example.aiagent.di.NetworkModule  FirebaseVideoService $com.example.aiagent.di.NetworkModule  GeminiApiService $com.example.aiagent.di.NetworkModule  GeminiRepository $com.example.aiagent.di.NetworkModule  NotificationHelper $com.example.aiagent.di.NetworkModule  OkHttpClient $com.example.aiagent.di.NetworkModule  Provides $com.example.aiagent.di.NetworkModule  Retrofit $com.example.aiagent.di.NetworkModule  SchedulerManager $com.example.aiagent.di.NetworkModule  	Singleton $com.example.aiagent.di.NetworkModule  SmartContentManager $com.example.aiagent.di.NetworkModule  UserSettingsRepository $com.example.aiagent.di.NetworkModule  VideoDownloader $com.example.aiagent.di.NetworkModule  YouTubeApiService $com.example.aiagent.di.NetworkModule  YouTubeRepository $com.example.aiagent.di.NetworkModule  YouTubeSetupManager $com.example.aiagent.di.NetworkModule  com $com.example.aiagent.di.NetworkModule  
AlarmReceiver com.example.aiagent.receiver  BootReceiver com.example.aiagent.receiver  String com.example.aiagent.receiver  Context *com.example.aiagent.receiver.AlarmReceiver  Intent *com.example.aiagent.receiver.AlarmReceiver  String *com.example.aiagent.receiver.AlarmReceiver  Context 4com.example.aiagent.receiver.AlarmReceiver.Companion  Intent 4com.example.aiagent.receiver.AlarmReceiver.Companion  String 4com.example.aiagent.receiver.AlarmReceiver.Companion  Context )com.example.aiagent.receiver.BootReceiver  Inject )com.example.aiagent.receiver.BootReceiver  Intent )com.example.aiagent.receiver.BootReceiver  SchedulerManager )com.example.aiagent.receiver.BootReceiver  Int com.example.aiagent.service  String com.example.aiagent.service  VideoProcessingService com.example.aiagent.service  YouTubeUploadService com.example.aiagent.service  Context 2com.example.aiagent.service.VideoProcessingService  IBinder 2com.example.aiagent.service.VideoProcessingService  Inject 2com.example.aiagent.service.VideoProcessingService  Int 2com.example.aiagent.service.VideoProcessingService  Intent 2com.example.aiagent.service.VideoProcessingService  Notification 2com.example.aiagent.service.VideoProcessingService  NotificationHelper 2com.example.aiagent.service.VideoProcessingService  String 2com.example.aiagent.service.VideoProcessingService  VideoProcessor 2com.example.aiagent.service.VideoProcessingService  VideoProjectRepository 2com.example.aiagent.service.VideoProcessingService  Context <com.example.aiagent.service.VideoProcessingService.Companion  IBinder <com.example.aiagent.service.VideoProcessingService.Companion  Inject <com.example.aiagent.service.VideoProcessingService.Companion  Int <com.example.aiagent.service.VideoProcessingService.Companion  Intent <com.example.aiagent.service.VideoProcessingService.Companion  Notification <com.example.aiagent.service.VideoProcessingService.Companion  NotificationHelper <com.example.aiagent.service.VideoProcessingService.Companion  String <com.example.aiagent.service.VideoProcessingService.Companion  VideoProcessor <com.example.aiagent.service.VideoProcessingService.Companion  VideoProjectRepository <com.example.aiagent.service.VideoProcessingService.Companion  Context 0com.example.aiagent.service.YouTubeUploadService  IBinder 0com.example.aiagent.service.YouTubeUploadService  Inject 0com.example.aiagent.service.YouTubeUploadService  Int 0com.example.aiagent.service.YouTubeUploadService  Intent 0com.example.aiagent.service.YouTubeUploadService  Notification 0com.example.aiagent.service.YouTubeUploadService  NotificationHelper 0com.example.aiagent.service.YouTubeUploadService  String 0com.example.aiagent.service.YouTubeUploadService  VideoProjectRepository 0com.example.aiagent.service.YouTubeUploadService  YouTubeRepository 0com.example.aiagent.service.YouTubeUploadService  Context :com.example.aiagent.service.YouTubeUploadService.Companion  IBinder :com.example.aiagent.service.YouTubeUploadService.Companion  Inject :com.example.aiagent.service.YouTubeUploadService.Companion  Int :com.example.aiagent.service.YouTubeUploadService.Companion  Intent :com.example.aiagent.service.YouTubeUploadService.Companion  Notification :com.example.aiagent.service.YouTubeUploadService.Companion  NotificationHelper :com.example.aiagent.service.YouTubeUploadService.Companion  String :com.example.aiagent.service.YouTubeUploadService.Companion  VideoProjectRepository :com.example.aiagent.service.YouTubeUploadService.Companion  YouTubeRepository :com.example.aiagent.service.YouTubeUploadService.Companion  
AiAgentApp com.example.aiagent.ui  	Analytics com.example.aiagent.ui  
BottomNavItem com.example.aiagent.ui  
Composable com.example.aiagent.ui  	Dashboard com.example.aiagent.ui  ExperimentalMaterial3Api com.example.aiagent.ui  MainActivity com.example.aiagent.ui  OptIn com.example.aiagent.ui  Settings com.example.aiagent.ui  String com.example.aiagent.ui  VideoLibrary com.example.aiagent.ui  bottomNavItems com.example.aiagent.ui  listOf com.example.aiagent.ui  ImageVector $com.example.aiagent.ui.BottomNavItem  String $com.example.aiagent.ui.BottomNavItem  AgentStatusSection !com.example.aiagent.ui.components  Boolean !com.example.aiagent.ui.components  
Composable !com.example.aiagent.ui.components  CurrentTaskCard !com.example.aiagent.ui.components  EmptyStateCard !com.example.aiagent.ui.components  ExperimentalMaterial3Api !com.example.aiagent.ui.components  List !com.example.aiagent.ui.components  Long !com.example.aiagent.ui.components  NextUploadCard !com.example.aiagent.ui.components  OptIn !com.example.aiagent.ui.components  ProjectActivityCard !com.example.aiagent.ui.components  QuickAction !com.example.aiagent.ui.components  QuickActionCard !com.example.aiagent.ui.components  QuickActionsSection !com.example.aiagent.ui.components  RecentActivitySection !com.example.aiagent.ui.components  SettingsSection !com.example.aiagent.ui.components  
StatusIcon !com.example.aiagent.ui.components  StatusIndicator !com.example.aiagent.ui.components  String !com.example.aiagent.ui.components  Unit !com.example.aiagent.ui.components  androidx !com.example.aiagent.ui.components  
formatDate !com.example.aiagent.ui.components  getStatusColor !com.example.aiagent.ui.components  
getStatusText !com.example.aiagent.ui.components  ImageVector -com.example.aiagent.ui.components.QuickAction  String -com.example.aiagent.ui.components.QuickAction  Unit -com.example.aiagent.ui.components.QuickAction  androidx -com.example.aiagent.ui.components.QuickAction  AISettingsCard com.example.aiagent.ui.screens  	Analytics com.example.aiagent.ui.screens  AnalyticsScreen com.example.aiagent.ui.screens  AppSettingsCard com.example.aiagent.ui.screens  Boolean com.example.aiagent.ui.screens  ChannelSettingsCard com.example.aiagent.ui.screens  
Composable com.example.aiagent.ui.screens  	Dashboard com.example.aiagent.ui.screens  DashboardHeader com.example.aiagent.ui.screens  DashboardScreen com.example.aiagent.ui.screens  ExperimentalMaterial3Api com.example.aiagent.ui.screens  Float com.example.aiagent.ui.screens  Int com.example.aiagent.ui.screens  OptIn com.example.aiagent.ui.screens  ProjectsScreen com.example.aiagent.ui.screens  QuickStatsSection com.example.aiagent.ui.screens  Settings com.example.aiagent.ui.screens  SettingsScreen com.example.aiagent.ui.screens  StatCard com.example.aiagent.ui.screens  StatCardItem com.example.aiagent.ui.screens  String com.example.aiagent.ui.screens  Unit com.example.aiagent.ui.screens  UploadSettingsCard com.example.aiagent.ui.screens  VideoLibrary com.example.aiagent.ui.screens  YouTubeSettingsCard com.example.aiagent.ui.screens  listOf com.example.aiagent.ui.screens  Color 'com.example.aiagent.ui.screens.StatCard  ImageVector 'com.example.aiagent.ui.screens.StatCard  String 'com.example.aiagent.ui.screens.StatCard  
AccentBlue com.example.aiagent.ui.theme  AccentGreen com.example.aiagent.ui.theme  AccentOrange com.example.aiagent.ui.theme  AccentPurple com.example.aiagent.ui.theme  AiAgentTheme com.example.aiagent.ui.theme  ArabicFontFamily com.example.aiagent.ui.theme  BackgroundDark com.example.aiagent.ui.theme  BackgroundLight com.example.aiagent.ui.theme  Boolean com.example.aiagent.ui.theme  ButtonDisabled com.example.aiagent.ui.theme  
ButtonPrimary com.example.aiagent.ui.theme  ButtonSecondary com.example.aiagent.ui.theme  CardBackground com.example.aiagent.ui.theme  CardBackgroundDark com.example.aiagent.ui.theme  
CardElevation com.example.aiagent.ui.theme  
Composable com.example.aiagent.ui.theme  DarkColorScheme com.example.aiagent.ui.theme  DarkGray com.example.aiagent.ui.theme  ErrorRed com.example.aiagent.ui.theme  ExperimentalMaterial3Api com.example.aiagent.ui.theme  GradientEnd com.example.aiagent.ui.theme  
GradientStart com.example.aiagent.ui.theme  InfoBlue com.example.aiagent.ui.theme  LightColorScheme com.example.aiagent.ui.theme  	LightGray com.example.aiagent.ui.theme  
ModernBlue com.example.aiagent.ui.theme  ModernBlueDark com.example.aiagent.ui.theme  ModernBlueLight com.example.aiagent.ui.theme  NeutralGray com.example.aiagent.ui.theme  ProgressBackground com.example.aiagent.ui.theme  ProgressForeground com.example.aiagent.ui.theme  R com.example.aiagent.ui.theme  RobotoFontFamily com.example.aiagent.ui.theme  SuccessGreen com.example.aiagent.ui.theme  SurfaceDark com.example.aiagent.ui.theme  SurfaceLight com.example.aiagent.ui.theme  
TextOnDark com.example.aiagent.ui.theme  TextOnLight com.example.aiagent.ui.theme  TextPrimary com.example.aiagent.ui.theme  
TextSecondary com.example.aiagent.ui.theme  
Typography com.example.aiagent.ui.theme  Unit com.example.aiagent.ui.theme  
WarningOrange com.example.aiagent.ui.theme  
YouTubeRed com.example.aiagent.ui.theme  YouTubeRedDark com.example.aiagent.ui.theme  YouTubeRedLight com.example.aiagent.ui.theme  androidx com.example.aiagent.ui.theme  Boolean  com.example.aiagent.ui.viewmodel  DashboardUiState  com.example.aiagent.ui.viewmodel  DashboardViewModel  com.example.aiagent.ui.viewmodel  Float  com.example.aiagent.ui.viewmodel  Int  com.example.aiagent.ui.viewmodel  List  com.example.aiagent.ui.viewmodel  Long  com.example.aiagent.ui.viewmodel  MutableStateFlow  com.example.aiagent.ui.viewmodel  SettingsUiState  com.example.aiagent.ui.viewmodel  SettingsViewModel  com.example.aiagent.ui.viewmodel  	StateFlow  com.example.aiagent.ui.viewmodel  String  com.example.aiagent.ui.viewmodel  asStateFlow  com.example.aiagent.ui.viewmodel  Boolean 1com.example.aiagent.ui.viewmodel.DashboardUiState  Float 1com.example.aiagent.ui.viewmodel.DashboardUiState  Int 1com.example.aiagent.ui.viewmodel.DashboardUiState  List 1com.example.aiagent.ui.viewmodel.DashboardUiState  Long 1com.example.aiagent.ui.viewmodel.DashboardUiState  String 1com.example.aiagent.ui.viewmodel.DashboardUiState  VideoProject 1com.example.aiagent.ui.viewmodel.DashboardUiState  AiAgentManager 3com.example.aiagent.ui.viewmodel.DashboardViewModel  DashboardUiState 3com.example.aiagent.ui.viewmodel.DashboardViewModel  Inject 3com.example.aiagent.ui.viewmodel.DashboardViewModel  Long 3com.example.aiagent.ui.viewmodel.DashboardViewModel  MutableStateFlow 3com.example.aiagent.ui.viewmodel.DashboardViewModel  	StateFlow 3com.example.aiagent.ui.viewmodel.DashboardViewModel  String 3com.example.aiagent.ui.viewmodel.DashboardViewModel  UserSettingsRepository 3com.example.aiagent.ui.viewmodel.DashboardViewModel  VideoProjectRepository 3com.example.aiagent.ui.viewmodel.DashboardViewModel  _uiState 3com.example.aiagent.ui.viewmodel.DashboardViewModel  asStateFlow 3com.example.aiagent.ui.viewmodel.DashboardViewModel  getASStateFlow 3com.example.aiagent.ui.viewmodel.DashboardViewModel  getAsStateFlow 3com.example.aiagent.ui.viewmodel.DashboardViewModel  Boolean 0com.example.aiagent.ui.viewmodel.SettingsUiState  Int 0com.example.aiagent.ui.viewmodel.SettingsUiState  String 0com.example.aiagent.ui.viewmodel.SettingsUiState  Boolean 2com.example.aiagent.ui.viewmodel.SettingsViewModel  Inject 2com.example.aiagent.ui.viewmodel.SettingsViewModel  Int 2com.example.aiagent.ui.viewmodel.SettingsViewModel  MutableStateFlow 2com.example.aiagent.ui.viewmodel.SettingsViewModel  NotificationHelper 2com.example.aiagent.ui.viewmodel.SettingsViewModel  SettingsUiState 2com.example.aiagent.ui.viewmodel.SettingsViewModel  	StateFlow 2com.example.aiagent.ui.viewmodel.SettingsViewModel  String 2com.example.aiagent.ui.viewmodel.SettingsViewModel  UserSettingsRepository 2com.example.aiagent.ui.viewmodel.SettingsViewModel  YouTubeSetupManager 2com.example.aiagent.ui.viewmodel.SettingsViewModel  _uiState 2com.example.aiagent.ui.viewmodel.SettingsViewModel  asStateFlow 2com.example.aiagent.ui.viewmodel.SettingsViewModel  getASStateFlow 2com.example.aiagent.ui.viewmodel.SettingsViewModel  getAsStateFlow 2com.example.aiagent.ui.viewmodel.SettingsViewModel  Boolean com.example.aiagent.utils  ContentContext com.example.aiagent.utils  Context com.example.aiagent.utils  FirebaseInitializer com.example.aiagent.utils  
FirebaseVideo com.example.aiagent.utils  Int com.example.aiagent.utils  List com.example.aiagent.utils  Long com.example.aiagent.utils  NotificationHelper com.example.aiagent.utils  ProgressRequestBody com.example.aiagent.utils  RemoteVideoInfo com.example.aiagent.utils  Result com.example.aiagent.utils  SchedulerManager com.example.aiagent.utils  ServiceAccountInfo com.example.aiagent.utils  SmartContentManager com.example.aiagent.utils  String com.example.aiagent.utils  Unit com.example.aiagent.utils  UserSettings com.example.aiagent.utils  VideoDownloader com.example.aiagent.utils  	VideoInfo com.example.aiagent.utils  VideoProcessor com.example.aiagent.utils  VideoQuality com.example.aiagent.utils  WorkManager com.example.aiagent.utils  YouTubeAuthManager com.example.aiagent.utils  YouTubeSetupManager com.example.aiagent.utils  YouTubeSetupStatus com.example.aiagent.utils  listOf com.example.aiagent.utils  Boolean (com.example.aiagent.utils.ContentContext  Int (com.example.aiagent.utils.ContentContext  String (com.example.aiagent.utils.ContentContext  ApplicationContext -com.example.aiagent.utils.FirebaseInitializer  Boolean -com.example.aiagent.utils.FirebaseInitializer  Context -com.example.aiagent.utils.FirebaseInitializer  Inject -com.example.aiagent.utils.FirebaseInitializer  ApplicationContext 7com.example.aiagent.utils.FirebaseInitializer.Companion  Boolean 7com.example.aiagent.utils.FirebaseInitializer.Companion  Context 7com.example.aiagent.utils.FirebaseInitializer.Companion  Inject 7com.example.aiagent.utils.FirebaseInitializer.Companion  ApplicationContext ,com.example.aiagent.utils.NotificationHelper  Context ,com.example.aiagent.utils.NotificationHelper  Inject ,com.example.aiagent.utils.NotificationHelper  Int ,com.example.aiagent.utils.NotificationHelper  NotificationManager ,com.example.aiagent.utils.NotificationHelper  
PendingIntent ,com.example.aiagent.utils.NotificationHelper  String ,com.example.aiagent.utils.NotificationHelper  context ,com.example.aiagent.utils.NotificationHelper  Buffer -com.example.aiagent.utils.ProgressRequestBody  BufferedSink -com.example.aiagent.utils.ProgressRequestBody  ForwardingSink -com.example.aiagent.utils.ProgressRequestBody  Int -com.example.aiagent.utils.ProgressRequestBody  Long -com.example.aiagent.utils.ProgressRequestBody  	MediaType -com.example.aiagent.utils.ProgressRequestBody  RequestBody -com.example.aiagent.utils.ProgressRequestBody  Sink -com.example.aiagent.utils.ProgressRequestBody  Unit -com.example.aiagent.utils.ProgressRequestBody  Buffer :com.example.aiagent.utils.ProgressRequestBody.ProgressSink  Int :com.example.aiagent.utils.ProgressRequestBody.ProgressSink  Long :com.example.aiagent.utils.ProgressRequestBody.ProgressSink  Sink :com.example.aiagent.utils.ProgressRequestBody.ProgressSink  Unit :com.example.aiagent.utils.ProgressRequestBody.ProgressSink  Boolean )com.example.aiagent.utils.RemoteVideoInfo  Long )com.example.aiagent.utils.RemoteVideoInfo  String )com.example.aiagent.utils.RemoteVideoInfo  AlarmManager *com.example.aiagent.utils.SchedulerManager  ApplicationContext *com.example.aiagent.utils.SchedulerManager  Boolean *com.example.aiagent.utils.SchedulerManager  Context *com.example.aiagent.utils.SchedulerManager  Inject *com.example.aiagent.utils.SchedulerManager  Long *com.example.aiagent.utils.SchedulerManager  String *com.example.aiagent.utils.SchedulerManager  UploadSchedule *com.example.aiagent.utils.SchedulerManager  VideoProject *com.example.aiagent.utils.SchedulerManager  WorkManager *com.example.aiagent.utils.SchedulerManager  context *com.example.aiagent.utils.SchedulerManager  AlarmManager 4com.example.aiagent.utils.SchedulerManager.Companion  ApplicationContext 4com.example.aiagent.utils.SchedulerManager.Companion  Boolean 4com.example.aiagent.utils.SchedulerManager.Companion  Context 4com.example.aiagent.utils.SchedulerManager.Companion  Inject 4com.example.aiagent.utils.SchedulerManager.Companion  Long 4com.example.aiagent.utils.SchedulerManager.Companion  String 4com.example.aiagent.utils.SchedulerManager.Companion  UploadSchedule 4com.example.aiagent.utils.SchedulerManager.Companion  VideoProject 4com.example.aiagent.utils.SchedulerManager.Companion  WorkManager 4com.example.aiagent.utils.SchedulerManager.Companion  String ,com.example.aiagent.utils.ServiceAccountInfo  ApplicationContext -com.example.aiagent.utils.SmartContentManager  Boolean -com.example.aiagent.utils.SmartContentManager  ContentContext -com.example.aiagent.utils.SmartContentManager  Context -com.example.aiagent.utils.SmartContentManager  
FirebaseVideo -com.example.aiagent.utils.SmartContentManager  GeminiRepository -com.example.aiagent.utils.SmartContentManager  Inject -com.example.aiagent.utils.SmartContentManager  Int -com.example.aiagent.utils.SmartContentManager  List -com.example.aiagent.utils.SmartContentManager  Result -com.example.aiagent.utils.SmartContentManager  String -com.example.aiagent.utils.SmartContentManager  UserSettings -com.example.aiagent.utils.SmartContentManager  VideoContentResponse -com.example.aiagent.utils.SmartContentManager  ApplicationContext 7com.example.aiagent.utils.SmartContentManager.Companion  Boolean 7com.example.aiagent.utils.SmartContentManager.Companion  ContentContext 7com.example.aiagent.utils.SmartContentManager.Companion  Context 7com.example.aiagent.utils.SmartContentManager.Companion  
FirebaseVideo 7com.example.aiagent.utils.SmartContentManager.Companion  GeminiRepository 7com.example.aiagent.utils.SmartContentManager.Companion  Inject 7com.example.aiagent.utils.SmartContentManager.Companion  Int 7com.example.aiagent.utils.SmartContentManager.Companion  List 7com.example.aiagent.utils.SmartContentManager.Companion  Result 7com.example.aiagent.utils.SmartContentManager.Companion  String 7com.example.aiagent.utils.SmartContentManager.Companion  UserSettings 7com.example.aiagent.utils.SmartContentManager.Companion  VideoContentResponse 7com.example.aiagent.utils.SmartContentManager.Companion  ApplicationContext )com.example.aiagent.utils.VideoDownloader  Boolean )com.example.aiagent.utils.VideoDownloader  Context )com.example.aiagent.utils.VideoDownloader  File )com.example.aiagent.utils.VideoDownloader  Inject )com.example.aiagent.utils.VideoDownloader  Int )com.example.aiagent.utils.VideoDownloader  OkHttpClient )com.example.aiagent.utils.VideoDownloader  RemoteVideoInfo )com.example.aiagent.utils.VideoDownloader  Result )com.example.aiagent.utils.VideoDownloader  String )com.example.aiagent.utils.VideoDownloader  Unit )com.example.aiagent.utils.VideoDownloader  VideoQuality )com.example.aiagent.utils.VideoDownloader  ApplicationContext 3com.example.aiagent.utils.VideoDownloader.Companion  Boolean 3com.example.aiagent.utils.VideoDownloader.Companion  Context 3com.example.aiagent.utils.VideoDownloader.Companion  File 3com.example.aiagent.utils.VideoDownloader.Companion  Inject 3com.example.aiagent.utils.VideoDownloader.Companion  Int 3com.example.aiagent.utils.VideoDownloader.Companion  OkHttpClient 3com.example.aiagent.utils.VideoDownloader.Companion  RemoteVideoInfo 3com.example.aiagent.utils.VideoDownloader.Companion  Result 3com.example.aiagent.utils.VideoDownloader.Companion  String 3com.example.aiagent.utils.VideoDownloader.Companion  Unit 3com.example.aiagent.utils.VideoDownloader.Companion  VideoQuality 3com.example.aiagent.utils.VideoDownloader.Companion  Int #com.example.aiagent.utils.VideoInfo  ApplicationContext (com.example.aiagent.utils.VideoProcessor  Context (com.example.aiagent.utils.VideoProcessor  File (com.example.aiagent.utils.VideoProcessor  Inject (com.example.aiagent.utils.VideoProcessor  Int (com.example.aiagent.utils.VideoProcessor  Result (com.example.aiagent.utils.VideoProcessor  String (com.example.aiagent.utils.VideoProcessor  Unit (com.example.aiagent.utils.VideoProcessor  	VideoInfo (com.example.aiagent.utils.VideoProcessor  VideoProject (com.example.aiagent.utils.VideoProcessor  WatermarkStyle (com.example.aiagent.utils.VideoProcessor  ApplicationContext 2com.example.aiagent.utils.VideoProcessor.Companion  Context 2com.example.aiagent.utils.VideoProcessor.Companion  File 2com.example.aiagent.utils.VideoProcessor.Companion  Inject 2com.example.aiagent.utils.VideoProcessor.Companion  Int 2com.example.aiagent.utils.VideoProcessor.Companion  Result 2com.example.aiagent.utils.VideoProcessor.Companion  String 2com.example.aiagent.utils.VideoProcessor.Companion  Unit 2com.example.aiagent.utils.VideoProcessor.Companion  	VideoInfo 2com.example.aiagent.utils.VideoProcessor.Companion  VideoProject 2com.example.aiagent.utils.VideoProcessor.Companion  WatermarkStyle 2com.example.aiagent.utils.VideoProcessor.Companion  Boolean ,com.example.aiagent.utils.YouTubeAuthManager  Context ,com.example.aiagent.utils.YouTubeAuthManager  List ,com.example.aiagent.utils.YouTubeAuthManager  Long ,com.example.aiagent.utils.YouTubeAuthManager  ServiceAccountCredentials ,com.example.aiagent.utils.YouTubeAuthManager  ServiceAccountInfo ,com.example.aiagent.utils.YouTubeAuthManager  String ,com.example.aiagent.utils.YouTubeAuthManager  listOf ,com.example.aiagent.utils.YouTubeAuthManager  Boolean 6com.example.aiagent.utils.YouTubeAuthManager.Companion  Context 6com.example.aiagent.utils.YouTubeAuthManager.Companion  List 6com.example.aiagent.utils.YouTubeAuthManager.Companion  Long 6com.example.aiagent.utils.YouTubeAuthManager.Companion  ServiceAccountCredentials 6com.example.aiagent.utils.YouTubeAuthManager.Companion  ServiceAccountInfo 6com.example.aiagent.utils.YouTubeAuthManager.Companion  String 6com.example.aiagent.utils.YouTubeAuthManager.Companion  	getLISTOf 6com.example.aiagent.utils.YouTubeAuthManager.Companion  	getListOf 6com.example.aiagent.utils.YouTubeAuthManager.Companion  invoke 6com.example.aiagent.utils.YouTubeAuthManager.Companion  listOf 6com.example.aiagent.utils.YouTubeAuthManager.Companion  ApplicationContext -com.example.aiagent.utils.YouTubeSetupManager  Boolean -com.example.aiagent.utils.YouTubeSetupManager  Context -com.example.aiagent.utils.YouTubeSetupManager  Inject -com.example.aiagent.utils.YouTubeSetupManager  NotificationHelper -com.example.aiagent.utils.YouTubeSetupManager  Result -com.example.aiagent.utils.YouTubeSetupManager  ServiceAccountInfo -com.example.aiagent.utils.YouTubeSetupManager  String -com.example.aiagent.utils.YouTubeSetupManager  Uri -com.example.aiagent.utils.YouTubeSetupManager  YouTubeAuthManager -com.example.aiagent.utils.YouTubeSetupManager  YouTubeSetupStatus -com.example.aiagent.utils.YouTubeSetupManager  ApplicationContext 7com.example.aiagent.utils.YouTubeSetupManager.Companion  Boolean 7com.example.aiagent.utils.YouTubeSetupManager.Companion  Context 7com.example.aiagent.utils.YouTubeSetupManager.Companion  Inject 7com.example.aiagent.utils.YouTubeSetupManager.Companion  NotificationHelper 7com.example.aiagent.utils.YouTubeSetupManager.Companion  Result 7com.example.aiagent.utils.YouTubeSetupManager.Companion  ServiceAccountInfo 7com.example.aiagent.utils.YouTubeSetupManager.Companion  String 7com.example.aiagent.utils.YouTubeSetupManager.Companion  Uri 7com.example.aiagent.utils.YouTubeSetupManager.Companion  YouTubeAuthManager 7com.example.aiagent.utils.YouTubeSetupManager.Companion  YouTubeSetupStatus 7com.example.aiagent.utils.YouTubeSetupManager.Companion  ScheduledUploadWorker com.example.aiagent.worker  String com.example.aiagent.worker  Assisted 0com.example.aiagent.worker.ScheduledUploadWorker  AssistedInject 0com.example.aiagent.worker.ScheduledUploadWorker  Context 0com.example.aiagent.worker.ScheduledUploadWorker  NotificationHelper 0com.example.aiagent.worker.ScheduledUploadWorker  Result 0com.example.aiagent.worker.ScheduledUploadWorker  String 0com.example.aiagent.worker.ScheduledUploadWorker  VideoProjectRepository 0com.example.aiagent.worker.ScheduledUploadWorker  WorkerParameters 0com.example.aiagent.worker.ScheduledUploadWorker  Assisted :com.example.aiagent.worker.ScheduledUploadWorker.Companion  AssistedInject :com.example.aiagent.worker.ScheduledUploadWorker.Companion  Context :com.example.aiagent.worker.ScheduledUploadWorker.Companion  NotificationHelper :com.example.aiagent.worker.ScheduledUploadWorker.Companion  Result :com.example.aiagent.worker.ScheduledUploadWorker.Companion  String :com.example.aiagent.worker.ScheduledUploadWorker.Companion  VideoProjectRepository :com.example.aiagent.worker.ScheduledUploadWorker.Companion  WorkerParameters :com.example.aiagent.worker.ScheduledUploadWorker.Companion  GenerativeModel !com.google.ai.client.generativeai  GenerateContentResponse &com.google.ai.client.generativeai.type  generationConfig &com.google.ai.client.generativeai.type  GoogleCredentials com.google.auth.oauth2  ServiceAccountCredentials com.google.auth.oauth2  FirebaseApp com.google.firebase  CollectionReference com.google.firebase.firestore  
DocumentId com.google.firebase.firestore  FirebaseFirestore com.google.firebase.firestore  FirebaseFirestoreSettings com.google.firebase.firestore  PropertyName com.google.firebase.firestore  Query com.google.firebase.firestore  
collection /com.google.firebase.firestore.FirebaseFirestore  getInstance /com.google.firebase.firestore.FirebaseFirestore  Module dagger  Provides dagger  Assisted dagger.assisted  AssistedInject dagger.assisted  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  
HiltViewModel dagger.hilt.android.lifecycle  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  File java.io  FileInputStream java.io  FileOutputStream java.io  InputStream java.io  	Analytics 	java.lang  
Configuration 	java.lang  Context 	java.lang  
Converters 	java.lang  CoroutineScope 	java.lang  	Dashboard 	java.lang  DashboardUiState 	java.lang  Dispatchers 	java.lang  ExperimentalMaterial3Api 	java.lang  FirebaseFirestore 	java.lang  Json 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  R 	java.lang  SettingsUiState 	java.lang  SingletonComponent 	java.lang  
SupervisorJob 	java.lang  VideoLibrary 	java.lang  WorkManager 	java.lang  YouTubeAuthManager 	java.lang  android 	java.lang  androidx 	java.lang  asStateFlow 	java.lang  com 	java.lang  listOf 	java.lang  SimpleDateFormat 	java.text  
Composable 	java.util  Context 	java.util  CoroutineScope 	java.util  Date 	java.util  Dispatchers 	java.util  ExperimentalMaterial3Api 	java.util  
FirebaseVideo 	java.util  Result 	java.util  
SupervisorJob 	java.util  UUID 	java.util  UserSettings 	java.util  VideoProject 	java.util  WorkManager 	java.util  listOf 	java.util  TimeUnit java.util.concurrent  Inject javax.inject  	Singleton javax.inject  	Analytics kotlin  Any kotlin  Array kotlin  Boolean kotlin  
Configuration kotlin  Context kotlin  
Converters kotlin  CoroutineScope kotlin  	Dashboard kotlin  DashboardUiState kotlin  Dispatchers kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  FirebaseFirestore kotlin  Float kotlin  	Function1 kotlin  Int kotlin  Json kotlin  Long kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  R kotlin  Result kotlin  SettingsUiState kotlin  SingletonComponent kotlin  String kotlin  
SupervisorJob kotlin  Unit kotlin  VideoLibrary kotlin  Volatile kotlin  WorkManager kotlin  YouTubeAuthManager kotlin  android kotlin  androidx kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  listOf kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getSP 
kotlin.Int  getSp 
kotlin.Int  	Analytics kotlin.annotation  
Configuration kotlin.annotation  Context kotlin.annotation  
Converters kotlin.annotation  CoroutineScope kotlin.annotation  	Dashboard kotlin.annotation  DashboardUiState kotlin.annotation  Dispatchers kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  FirebaseFirestore kotlin.annotation  Json kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  R kotlin.annotation  Result kotlin.annotation  SettingsUiState kotlin.annotation  SingletonComponent kotlin.annotation  
SupervisorJob kotlin.annotation  VideoLibrary kotlin.annotation  Volatile kotlin.annotation  WorkManager kotlin.annotation  YouTubeAuthManager kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  listOf kotlin.annotation  	Analytics kotlin.collections  
Configuration kotlin.collections  Context kotlin.collections  
Converters kotlin.collections  CoroutineScope kotlin.collections  	Dashboard kotlin.collections  DashboardUiState kotlin.collections  Dispatchers kotlin.collections  ExperimentalMaterial3Api kotlin.collections  FirebaseFirestore kotlin.collections  Json kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  R kotlin.collections  Result kotlin.collections  SettingsUiState kotlin.collections  SingletonComponent kotlin.collections  
SupervisorJob kotlin.collections  VideoLibrary kotlin.collections  Volatile kotlin.collections  WorkManager kotlin.collections  YouTubeAuthManager kotlin.collections  android kotlin.collections  androidx kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  listOf kotlin.collections  	Analytics kotlin.comparisons  
Configuration kotlin.comparisons  Context kotlin.comparisons  
Converters kotlin.comparisons  CoroutineScope kotlin.comparisons  	Dashboard kotlin.comparisons  DashboardUiState kotlin.comparisons  Dispatchers kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  FirebaseFirestore kotlin.comparisons  Json kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  R kotlin.comparisons  Result kotlin.comparisons  SettingsUiState kotlin.comparisons  SingletonComponent kotlin.comparisons  
SupervisorJob kotlin.comparisons  VideoLibrary kotlin.comparisons  Volatile kotlin.comparisons  WorkManager kotlin.comparisons  YouTubeAuthManager kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  listOf kotlin.comparisons  CoroutineContext kotlin.coroutines  plus 1kotlin.coroutines.AbstractCoroutineContextElement  	Analytics 	kotlin.io  
Configuration 	kotlin.io  Context 	kotlin.io  
Converters 	kotlin.io  CoroutineScope 	kotlin.io  	Dashboard 	kotlin.io  DashboardUiState 	kotlin.io  Dispatchers 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  FirebaseFirestore 	kotlin.io  Json 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  R 	kotlin.io  Result 	kotlin.io  SettingsUiState 	kotlin.io  SingletonComponent 	kotlin.io  
SupervisorJob 	kotlin.io  VideoLibrary 	kotlin.io  Volatile 	kotlin.io  WorkManager 	kotlin.io  YouTubeAuthManager 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  listOf 	kotlin.io  	Analytics 
kotlin.jvm  
Configuration 
kotlin.jvm  Context 
kotlin.jvm  
Converters 
kotlin.jvm  CoroutineScope 
kotlin.jvm  	Dashboard 
kotlin.jvm  DashboardUiState 
kotlin.jvm  Dispatchers 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  FirebaseFirestore 
kotlin.jvm  Json 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  R 
kotlin.jvm  Result 
kotlin.jvm  SettingsUiState 
kotlin.jvm  SingletonComponent 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  VideoLibrary 
kotlin.jvm  Volatile 
kotlin.jvm  WorkManager 
kotlin.jvm  YouTubeAuthManager 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  listOf 
kotlin.jvm  	Analytics 
kotlin.ranges  
Configuration 
kotlin.ranges  Context 
kotlin.ranges  
Converters 
kotlin.ranges  CoroutineScope 
kotlin.ranges  	Dashboard 
kotlin.ranges  DashboardUiState 
kotlin.ranges  Dispatchers 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  FirebaseFirestore 
kotlin.ranges  Json 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  R 
kotlin.ranges  Result 
kotlin.ranges  SettingsUiState 
kotlin.ranges  SingletonComponent 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  VideoLibrary 
kotlin.ranges  Volatile 
kotlin.ranges  WorkManager 
kotlin.ranges  YouTubeAuthManager 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  listOf 
kotlin.ranges  KClass kotlin.reflect  	Analytics kotlin.sequences  
Configuration kotlin.sequences  Context kotlin.sequences  
Converters kotlin.sequences  CoroutineScope kotlin.sequences  	Dashboard kotlin.sequences  DashboardUiState kotlin.sequences  Dispatchers kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  FirebaseFirestore kotlin.sequences  Json kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  R kotlin.sequences  Result kotlin.sequences  SettingsUiState kotlin.sequences  SingletonComponent kotlin.sequences  
SupervisorJob kotlin.sequences  VideoLibrary kotlin.sequences  Volatile kotlin.sequences  WorkManager kotlin.sequences  YouTubeAuthManager kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  listOf kotlin.sequences  	Analytics kotlin.text  
Configuration kotlin.text  Context kotlin.text  
Converters kotlin.text  CoroutineScope kotlin.text  	Dashboard kotlin.text  DashboardUiState kotlin.text  Dispatchers kotlin.text  ExperimentalMaterial3Api kotlin.text  FirebaseFirestore kotlin.text  Json kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  R kotlin.text  Result kotlin.text  SettingsUiState kotlin.text  SingletonComponent kotlin.text  
SupervisorJob kotlin.text  VideoLibrary kotlin.text  Volatile kotlin.text  WorkManager kotlin.text  YouTubeAuthManager kotlin.text  android kotlin.text  androidx kotlin.text  asStateFlow kotlin.text  com kotlin.text  listOf kotlin.text  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  UserSettings kotlinx.coroutines  VideoProject kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  plus &kotlinx.coroutines.CoroutineDispatcher  IO kotlinx.coroutines.Dispatchers  DashboardUiState kotlinx.coroutines.flow  Flow kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  SettingsUiState kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  map kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  await kotlinx.coroutines.tasks  Serializable kotlinx.serialization  decodeFromString kotlinx.serialization  encodeToString kotlinx.serialization  Json kotlinx.serialization.json  JsonBuilder kotlinx.serialization.json  invoke kotlinx.serialization.json.Json  invoke 'kotlinx.serialization.json.Json.Default  ignoreUnknownKeys &kotlinx.serialization.json.JsonBuilder  	MediaType okhttp3  
MultipartBody okhttp3  OkHttpClient okhttp3  Request okhttp3  RequestBody okhttp3  Response okhttp3  	Companion okhttp3.MediaType  toMediaTypeOrNull okhttp3.MediaType.Companion  Part okhttp3.MultipartBody  Buffer okhttp3.RequestBody  BufferedSink okhttp3.RequestBody  	Companion okhttp3.RequestBody  ForwardingSink okhttp3.RequestBody  Int okhttp3.RequestBody  Long okhttp3.RequestBody  	MediaType okhttp3.RequestBody  RequestBody okhttp3.RequestBody  Sink okhttp3.RequestBody  Unit okhttp3.RequestBody  
asRequestBody okhttp3.RequestBody.Companion  
toRequestBody okhttp3.RequestBody.Companion  HttpLoggingInterceptor okhttp3.logging  Buffer okio  BufferedSink okio  ForwardingSink okio  Sink okio  buffer okio  Buffer okio.ForwardingSink  Int okio.ForwardingSink  Long okio.ForwardingSink  Sink okio.ForwardingSink  Unit okio.ForwardingSink  
JSONObject org.json  Response 	retrofit2  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  Body retrofit2.http  GET retrofit2.http  Header retrofit2.http  	Multipart retrofit2.http  POST retrofit2.http  PUT retrofit2.http  Part retrofit2.http  Query retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 