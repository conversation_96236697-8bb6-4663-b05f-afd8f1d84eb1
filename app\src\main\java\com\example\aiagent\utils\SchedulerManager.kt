package com.example.aiagent.utils

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.work.*
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.data.model.UploadSchedule
import com.example.aiagent.receiver.AlarmReceiver
import com.example.aiagent.worker.ScheduledUploadWorker
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير الجدولة للمهام المؤجلة
 * يتعامل مع جدولة المهام باستخدام AlarmManager و WorkManager
 */
@Singleton
class SchedulerManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
    private val workManager = WorkManager.getInstance(context)
    
    companion object {
        private const val TAG = "SchedulerManager"
        private const val PERIODIC_CHECK_WORK_NAME = "periodic_upload_check"
    }

    /**
     * جدولة مشروع للرفع في وقت محدد
     */
    fun scheduleProject(project: VideoProject) {
        val scheduledTime = project.scheduledUploadTime
        if (scheduledTime == null || scheduledTime <= System.currentTimeMillis()) {
            Log.w(TAG, "Invalid scheduled time for project: ${project.id}")
            return
        }

        try {
            // جدولة باستخدام AlarmManager للدقة العالية
            scheduleWithAlarmManager(project.id, scheduledTime)
            
            // جدولة احتياطية باستخدام WorkManager
            scheduleWithWorkManager(project.id, scheduledTime)
            
            Log.d(TAG, "Scheduled project ${project.id} for ${Date(scheduledTime)}")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error scheduling project: ${project.id}", e)
        }
    }

    /**
     * إلغاء جدولة مشروع
     */
    fun cancelProjectSchedule(projectId: String) {
        try {
            // إلغاء AlarmManager
            cancelAlarmManagerSchedule(projectId)
            
            // إلغاء WorkManager
            workManager.cancelUniqueWork("scheduled_upload_$projectId")
            
            Log.d(TAG, "Cancelled schedule for project: $projectId")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error cancelling schedule for project: $projectId", e)
        }
    }

    /**
     * إعداد جدولة دورية حسب إعدادات المستخدم
     */
    fun setupPeriodicSchedule(uploadSchedule: UploadSchedule) {
        if (!uploadSchedule.enabled) {
            cancelPeriodicSchedule()
            return
        }

        try {
            val constraints = Constraints.Builder()
                .setRequiredNetworkType(NetworkType.CONNECTED)
                .setRequiresBatteryNotLow(false) // السماح بالعمل حتى مع بطارية منخفضة
                .build()

            val periodicWork = PeriodicWorkRequestBuilder<ScheduledUploadWorker>(
                uploadSchedule.intervalHours.toLong(), TimeUnit.HOURS
            )
                .setConstraints(constraints)
                .addTag("periodic_upload")
                .setBackoffCriteria(
                    BackoffPolicy.EXPONENTIAL,
                    15, TimeUnit.MINUTES
                )
                .build()

            workManager.enqueueUniquePeriodicWork(
                PERIODIC_CHECK_WORK_NAME,
                ExistingPeriodicWorkPolicy.REPLACE,
                periodicWork
            )

            Log.d(TAG, "Setup periodic schedule every ${uploadSchedule.intervalHours} hours")

        } catch (e: Exception) {
            Log.e(TAG, "Error setting up periodic schedule", e)
        }
    }

    /**
     * إلغاء الجدولة الدورية
     */
    fun cancelPeriodicSchedule() {
        workManager.cancelUniqueWork(PERIODIC_CHECK_WORK_NAME)
        Log.d(TAG, "Cancelled periodic schedule")
    }

    /**
     * إعادة تشغيل الجدولة بعد إعادة تشغيل الجهاز
     */
    fun restartScheduler() {
        try {
            // إنشاء مهمة فورية للتحقق من المشاريع المجدولة
            val immediateWork = OneTimeWorkRequestBuilder<ScheduledUploadWorker>()
                .addTag("restart_check")
                .build()

            workManager.enqueueUniqueWork(
                "restart_scheduler",
                ExistingWorkPolicy.REPLACE,
                immediateWork
            )

            Log.d(TAG, "Restarted scheduler after boot")

        } catch (e: Exception) {
            Log.e(TAG, "Error restarting scheduler", e)
        }
    }

    /**
     * جدولة باستخدام AlarmManager
     */
    private fun scheduleWithAlarmManager(projectId: String, scheduledTime: Long) {
        val intent = Intent(context, AlarmReceiver::class.java).apply {
            action = AlarmReceiver.ACTION_SCHEDULED_UPLOAD
            putExtra(AlarmReceiver.EXTRA_PROJECT_ID, projectId)
        }

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            projectId.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (alarmManager.canScheduleExactAlarms()) {
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        scheduledTime,
                        pendingIntent
                    )
                } else {
                    // إذا لم تكن الصلاحية متاحة، استخدم جدولة تقريبية
                    alarmManager.setAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        scheduledTime,
                        pendingIntent
                    )
                }
            } else {
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    scheduledTime,
                    pendingIntent
                )
            }
        } catch (e: SecurityException) {
            Log.w(TAG, "Cannot schedule exact alarm, using approximate timing", e)
            alarmManager.set(
                AlarmManager.RTC_WAKEUP,
                scheduledTime,
                pendingIntent
            )
        }
    }

    /**
     * جدولة باستخدام WorkManager
     */
    private fun scheduleWithWorkManager(projectId: String, scheduledTime: Long) {
        val delay = scheduledTime - System.currentTimeMillis()
        if (delay <= 0) return

        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val scheduledWork = OneTimeWorkRequestBuilder<ScheduledUploadWorker>()
            .setInitialDelay(delay, TimeUnit.MILLISECONDS)
            .setConstraints(constraints)
            .setInputData(
                workDataOf(
                    ScheduledUploadWorker.KEY_PROJECT_ID to projectId
                )
            )
            .addTag("scheduled_upload")
            .addTag("project_$projectId")
            .build()

        workManager.enqueueUniqueWork(
            "scheduled_upload_$projectId",
            ExistingWorkPolicy.REPLACE,
            scheduledWork
        )
    }

    /**
     * إلغاء جدولة AlarmManager
     */
    private fun cancelAlarmManagerSchedule(projectId: String) {
        val intent = Intent(context, AlarmReceiver::class.java).apply {
            action = AlarmReceiver.ACTION_SCHEDULED_UPLOAD
            putExtra(AlarmReceiver.EXTRA_PROJECT_ID, projectId)
        }

        val pendingIntent = PendingIntent.getBroadcast(
            context,
            projectId.hashCode(),
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        alarmManager.cancel(pendingIntent)
        pendingIntent.cancel()
    }

    /**
     * التحقق من صلاحية جدولة المنبهات الدقيقة
     */
    fun canScheduleExactAlarms(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            alarmManager.canScheduleExactAlarms()
        } else {
            true
        }
    }
}
