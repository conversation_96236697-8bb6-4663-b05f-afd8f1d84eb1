package com.example.aiagent.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import android.content.Context
import com.example.aiagent.data.model.*
import kotlinx.serialization.encodeToString
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

/**
 * قاعدة البيانات الرئيسية للتطبيق
 */
@Database(
    entities = [
        VideoProject::class,
        UserSettings::class,
        AppStatistics::class,
        OperationLog::class
    ],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class AiAgentDatabase : RoomDatabase() {
    
    abstract fun videoProjectDao(): VideoProjectDao
    abstract fun userSettingsDao(): UserSettingsDao
    abstract fun appStatisticsDao(): AppStatisticsDao
    abstract fun operationLogDao(): OperationLogDao
    
    companion object {
        @Volatile
        private var INSTANCE: AiAgentDatabase? = null
        
        fun getDatabase(context: Context): AiAgentDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AiAgentDatabase::class.java,
                    "ai_agent_database"
                )
                .fallbackToDestructiveMigration()
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}

/**
 * محولات الأنواع لـ Room
 */
class Converters {
    
    private val json = Json { ignoreUnknownKeys = true }
    
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return json.encodeToString(value)
    }
    
    @TypeConverter
    fun toStringList(value: String): List<String> {
        return json.decodeFromString(value)
    }
    
    @TypeConverter
    fun fromVideoStatus(status: VideoStatus): String {
        return status.name
    }
    
    @TypeConverter
    fun toVideoStatus(status: String): VideoStatus {
        return VideoStatus.valueOf(status)
    }
    
    @TypeConverter
    fun fromWatermarkStyle(style: WatermarkStyle): String {
        return style.name
    }
    
    @TypeConverter
    fun toWatermarkStyle(style: String): WatermarkStyle {
        return WatermarkStyle.valueOf(style)
    }
    
    @TypeConverter
    fun fromVideoQuality(quality: VideoQuality): String {
        return quality.name
    }
    
    @TypeConverter
    fun toVideoQuality(quality: String): VideoQuality {
        return VideoQuality.valueOf(quality)
    }
    
    @TypeConverter
    fun fromUploadSchedule(schedule: UploadSchedule): String {
        return json.encodeToString(schedule)
    }
    
    @TypeConverter
    fun toUploadSchedule(schedule: String): UploadSchedule {
        return json.decodeFromString(schedule)
    }
}
