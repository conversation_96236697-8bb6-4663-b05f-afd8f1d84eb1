R_DEF: Internal format may change without notice
local
color black
color white
drawable ic_app
drawable ic_error
drawable ic_info
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_permission
drawable ic_processing
drawable ic_settings
drawable ic_stop
drawable ic_success
drawable ic_upload
drawable ic_video_processing
drawable ic_warning
drawable ic_youtube
font roboto_bold
font roboto_medium
font roboto_regular
id main
layout activity_main
mipmap ic_launcher
mipmap ic_launcher_round
string action_create_immediate
string action_create_video
string action_customize_app
string action_open_settings
string action_schedule_upload
string action_schedule_video
string action_view_analytics
string action_view_reports
string agent_active_working
string agent_status
string agent_stopped
string app_name
string auto_upload
string button_cancel
string button_export
string button_import
string button_reset
string button_save
string button_setup
string channel_name
string current_task
string dashboard_agent_active
string dashboard_agent_inactive
string dashboard_channel
string dashboard_welcome
string default_web_client_id
string error_update_failed
string gcm_defaultSenderId
string gemini_api_key
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string info_feature_coming_soon
string nav_analytics
string nav_dashboard
string nav_projects
string nav_settings
string next_upload
string no_recent_projects
string notification_agent_started
string notification_agent_stopped
string notification_error
string notification_video_processing
string notification_video_uploaded
string notification_video_uploading
string project_id
string recent_activity
string settings_ai
string settings_app
string settings_channel
string settings_upload
string settings_youtube
string start_new_project
string stats_pending_videos
string stats_success_rate
string stats_total_videos
string stats_uploaded_today
string status_cancelled
string status_downloading
string status_failed
string status_generating_content
string status_pending
string status_processing
string status_ready_to_upload
string status_uploaded
string status_uploading
string success_updated
string upload_interval
string view_all
string youtube_setup
style Base.Theme.AiAgent
style Theme.AiAgent
xml backup_rules
xml data_extraction_rules
xml file_paths
