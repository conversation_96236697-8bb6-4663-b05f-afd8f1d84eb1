package com.example.aiagent.utils

import android.content.Context
import android.net.Uri
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير إعداد YouTube Service Account
 * يساعد في إعداد وإدارة ملفات Service Account
 */
@Singleton
class YouTubeSetupManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val youTubeAuthManager: YouTubeAuthManager,
    private val notificationHelper: NotificationHelper
) {
    
    companion object {
        private const val TAG = "YouTubeSetupManager"
        private const val SERVICE_ACCOUNT_DIR = "service_accounts"
        private const val SERVICE_ACCOUNT_FILENAME = "youtube_service_account.json"
    }

    /**
     * إعداد Service Account من ملف
     */
    suspend fun setupServiceAccountFromFile(fileUri: Uri): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Setting up service account from file")
            
            // قراءة محتوى الملف
            val inputStream = context.contentResolver.openInputStream(fileUri)
                ?: return@withContext Result.failure(Exception("فشل في قراءة الملف"))
            
            val jsonContent = inputStream.bufferedReader().use { it.readText() }
            inputStream.close()
            
            // التحقق من صحة JSON
            val validationResult = validateServiceAccountJson(jsonContent)
            if (validationResult.isFailure) {
                return@withContext validationResult
            }
            
            // حفظ الملف في التطبيق
            val savedPath = saveServiceAccountFile(jsonContent)
            if (savedPath == null) {
                return@withContext Result.failure(Exception("فشل في حفظ ملف Service Account"))
            }
            
            // تهيئة المصادقة
            val initSuccess = youTubeAuthManager.initializeFromJson(jsonContent)
            if (!initSuccess) {
                return@withContext Result.failure(Exception("فشل في تهيئة المصادقة"))
            }
            
            // اختبار الاتصال
            val connectionTest = youTubeAuthManager.testConnection()
            if (!connectionTest) {
                return@withContext Result.failure(Exception("فشل في اختبار الاتصال بـ YouTube API"))
            }
            
            // حفظ المسار في الإعدادات
            youTubeAuthManager.saveServiceAccountPath(savedPath)
            
            notificationHelper.showSuccess(
                "تم إعداد YouTube بنجاح",
                "تم تهيئة Service Account وهو جاهز للاستخدام"
            )
            
            Log.d(TAG, "Service account setup completed successfully")
            Result.success(savedPath)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up service account", e)
            notificationHelper.showError(
                "خطأ في إعداد YouTube",
                "فشل في إعداد Service Account: ${e.message}"
            )
            Result.failure(e)
        }
    }

    /**
     * التحقق من صحة JSON الخاص بـ Service Account
     */
    private fun validateServiceAccountJson(jsonContent: String): Result<String> {
        return try {
            val jsonObject = JSONObject(jsonContent)
            
            // التحقق من الحقول المطلوبة
            val requiredFields = listOf(
                "type",
                "project_id",
                "private_key_id",
                "private_key",
                "client_email",
                "client_id",
                "auth_uri",
                "token_uri"
            )
            
            val missingFields = mutableListOf<String>()
            for (field in requiredFields) {
                if (!jsonObject.has(field) || jsonObject.getString(field).isEmpty()) {
                    missingFields.add(field)
                }
            }
            
            if (missingFields.isNotEmpty()) {
                return Result.failure(Exception("حقول مفقودة في ملف Service Account: ${missingFields.joinToString(", ")}"))
            }
            
            // التحقق من نوع الحساب
            val accountType = jsonObject.getString("type")
            if (accountType != "service_account") {
                return Result.failure(Exception("نوع الحساب غير صحيح. يجب أن يكون 'service_account'"))
            }
            
            // التحقق من صحة البريد الإلكتروني
            val clientEmail = jsonObject.getString("client_email")
            if (!clientEmail.contains("@") || !clientEmail.contains(".iam.gserviceaccount.com")) {
                return Result.failure(Exception("بريد Service Account غير صحيح"))
            }
            
            Result.success("ملف Service Account صحيح")
        } catch (e: Exception) {
            Result.failure(Exception("ملف JSON غير صحيح: ${e.message}"))
        }
    }

    /**
     * حفظ ملف Service Account
     */
    private fun saveServiceAccountFile(jsonContent: String): String? {
        return try {
            // إنشاء مجلد Service Accounts
            val serviceAccountDir = File(context.filesDir, SERVICE_ACCOUNT_DIR)
            if (!serviceAccountDir.exists()) {
                serviceAccountDir.mkdirs()
            }
            
            // حفظ الملف
            val serviceAccountFile = File(serviceAccountDir, SERVICE_ACCOUNT_FILENAME)
            val outputStream = FileOutputStream(serviceAccountFile)
            outputStream.write(jsonContent.toByteArray())
            outputStream.close()
            
            Log.d(TAG, "Service account file saved: ${serviceAccountFile.absolutePath}")
            serviceAccountFile.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "Error saving service account file", e)
            null
        }
    }

    /**
     * تحميل Service Account المحفوظ
     */
    suspend fun loadSavedServiceAccount(): Result<String> = withContext(Dispatchers.IO) {
        try {
            val savedPath = youTubeAuthManager.loadServiceAccountPath()
            if (savedPath == null) {
                return@withContext Result.failure(Exception("لا يوجد Service Account محفوظ"))
            }
            
            val serviceAccountFile = File(savedPath)
            if (!serviceAccountFile.exists()) {
                return@withContext Result.failure(Exception("ملف Service Account غير موجود"))
            }
            
            val jsonContent = serviceAccountFile.readText()
            
            // تهيئة المصادقة
            val initSuccess = youTubeAuthManager.initializeFromJson(jsonContent)
            if (!initSuccess) {
                return@withContext Result.failure(Exception("فشل في تهيئة المصادقة"))
            }
            
            // اختبار الاتصال
            val connectionTest = youTubeAuthManager.testConnection()
            if (!connectionTest) {
                return@withContext Result.failure(Exception("فشل في اختبار الاتصال"))
            }
            
            Log.d(TAG, "Saved service account loaded successfully")
            Result.success(savedPath)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading saved service account", e)
            Result.failure(e)
        }
    }

    /**
     * التحقق من حالة إعداد YouTube
     */
    suspend fun checkYouTubeSetupStatus(): YouTubeSetupStatus = withContext(Dispatchers.IO) {
        try {
            // التحقق من وجود ملف محفوظ
            val savedPath = youTubeAuthManager.loadServiceAccountPath()
            if (savedPath == null) {
                return@withContext YouTubeSetupStatus.NOT_CONFIGURED
            }
            
            // التحقق من وجود الملف
            val serviceAccountFile = File(savedPath)
            if (!serviceAccountFile.exists()) {
                return@withContext YouTubeSetupStatus.FILE_MISSING
            }
            
            // التحقق من تهيئة المصادقة
            if (!youTubeAuthManager.isInitialized()) {
                val loadResult = loadSavedServiceAccount()
                if (loadResult.isFailure) {
                    return@withContext YouTubeSetupStatus.AUTH_FAILED
                }
            }
            
            // اختبار الاتصال
            val connectionTest = youTubeAuthManager.testConnection()
            if (!connectionTest) {
                return@withContext YouTubeSetupStatus.CONNECTION_FAILED
            }
            
            YouTubeSetupStatus.CONFIGURED_AND_WORKING
        } catch (e: Exception) {
            Log.e(TAG, "Error checking YouTube setup status", e)
            YouTubeSetupStatus.ERROR
        }
    }

    /**
     * الحصول على معلومات Service Account
     */
    fun getServiceAccountInfo(): ServiceAccountInfo? {
        return youTubeAuthManager.getServiceAccountInfo()
    }

    /**
     * إزالة إعداد YouTube
     */
    fun removeYouTubeSetup(): Boolean {
        return try {
            // حذف ملف Service Account
            val savedPath = youTubeAuthManager.loadServiceAccountPath()
            if (savedPath != null) {
                val serviceAccountFile = File(savedPath)
                if (serviceAccountFile.exists()) {
                    serviceAccountFile.delete()
                }
            }
            
            // مسح الإعدادات
            val prefs = context.getSharedPreferences("youtube_auth", Context.MODE_PRIVATE)
            prefs.edit().clear().apply()
            
            // تنظيف المصادقة
            youTubeAuthManager.cleanup()
            
            notificationHelper.showInfo(
                "تم إزالة إعداد YouTube",
                "تم حذف جميع إعدادات YouTube بنجاح"
            )
            
            Log.d(TAG, "YouTube setup removed successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error removing YouTube setup", e)
            false
        }
    }

    /**
     * إنشاء تقرير تشخيصي
     */
    suspend fun generateDiagnosticReport(): String = withContext(Dispatchers.IO) {
        val report = StringBuilder()
        
        try {
            report.appendLine("=== تقرير تشخيص YouTube ===")
            report.appendLine("التاريخ: ${java.util.Date()}")
            report.appendLine()
            
            // حالة الإعداد
            val setupStatus = checkYouTubeSetupStatus()
            report.appendLine("حالة الإعداد: $setupStatus")
            
            // معلومات Service Account
            val serviceAccountInfo = getServiceAccountInfo()
            if (serviceAccountInfo != null) {
                report.appendLine("Client Email: ${serviceAccountInfo.clientEmail}")
                report.appendLine("Project ID: ${serviceAccountInfo.projectId}")
                report.appendLine("Private Key ID: ${serviceAccountInfo.privateKeyId}")
            } else {
                report.appendLine("معلومات Service Account: غير متوفرة")
            }
            
            // التحقق من الصلاحيات
            val permissionIssues = youTubeAuthManager.validateServiceAccountPermissions()
            if (permissionIssues.isEmpty()) {
                report.appendLine("الصلاحيات: صحيحة")
            } else {
                report.appendLine("مشاكل الصلاحيات:")
                permissionIssues.forEach { issue ->
                    report.appendLine("  - $issue")
                }
            }
            
            // اختبار الاتصال
            val connectionTest = youTubeAuthManager.testConnection()
            report.appendLine("اختبار الاتصال: ${if (connectionTest) "نجح" else "فشل"}")
            
        } catch (e: Exception) {
            report.appendLine("خطأ في إنشاء التقرير: ${e.message}")
        }
        
        report.toString()
    }
}

/**
 * حالات إعداد YouTube
 */
enum class YouTubeSetupStatus {
    NOT_CONFIGURED,           // غير مُعد
    FILE_MISSING,            // الملف مفقود
    AUTH_FAILED,             // فشل المصادقة
    CONNECTION_FAILED,       // فشل الاتصال
    CONFIGURED_AND_WORKING,  // مُعد ويعمل
    ERROR                    // خطأ
}
