# وكيل الفيديو الذكي - AI Video Agent

تطبيق أندرويد ذكي لإنشاء ونشر فيديوهات YouTube Shorts تلقائياً باستخدام الذكاء الاصطناعي.

## 🌟 المميزات الرئيسية

### 🤖 الذكاء الاصطناعي
- **Gemini AI Integration**: توليد محتوى ذكي للعناوين والأوصاف والهاشتاغات
- **محتوى مخصص**: تحسين المحتوى حسب الوقت والمناسبات
- **تحليل الترندات**: إنشاء محتوى يواكب الاتجاهات الحالية

### 📱 واجهة مستخدم حديثة
- **Material Design 3**: تصميم عصري ومتجاوب
- **دعم اللغة العربية**: واجهة مستخدم باللغة العربية
- **ثيم YouTube**: ألوان مستوحاة من YouTube
- **تنقل سهل**: شريط تنقل سفلي بديهي

### 🎥 معالجة الفيديو
- **FFmpeg Integration**: معالجة فيديو احترافية
- **تحسين الجودة**: ضبط الدقة والجودة تلقائياً
- **علامة مائية**: إضافة علامة مائية مخصصة
- **تحسين للشورتس**: تحسين خاص لفيديوهات YouTube Shorts

### ☁️ Firebase Integration
- **قاعدة بيانات الفيديوهات**: مكتبة ضخمة من الفيديوهات المضحكة
- **Firestore**: تخزين البيانات والإحصائيات
- **Firebase Storage**: تخزين الفيديوهات والصور

### 📤 رفع تلقائي على YouTube
- **Service Account**: رفع آمن باستخدام Google Service Account
- **رفع مجدول**: جدولة الرفع في أوقات محددة
- **إعادة المحاولة**: نظام ذكي لإعادة المحاولة عند الفشل
- **مراقبة التقدم**: متابعة حالة الرفع في الوقت الفعلي

### 🔄 العمل في الخلفية
- **Foreground Services**: خدمات متوافقة مع Android 14+
- **Work Manager**: جدولة المهام بشكل موثوق
- **Battery Optimization**: تحسين استهلاك البطارية
- **Boot Receiver**: إعادة تشغيل المهام بعد إعادة تشغيل الجهاز

## 🏗️ البنية التقنية

### 🛠️ التقنيات المستخدمة
- **Kotlin**: لغة البرمجة الأساسية
- **Jetpack Compose**: واجهة المستخدم الحديثة
- **Hilt**: حقن التبعيات
- **Room Database**: قاعدة البيانات المحلية
- **Coroutines & Flow**: البرمجة غير المتزامنة
- **Retrofit**: التواصل مع APIs
- **FFmpeg**: معالجة الفيديو

### 📦 المكونات الرئيسية

#### 🎯 Core Components
- `AiAgentManager`: المدير الرئيسي للوكيل الذكي
- `SmartContentManager`: مدير المحتوى الذكي
- `VideoProcessor`: معالج الفيديو
- `SchedulerManager`: مدير الجدولة

#### 🗄️ Data Layer
- `VideoProjectRepository`: مستودع مشاريع الفيديو
- `FirebaseVideoRepository`: مستودع فيديوهات Firebase
- `YouTubeRepository`: مستودع YouTube API
- `GeminiRepository`: مستودع Gemini AI

#### 🎨 UI Layer
- `DashboardScreen`: الشاشة الرئيسية
- `ProjectsScreen`: شاشة المشاريع
- `AnalyticsScreen`: شاشة الإحصائيات
- `SettingsScreen`: شاشة الإعدادات

#### ⚙️ Services
- `VideoProcessingService`: خدمة معالجة الفيديو
- `YouTubeUploadService`: خدمة رفع YouTube
- `ScheduledUploadWorker`: عامل المهام المجدولة

## 🚀 التثبيت والإعداد

### 📋 المتطلبات
- Android Studio Arctic Fox أو أحدث
- Android SDK 24+ (Android 7.0)
- Kotlin 1.9.0+
- Gradle 8.0+

### 🔧 خطوات الإعداد

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/ai-video-agent.git
cd ai-video-agent
```

2. **إعداد Firebase**
   - اتبع التعليمات في [FIREBASE_SETUP.md](FIREBASE_SETUP.md)
   - أضف ملف `google-services.json` إلى مجلد `app/`

3. **إعداد YouTube API**
   - اتبع التعليمات في [YOUTUBE_SETUP.md](YOUTUBE_SETUP.md)
   - أنشئ Service Account وحمل ملف JSON

4. **إعداد Gemini AI**
   - احصل على API Key من [Google AI Studio](https://makersuite.google.com/)
   - أدخل المفتاح في إعدادات التطبيق

5. **بناء المشروع**
```bash
./gradlew build
```

## 📱 كيفية الاستخدام

### 🎬 إنشاء فيديو جديد
1. افتح التطبيق واذهب للشاشة الرئيسية
2. انقر على "إنشاء فيديو"
3. سيقوم الوكيل الذكي بـ:
   - اختيار فيديو عشوائي من Firebase
   - توليد محتوى ذكي بـ Gemini AI
   - معالجة الفيديو وإضافة العلامة المائية
   - رفع الفيديو على YouTube تلقائياً

### ⏰ جدولة الفيديوهات
1. اذهب إلى الإعدادات
2. فعل "الرفع التلقائي"
3. حدد فترة الرفع (كل كم ساعة)
4. سيقوم الوكيل بإنشاء ورفع فيديوهات تلقائياً

### 📊 متابعة الإحصائيات
- شاهد إحصائيات الفيديوهات في الشاشة الرئيسية
- تابع حالة المشاريع في شاشة المشاريع
- راجع التقارير التفصيلية في شاشة الإحصائيات

## 🔒 الأمان والخصوصية

### 🛡️ حماية البيانات
- جميع مفاتيح API محفوظة محلياً ومشفرة
- لا يتم إرسال بيانات شخصية لخوادم خارجية
- Service Account محفوظ بأمان في التطبيق

### 🔐 الصلاحيات
- **الإنترنت**: لتحميل الفيديوهات والتواصل مع APIs
- **التخزين**: لحفظ الفيديوهات المعالجة
- **الخدمات في الخلفية**: للعمل التلقائي
- **الإشعارات**: لإعلام المستخدم بحالة العمليات

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم

### 📚 الوثائق
- [إعداد Firebase](FIREBASE_SETUP.md)
- [إعداد YouTube](YOUTUBE_SETUP.md)
- [دليل المطور](DEVELOPER_GUIDE.md)

### 🐛 الإبلاغ عن الأخطاء
إذا واجهت أي مشاكل، يرجى فتح [Issue جديد](https://github.com/your-username/ai-video-agent/issues).

### 💬 التواصل
- Email: <EMAIL>
- Discord: [AI Agent Community](https://discord.gg/aiagent)

## 🙏 شكر خاص

- **Google**: لـ Gemini AI و YouTube API
- **Firebase**: لخدمات قاعدة البيانات والتخزين
- **FFmpeg**: لمعالجة الفيديو
- **Material Design**: للتصميم الجميل

---

**تم تطويره بـ ❤️ للمجتمع العربي**
