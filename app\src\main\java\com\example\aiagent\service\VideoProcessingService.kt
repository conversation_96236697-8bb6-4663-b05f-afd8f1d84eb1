package com.example.aiagent.service

import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.example.aiagent.AiAgentApplication
import com.example.aiagent.R
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.data.model.VideoStatus
import com.example.aiagent.data.repository.VideoProjectRepository
import com.example.aiagent.MainActivity
import com.example.aiagent.utils.VideoProcessor
import com.example.aiagent.utils.NotificationHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * خدمة معالجة الفيديو في الخلفية
 * متوافقة مع Android 14+ وقيود Foreground Services
 */
@AndroidEntryPoint
class VideoProcessingService : LifecycleService() {

    @Inject
    lateinit var videoProjectRepository: VideoProjectRepository
    
    @Inject
    lateinit var videoProcessor: VideoProcessor
    
    @Inject
    lateinit var notificationHelper: NotificationHelper

    private var currentProjectId: String? = null
    private var isProcessing = false

    companion object {
        const val ACTION_START_PROCESSING = "START_PROCESSING"
        const val ACTION_STOP_PROCESSING = "STOP_PROCESSING"
        const val ACTION_PROCESS_VIDEO = "PROCESS_VIDEO"
        const val EXTRA_PROJECT_ID = "project_id"
        
        /**
         * بدء خدمة معالجة الفيديو
         */
        fun startProcessing(context: Context, projectId: String) {
            val intent = Intent(context, VideoProcessingService::class.java).apply {
                action = ACTION_PROCESS_VIDEO
                putExtra(EXTRA_PROJECT_ID, projectId)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        /**
         * إيقاف خدمة معالجة الفيديو
         */
        fun stopProcessing(context: Context) {
            val intent = Intent(context, VideoProcessingService::class.java).apply {
                action = ACTION_STOP_PROCESSING
            }
            context.startService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        startForegroundService()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        
        when (intent?.action) {
            ACTION_PROCESS_VIDEO -> {
                val projectId = intent.getStringExtra(EXTRA_PROJECT_ID)
                if (projectId != null) {
                    processVideo(projectId)
                }
            }
            ACTION_STOP_PROCESSING -> {
                stopProcessing()
            }
        }
        
        return START_STICKY
    }

    override fun onBind(intent: Intent): IBinder? {
        super.onBind(intent)
        return null
    }

    /**
     * بدء الخدمة في المقدمة
     */
    private fun startForegroundService() {
        val notification = createNotification(
            title = "وكيل الفيديو نشط",
            content = "جاهز لمعالجة الفيديوهات",
            progress = null
        )

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                AiAgentApplication.NOTIFICATION_ID_VIDEO_PROCESSING,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROCESSING
            )
        } else {
            startForeground(AiAgentApplication.NOTIFICATION_ID_VIDEO_PROCESSING, notification)
        }
    }

    /**
     * معالجة الفيديو
     */
    private fun processVideo(projectId: String) {
        if (isProcessing) {
            // إذا كان هناك معالجة جارية، أضف المشروع للقائمة
            return
        }

        currentProjectId = projectId
        isProcessing = true

        lifecycleScope.launch {
            try {
                val project = videoProjectRepository.getProjectById(projectId)
                if (project == null) {
                    notificationHelper.showError("مشروع غير موجود", "لم يتم العثور على المشروع المطلوب")
                    stopProcessing()
                    return@launch
                }

                // تحديث حالة المشروع
                videoProjectRepository.updateProjectStatus(projectId, VideoStatus.PROCESSING)
                
                // تحديث الإشعار
                updateNotification("جاري معالجة الفيديو", project.title, 0)

                // بدء معالجة الفيديو
                val result = videoProcessor.processVideo(
                    project = project,
                    onProgress = { progress ->
                        updateNotification("جاري معالجة الفيديو", project.title, progress)
                        lifecycleScope.launch {
                            // يمكن إضافة تحديث تقدم في قاعدة البيانات هنا إذا لزم الأمر
                        }
                    }
                )

                result.fold(
                    onSuccess = { processedVideoPath ->
                        // تحديث المشروع بمسار الفيديو المعالج
                        val updatedProject = project.copy(
                            processedVideoPath = processedVideoPath,
                            status = VideoStatus.READY_TO_UPLOAD
                        )
                        videoProjectRepository.updateProject(updatedProject)
                        
                        // إشعار النجاح
                        notificationHelper.showSuccess(
                            "تمت المعالجة بنجاح",
                            "تم معالجة فيديو: ${project.title}"
                        )
                        
                        // بدء رفع الفيديو تلقائياً إذا كان مفعلاً
                        YouTubeUploadService.startUpload(this@VideoProcessingService, projectId)
                    },
                    onFailure = { error ->
                        // تحديث حالة الفشل
                        videoProjectRepository.updateProjectStatus(
                            projectId, 
                            VideoStatus.FAILED, 
                            error.message
                        )
                        
                        // إشعار الخطأ
                        notificationHelper.showError(
                            "فشل في معالجة الفيديو",
                            error.message ?: "خطأ غير معروف"
                        )
                    }
                )

            } catch (e: Exception) {
                // معالجة الأخطاء العامة
                videoProjectRepository.updateProjectStatus(
                    projectId,
                    VideoStatus.FAILED,
                    "خطأ في الخدمة: ${e.message}"
                )
                
                notificationHelper.showError(
                    "خطأ في الخدمة",
                    e.message ?: "خطأ غير معروف"
                )
            } finally {
                stopProcessing()
            }
        }
    }

    /**
     * إيقاف المعالجة
     */
    private fun stopProcessing() {
        isProcessing = false
        currentProjectId = null
        
        // تحديث الإشعار
        updateNotification(
            "وكيل الفيديو نشط",
            "جاهز لمعالجة الفيديوهات",
            null
        )
        
        // إيقاف الخدمة إذا لم تعد هناك مهام
        stopSelf()
    }

    /**
     * تحديث الإشعار
     */
    private fun updateNotification(title: String, content: String, progress: Int?) {
        val notification = createNotification(title, content, progress)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(AiAgentApplication.NOTIFICATION_ID_VIDEO_PROCESSING, notification)
    }

    /**
     * إنشاء الإشعار
     */
    private fun createNotification(title: String, content: String, progress: Int?): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(this, AiAgentApplication.CHANNEL_VIDEO_PROCESSING)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_video_processing)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)

        // إضافة شريط التقدم إذا كان متاحاً
        if (progress != null) {
            builder.setProgress(100, progress, false)
                .setSubText("$progress%")
        }

        // إضافة زر الإيقاف
        val stopIntent = Intent(this, VideoProcessingService::class.java).apply {
            action = ACTION_STOP_PROCESSING
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        builder.addAction(R.drawable.ic_stop, "إيقاف", stopPendingIntent)

        return builder.build()
    }

    override fun onDestroy() {
        super.onDestroy()
        isProcessing = false
        currentProjectId = null
    }
}
