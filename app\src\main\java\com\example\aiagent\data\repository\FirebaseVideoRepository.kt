package com.example.aiagent.data.repository

import android.content.Context
import android.util.Log
import com.example.aiagent.data.firebase.FirebaseVideoService
import com.example.aiagent.data.model.*
import com.example.aiagent.utils.VideoDownloader
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.util.UUID
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مستودع Firebase للفيديوهات
 * يدير جلب وتحميل الفيديوهات من Firebase
 */
@Singleton
class FirebaseVideoRepository @Inject constructor(
    @ApplicationContext private val context: Context,
    private val firebaseVideoService: FirebaseVideoService,
    private val videoDownloader: VideoDownloader
) {
    
    companion object {
        private const val TAG = "FirebaseVideoRepository"
    }

    /**
     * إنشاء مشروع فيديو جديد من Firebase
     */
    suspend fun createVideoProjectFromFirebase(
        channelName: String,
        criteria: VideoSearchCriteria = VideoSearchCriteria()
    ): Result<VideoProject> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Creating video project from Firebase")
            
            // جلب فيديو عشوائي من Firebase
            val videoResult = firebaseVideoService.getRandomVideo(criteria)
            
            if (videoResult.isFailure) {
                return@withContext Result.failure(
                    videoResult.exceptionOrNull() ?: Exception("فشل في جلب فيديو من Firebase")
                )
            }
            
            val firebaseVideo = videoResult.getOrNull()
                ?: return@withContext Result.failure(Exception("لم يتم العثور على فيديو مناسب"))
            
            // إنشاء مشروع فيديو جديد
            val projectId = UUID.randomUUID().toString()
            val project = VideoProject(
                id = projectId,
                title = firebaseVideo.title,
                description = firebaseVideo.description,
                hashtags = firebaseVideo.tags,
                sourceVideoUrl = firebaseVideo.downloadUrl,
                watermarkText = channelName,
                status = VideoStatus.PENDING,
                createdAt = System.currentTimeMillis()
            )
            
            Log.d(TAG, "Created video project: ${project.id} from Firebase video: ${firebaseVideo.id}")
            Result.success(project)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating video project from Firebase", e)
            Result.failure(e)
        }
    }

    /**
     * إنشاء عدة مشاريع فيديو من Firebase
     */
    suspend fun createMultipleVideoProjects(
        channelName: String,
        count: Int,
        criteria: VideoSearchCriteria = VideoSearchCriteria()
    ): Result<List<VideoProject>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Creating $count video projects from Firebase")
            
            val projects = mutableListOf<VideoProject>()
            val updatedCriteria = criteria.copy(limit = count * 2) // جلب أكثر للتنوع
            
            // جلب فيديوهات متعددة
            val videosResult = firebaseVideoService.getVideos(updatedCriteria)
            
            if (videosResult.isFailure) {
                return@withContext Result.failure(
                    videosResult.exceptionOrNull() ?: Exception("فشل في جلب فيديوهات من Firebase")
                )
            }
            
            val firebaseVideos = videosResult.getOrNull() ?: emptyList()
            
            if (firebaseVideos.isEmpty()) {
                return@withContext Result.failure(Exception("لم يتم العثور على فيديوهات مناسبة"))
            }
            
            // إنشاء مشاريع من الفيديوهات
            val selectedVideos = firebaseVideos.shuffled().take(count)
            
            for (firebaseVideo in selectedVideos) {
                val projectId = UUID.randomUUID().toString()
                val project = VideoProject(
                    id = projectId,
                    title = firebaseVideo.title,
                    description = firebaseVideo.description,
                    hashtags = firebaseVideo.tags,
                    sourceVideoUrl = firebaseVideo.downloadUrl,
                    watermarkText = channelName,
                    status = VideoStatus.PENDING,
                    createdAt = System.currentTimeMillis()
                )
                projects.add(project)
            }
            
            Log.d(TAG, "Created ${projects.size} video projects from Firebase")
            Result.success(projects)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating multiple video projects from Firebase", e)
            Result.failure(e)
        }
    }

    /**
     * تحميل فيديو من Firebase
     */
    suspend fun downloadVideoFromFirebase(
        firebaseVideo: FirebaseVideo,
        onProgress: (Int) -> Unit = {}
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Downloading video from Firebase: ${firebaseVideo.id}")
            
            // إنشاء مجلد التحميل
            val downloadDir = File(context.cacheDir, "firebase_downloads")
            if (!downloadDir.exists()) {
                downloadDir.mkdirs()
            }
            
            // تحميل الفيديو
            val fileName = "${firebaseVideo.id}.mp4"
            val downloadResult = videoDownloader.downloadVideo(
                url = firebaseVideo.downloadUrl,
                outputDir = downloadDir,
                fileName = fileName,
                onProgress = onProgress
            )
            
            if (downloadResult.isSuccess) {
                val downloadedPath = downloadResult.getOrNull()!!
                
                // تسجيل التحميل في Firebase
                firebaseVideoService.logVideoUsage(
                    videoId = firebaseVideo.id,
                    userId = getUserId(),
                    success = true
                )
                
                Log.d(TAG, "Successfully downloaded video: $downloadedPath")
                Result.success(downloadedPath)
            } else {
                // تسجيل الفشل في Firebase
                firebaseVideoService.logVideoUsage(
                    videoId = firebaseVideo.id,
                    userId = getUserId(),
                    success = false,
                    errorMessage = downloadResult.exceptionOrNull()?.message
                )
                
                Result.failure(downloadResult.exceptionOrNull() ?: Exception("فشل في تحميل الفيديو"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading video from Firebase", e)
            Result.failure(e)
        }
    }

    /**
     * البحث في فيديوهات Firebase
     */
    suspend fun searchFirebaseVideos(
        query: String,
        criteria: VideoSearchCriteria = VideoSearchCriteria()
    ): Result<List<FirebaseVideo>> {
        return firebaseVideoService.searchVideos(query, criteria)
    }

    /**
     * الحصول على الفيديوهات الشائعة
     */
    suspend fun getPopularVideos(limit: Int = 10): Result<List<FirebaseVideo>> {
        return firebaseVideoService.getPopularVideos(limit)
    }

    /**
     * الحصول على إحصائيات الفيديوهات
     */
    suspend fun getVideoStatistics(): Result<VideoStats> {
        return firebaseVideoService.getVideoStats()
    }

    /**
     * إنشاء مشروع فيديو مجدول
     */
    suspend fun createScheduledVideoProject(
        channelName: String,
        scheduledTime: Long,
        criteria: VideoSearchCriteria = VideoSearchCriteria()
    ): Result<VideoProject> = withContext(Dispatchers.IO) {
        try {
            // إنشاء مشروع عادي أولاً
            val projectResult = createVideoProjectFromFirebase(channelName, criteria)
            
            if (projectResult.isFailure) {
                return@withContext projectResult
            }
            
            val project = projectResult.getOrNull()!!
            
            // إضافة وقت الجدولة
            val scheduledProject = project.copy(
                scheduledUploadTime = scheduledTime
            )
            
            Log.d(TAG, "Created scheduled video project for: ${java.util.Date(scheduledTime)}")
            Result.success(scheduledProject)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating scheduled video project", e)
            Result.failure(e)
        }
    }

    /**
     * تسجيل نجاح رفع الفيديو
     */
    suspend fun logSuccessfulUpload(
        projectId: String,
        youtubeVideoId: String
    ): Result<Unit> {
        return try {
            // البحث عن الفيديو الأصلي في Firebase باستخدام URL المصدر
            // هذا يتطلب ربط projectId بـ Firebase video ID
            // للبساطة، سنستخدم projectId كـ userId
            firebaseVideoService.logVideoUsage(
                videoId = projectId, // يجب ربطه بـ Firebase video ID الفعلي
                userId = getUserId(),
                success = true,
                youtubeVideoId = youtubeVideoId
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error logging successful upload", e)
            Result.failure(e)
        }
    }

    /**
     * الحصول على معرف المستخدم
     */
    private fun getUserId(): String {
        // يمكن استخدام معرف الجهاز أو معرف المستخدم المسجل
        return android.provider.Settings.Secure.getString(
            context.contentResolver,
            android.provider.Settings.Secure.ANDROID_ID
        ) ?: "unknown_user"
    }

    /**
     * تنظيف الملفات المؤقتة
     */
    fun cleanupTempFiles() {
        try {
            val downloadDir = File(context.cacheDir, "firebase_downloads")
            if (downloadDir.exists()) {
                downloadDir.deleteRecursively()
            }
            videoDownloader.cleanupTempFiles()
        } catch (e: Exception) {
            Log.w(TAG, "Error cleaning up temp files", e)
        }
    }
}
