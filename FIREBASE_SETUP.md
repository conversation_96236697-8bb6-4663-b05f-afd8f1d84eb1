# إعداد Firebase لوكيل الفيديو الذكي

## المتطلبات الأساسية

1. **حساب Google/Firebase**
2. **مشروع Firebase جديد**
3. **قاعدة بيانات Firestore**
4. **Firebase Storage**

## خطوات الإعداد

### 1. إنشاء مشروع Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع" أو "Add project"
3. أدخل اسم المشروع: `ai-video-agent`
4. اختر إعدادات Google Analytics (اختياري)
5. انقر على "إنشاء المشروع"

### 2. إعداد Android App

1. في مشروع Firebase، انقر على أيقونة Android
2. أدخل package name: `com.example.aiagent`
3. أدخل اسم التطبيق: `AI Video Agent`
4. أدخل SHA-1 certificate fingerprint (اختياري للتطوير)
5. انقر على "تسجيل التطبيق"

### 3. تحميل ملف التكوين

1. حمل ملف `google-services.json`
2. ضع الملف في مجلد `app/` في مشروع Android
3. تأكد من أن الملف في المسار: `app/google-services.json`

### 4. إعداد Firestore Database

1. في Firebase Console، اذهب إلى "Firestore Database"
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للتطوير
4. اختر موقع قاعدة البيانات (يفضل أقرب منطقة)

### 5. إعداد Firebase Storage

1. في Firebase Console، اذهب إلى "Storage"
2. انقر على "البدء"
3. اختر "Start in test mode"
4. اختر موقع التخزين (نفس موقع Firestore)

### 6. إعداد قواعد الأمان

#### قواعد Firestore:
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قراءة الفيديوهات للجميع
    match /videos/{document} {
      allow read: if true;
      allow write: if false; // فقط من خلال Admin SDK
    }
    
    // سجل الاستخدام
    match /video_usage_logs/{document} {
      allow read, write: if true;
    }
    
    // الإحصائيات
    match /video_stats/{document} {
      allow read: if true;
      allow write: if false;
    }
  }
}
```

#### قواعد Storage:
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // قراءة الفيديوهات للجميع
    match /videos/{allPaths=**} {
      allow read: if true;
      allow write: if false;
    }
    
    // الصور المصغرة
    match /thumbnails/{allPaths=**} {
      allow read: if true;
      allow write: if false;
    }
  }
}
```

## بنية قاعدة البيانات

### مجموعة `videos`
```json
{
  "id": "video_001",
  "title": "فيديو مضحك رقم 1",
  "description": "وصف الفيديو المضحك",
  "download_url": "https://firebasestorage.googleapis.com/...",
  "thumbnail_url": "https://firebasestorage.googleapis.com/...",
  "duration": 45,
  "file_size": 8500000,
  "resolution": "720x1280",
  "category": "FUNNY",
  "tags": ["مضحك", "ترفيه", "كوميديا"],
  "language": "ar",
  "upload_date": 1703123456789,
  "view_count": 0,
  "download_count": 0,
  "rating": 4.5,
  "is_active": true,
  "source": "tiktok",
  "keywords": ["مضحك", "فيديو", "ترفيه"],
  "mood": "FUNNY",
  "age_rating": "ALL_AGES"
}
```

### مجموعة `video_usage_logs`
```json
{
  "id": "log_001",
  "video_id": "video_001",
  "user_id": "user_device_id",
  "download_date": 1703123456789,
  "upload_date": 1703123556789,
  "youtube_video_id": "dQw4w9WgXcQ",
  "success": true,
  "error_message": null
}
```

### مجموعة `video_stats`
```json
{
  "id": "global",
  "totalVideos": 100,
  "videosByCategory": {
    "FUNNY": 60,
    "EDUCATIONAL": 20,
    "ENTERTAINMENT": 20
  },
  "videosByMood": {
    "FUNNY": 60,
    "HAPPY": 30,
    "EXCITING": 10
  },
  "averageRating": 4.2,
  "totalDownloads": 1500,
  "mostPopularTags": ["مضحك", "ترفيه", "كوميديا"]
}
```

## رفع الفيديوهات إلى Firebase Storage

### 1. إنشاء مجلدات في Storage:
- `/videos/` - للفيديوهات الأساسية
- `/thumbnails/` - للصور المصغرة

### 2. تسمية الملفات:
- الفيديوهات: `video_001.mp4`, `video_002.mp4`
- الصور المصغرة: `thumb_001.jpg`, `thumb_002.jpg`

### 3. رفع الملفات:
يمكن رفع الملفات من خلال:
- Firebase Console (للاختبار)
- Firebase Admin SDK (للإنتاج)
- Firebase CLI

## إضافة بيانات تجريبية

يمكن استخدام Firebase Console لإضافة بيانات تجريبية:

1. اذهب إلى Firestore Database
2. انقر على "Start collection"
3. أدخل اسم المجموعة: `videos`
4. أضف المستندات بالبيانات المطلوبة

## نصائح مهمة

1. **الأمان**: لا تضع مفاتيح API في الكود المصدري
2. **التكلفة**: راقب استخدام Firestore و Storage لتجنب التكاليف الزائدة
3. **الأداء**: استخدم الفهرسة المناسبة للاستعلامات
4. **النسخ الاحتياطي**: قم بعمل نسخ احتياطية دورية للبيانات

## استكشاف الأخطاء

### خطأ "google-services.json not found":
- تأكد من وضع الملف في مجلد `app/`
- تأكد من أن اسم الملف صحيح

### خطأ "Permission denied":
- تحقق من قواعد الأمان في Firestore
- تأكد من أن التطبيق مسجل في Firebase

### خطأ "Network error":
- تحقق من اتصال الإنترنت
- تأكد من أن Firebase مهيأ بشكل صحيح

## الدعم

للحصول على المساعدة:
1. [Firebase Documentation](https://firebase.google.com/docs)
2. [Firebase Support](https://firebase.google.com/support)
3. [Stack Overflow](https://stackoverflow.com/questions/tagged/firebase)
