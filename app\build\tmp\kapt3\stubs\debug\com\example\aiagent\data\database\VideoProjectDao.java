package com.example.aiagent.data.database;

/**
 * DAO لإدارة مشاريع الفيديو
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000J\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0010\u0006\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u001c\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0007\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u0016\u0010\u000b\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u0014\u0010\u0013\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\t0\u00150\u0014H'J\u001a\u0010\u0016\u001a\u0004\u0018\u00010\u00172\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u0018\u0010\u0018\u001a\u0004\u0018\u00010\t2\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010\u0019\u001a\u00020\u001a2\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\u001c\u0010\u001b\u001a\b\u0012\u0004\u0012\u00020\t0\u00152\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J\"\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\t0\u00152\f\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00110\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u001eJ&\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\t0\u00152\u0006\u0010 \u001a\u00020\u00052\b\b\u0002\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010!J\u000e\u0010\"\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010#J\u000e\u0010$\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010#J\u000e\u0010%\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010#J\u0016\u0010&\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010\u000eJ\u0016\u0010'\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010(\u001a\u00020\u00032\f\u0010)\u001a\b\u0012\u0004\u0012\u00020\t0\u0015H\u00a7@\u00a2\u0006\u0002\u0010\u001eJ\u0016\u0010*\u001a\u00020\u00032\u0006\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJ\u001e\u0010+\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010,J&\u0010-\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u00112\u0006\u0010.\u001a\u00020\rH\u00a7@\u00a2\u0006\u0002\u0010/J\u001e\u00100\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u00101\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u00102J&\u00103\u001a\u00020\u00032\u0006\u0010\f\u001a\u00020\r2\u0006\u00104\u001a\u00020\r2\u0006\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u00105\u00a8\u00066"}, d2 = {"Lcom/example/aiagent/data/database/VideoProjectDao;", "", "deleteOldProjects", "", "timestamp", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProject", "project", "Lcom/example/aiagent/data/model/VideoProject;", "(Lcom/example/aiagent/data/model/VideoProject;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProjectById", "id", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteProjectsByStatus", "status", "Lcom/example/aiagent/data/model/VideoStatus;", "(Lcom/example/aiagent/data/model/VideoStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAllProjects", "Lkotlinx/coroutines/flow/Flow;", "", "getAverageUploadProgress", "", "getProjectById", "getProjectCountByStatus", "", "getProjectsByStatus", "getProjectsByStatuses", "statuses", "(Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getScheduledProjects", "currentTime", "(JLcom/example/aiagent/data/model/VideoStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTodayProjectCount", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getTotalProjectCount", "getYesterdayProjectCount", "incrementRetryCount", "insertProject", "insertProjects", "projects", "updateProject", "updateProjectStatus", "(Ljava/lang/String;Lcom/example/aiagent/data/model/VideoStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateProjectStatusWithError", "errorMessage", "(Ljava/lang/String;Lcom/example/aiagent/data/model/VideoStatus;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUploadProgress", "progress", "(Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateYouTubeVideoId", "videoId", "(Ljava/lang/String;Ljava/lang/String;Lcom/example/aiagent/data/model/VideoStatus;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
@androidx.room.Dao()
public abstract interface VideoProjectDao {
    
    @androidx.room.Query(value = "SELECT * FROM video_projects ORDER BY createdAt DESC")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<java.util.List<com.example.aiagent.data.model.VideoProject>> getAllProjects();
    
    @androidx.room.Query(value = "SELECT * FROM video_projects WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.aiagent.data.model.VideoProject> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM video_projects WHERE status = :status ORDER BY createdAt ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectsByStatus(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.aiagent.data.model.VideoProject>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM video_projects WHERE status IN (:statuses) ORDER BY createdAt ASC")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectsByStatuses(@org.jetbrains.annotations.NotNull()
    java.util.List<? extends com.example.aiagent.data.model.VideoStatus> statuses, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.aiagent.data.model.VideoProject>> $completion);
    
    @androidx.room.Query(value = "SELECT * FROM video_projects WHERE scheduledUploadTime <= :currentTime AND status = :status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getScheduledProjects(long currentTime, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.util.List<com.example.aiagent.data.model.VideoProject>> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM video_projects WHERE status = :status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getProjectCountByStatus(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM video_projects")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTotalProjectCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertProject(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoProject project, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertProjects(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.aiagent.data.model.VideoProject> projects, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateProject(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoProject project, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE video_projects SET status = :status WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateProjectStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE video_projects SET status = :status, errorMessage = :errorMessage WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateProjectStatusWithError(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status, @org.jetbrains.annotations.NotNull()
    java.lang.String errorMessage, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE video_projects SET uploadProgress = :progress WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateUploadProgress(@org.jetbrains.annotations.NotNull()
    java.lang.String id, int progress, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE video_projects SET youtubeVideoId = :videoId, status = :status WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateYouTubeVideoId(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    java.lang.String videoId, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE video_projects SET retryCount = retryCount + 1 WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object incrementRetryCount(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Delete()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteProject(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoProject project, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM video_projects WHERE id = :id")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteProjectById(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM video_projects WHERE status = :status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteProjectsByStatus(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM video_projects WHERE createdAt < :timestamp")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteOldProjects(long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "SELECT AVG(uploadProgress) FROM video_projects WHERE status = :status")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getAverageUploadProgress(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Double> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM video_projects WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now')")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getTodayProjectCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    @androidx.room.Query(value = "SELECT COUNT(*) FROM video_projects WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now', '-1 day')")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getYesterdayProjectCount(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Integer> $completion);
    
    /**
     * DAO لإدارة مشاريع الفيديو
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}