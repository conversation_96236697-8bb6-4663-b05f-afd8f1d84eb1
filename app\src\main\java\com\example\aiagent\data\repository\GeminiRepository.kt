package com.example.aiagent.data.repository

import android.util.Log
import com.example.aiagent.data.api.GeminiApiService
import com.example.aiagent.data.api.VideoContentResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مستودع Gemini AI
 * يدير التفاعل مع Gemini AI لتوليد المحتوى
 */
@Singleton
class GeminiRepository @Inject constructor(
    private val geminiApiService: GeminiApiService
) {
    
    companion object {
        private const val TAG = "GeminiRepository"
    }

    /**
     * تهيئة Gemini بمفتاح API
     */
    fun initializeGemini(apiKey: String): Boolean {
        return try {
            geminiApiService.initializeModel(apiKey)
            Log.d(TAG, "Gemini initialized successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Gemini", e)
            false
        }
    }

    /**
     * توليد محتوى شامل للفيديو
     */
    suspend fun generateVideoContent(
        videoDescription: String,
        channelName: String,
        keywords: List<String> = emptyList()
    ): Result<VideoContentResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating video content for: $videoDescription")
            
            val result = geminiApiService.generateVideoContent(
                videoDescription = videoDescription,
                channelName = channelName,
                keywords = keywords
            )
            
            if (result.isSuccess) {
                val content = result.getOrNull()!!
                Log.d(TAG, "Generated content - Title: ${content.title}")
                Result.success(content)
            } else {
                Log.e(TAG, "Failed to generate content: ${result.exceptionOrNull()?.message}")
                result
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error generating video content", e)
            Result.failure(e)
        }
    }

    /**
     * توليد عناوين متعددة للاختيار
     */
    suspend fun generateMultipleTitles(
        videoDescription: String,
        count: Int = 5
    ): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating $count titles for: $videoDescription")
            
            val result = geminiApiService.generateMultipleTitles(videoDescription, count)
            
            if (result.isSuccess) {
                val titles = result.getOrNull()!!
                Log.d(TAG, "Generated ${titles.size} titles")
                Result.success(titles)
            } else {
                Log.e(TAG, "Failed to generate titles: ${result.exceptionOrNull()?.message}")
                result
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error generating multiple titles", e)
            Result.failure(e)
        }
    }

    /**
     * توليد وصف محسن للفيديو
     */
    suspend fun generateEnhancedDescription(
        originalDescription: String,
        channelName: String,
        videoTitle: String,
        hashtags: List<String> = emptyList()
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating enhanced description")
            
            // استخدام نفس API مع تركيز على الوصف
            val result = geminiApiService.generateVideoContent(
                videoDescription = originalDescription,
                channelName = channelName,
                keywords = hashtags
            )
            
            if (result.isSuccess) {
                val content = result.getOrNull()!!
                val enhancedDescription = buildEnhancedDescription(
                    content.description,
                    channelName,
                    videoTitle,
                    hashtags
                )
                Log.d(TAG, "Generated enhanced description")
                Result.success(enhancedDescription)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("فشل في توليد الوصف"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error generating enhanced description", e)
            Result.failure(e)
        }
    }

    /**
     * توليد هاشتاغات ذكية
     */
    suspend fun generateSmartHashtags(
        videoDescription: String,
        videoTitle: String,
        category: String = "ترفيه"
    ): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating smart hashtags")
            
            val result = geminiApiService.generateVideoContent(
                videoDescription = "$videoTitle - $videoDescription - فئة: $category",
                channelName = "",
                keywords = emptyList()
            )
            
            if (result.isSuccess) {
                val content = result.getOrNull()!!
                val smartHashtags = content.hashtags.take(15) // حد أقصى 15 هاشتاغ
                Log.d(TAG, "Generated ${smartHashtags.size} hashtags")
                Result.success(smartHashtags)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("فشل في توليد الهاشتاغات"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error generating smart hashtags", e)
            Result.failure(e)
        }
    }

    /**
     * تحسين محتوى موجود
     */
    suspend fun optimizeExistingContent(
        title: String,
        description: String,
        hashtags: List<String>,
        channelName: String
    ): Result<VideoContentResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Optimizing existing content")
            
            val optimizationPrompt = """
                حسن هذا المحتوى:
                العنوان: $title
                الوصف: $description
                الهاشتاغات: ${hashtags.joinToString(", ")}
                اسم القناة: $channelName
            """.trimIndent()
            
            val result = geminiApiService.generateVideoContent(
                videoDescription = optimizationPrompt,
                channelName = channelName,
                keywords = hashtags
            )
            
            if (result.isSuccess) {
                val optimizedContent = result.getOrNull()!!
                Log.d(TAG, "Content optimized successfully")
                Result.success(optimizedContent)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("فشل في تحسين المحتوى"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error optimizing content", e)
            Result.failure(e)
        }
    }

    /**
     * توليد محتوى موسمي أو مناسبات
     */
    suspend fun generateSeasonalContent(
        baseDescription: String,
        occasion: String, // مثل "رمضان", "عيد الفطر", "رأس السنة"
        channelName: String
    ): Result<VideoContentResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating seasonal content for: $occasion")
            
            val seasonalPrompt = """
                $baseDescription
                
                اجعل المحتوى مناسباً لمناسبة: $occasion
                أضف عناصر تتعلق بهذه المناسبة في العنوان والوصف والهاشتاغات
            """.trimIndent()
            
            val result = geminiApiService.generateVideoContent(
                videoDescription = seasonalPrompt,
                channelName = channelName,
                keywords = listOf(occasion, "مناسبة", "احتفال")
            )
            
            if (result.isSuccess) {
                val seasonalContent = result.getOrNull()!!
                Log.d(TAG, "Seasonal content generated for: $occasion")
                Result.success(seasonalContent)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("فشل في توليد المحتوى الموسمي"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error generating seasonal content", e)
            Result.failure(e)
        }
    }

    /**
     * تحليل أداء المحتوى واقتراح تحسينات
     */
    suspend fun analyzeAndSuggestImprovements(
        title: String,
        description: String,
        hashtags: List<String>,
        viewCount: Int,
        likeCount: Int
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Analyzing content performance")
            
            val analysisPrompt = """
                حلل أداء هذا المحتوى واقترح تحسينات:
                
                العنوان: $title
                الوصف: $description
                الهاشتاغات: ${hashtags.joinToString(", ")}
                عدد المشاهدات: $viewCount
                عدد الإعجابات: $likeCount
                
                قدم اقتراحات محددة لتحسين الأداء
            """.trimIndent()
            
            val result = geminiApiService.generateVideoContent(
                videoDescription = analysisPrompt,
                channelName = "",
                keywords = emptyList()
            )
            
            if (result.isSuccess) {
                val analysis = result.getOrNull()!!.description
                Log.d(TAG, "Content analysis completed")
                Result.success(analysis)
            } else {
                Result.failure(result.exceptionOrNull() ?: Exception("فشل في تحليل المحتوى"))
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing content", e)
            Result.failure(e)
        }
    }

    /**
     * بناء وصف محسن
     */
    private fun buildEnhancedDescription(
        aiDescription: String,
        channelName: String,
        videoTitle: String,
        hashtags: List<String>
    ): String {
        return buildString {
            // الوصف الأساسي من AI
            appendLine(aiDescription)
            appendLine()
            
            // دعوة للتفاعل
            appendLine("🔔 لا تنسوا الاشتراك في القناة وتفعيل الجرس!")
            appendLine("👍 اضغطوا لايك إذا أعجبكم الفيديو")
            appendLine("💬 شاركونا آراءكم في التعليقات")
            appendLine()
            
            // اسم القناة
            appendLine("📺 قناة: $channelName")
            appendLine()
            
            // الهاشتاغات
            if (hashtags.isNotEmpty()) {
                appendLine("🏷️ ${hashtags.joinToString(" ")}")
            }
            
            // دعوة للمشاركة
            appendLine()
            appendLine("📤 شاركوا الفيديو مع أصدقائكم!")
        }
    }
}
