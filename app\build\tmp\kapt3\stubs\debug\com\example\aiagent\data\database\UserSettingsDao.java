package com.example.aiagent.data.database;

/**
 * DA<PERSON> لإدارة إعدادات المستخدم
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0007\bg\u0018\u00002\u00020\u0001J\u000e\u0010\u0002\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u0005\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00070\u0006H'J\u0010\u0010\b\u001a\u0004\u0018\u00010\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u0004J\u0016\u0010\t\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\f\u001a\u00020\u00032\u0006\u0010\r\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0016\u0010\u0010\u001a\u00020\u00032\u0006\u0010\u0011\u001a\u00020\u000eH\u00a7@\u00a2\u0006\u0002\u0010\u000fJ\u0016\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u0018\u0010\u0016\u001a\u00020\u00032\b\u0010\u0017\u001a\u0004\u0018\u00010\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015J\u0016\u0010\u0018\u001a\u00020\u00032\u0006\u0010\n\u001a\u00020\u0007H\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0018\u0010\u0019\u001a\u00020\u00032\b\u0010\u001a\u001a\u0004\u0018\u00010\u0014H\u00a7@\u00a2\u0006\u0002\u0010\u0015\u00a8\u0006\u001b"}, d2 = {"Lcom/example/aiagent/data/database/UserSettingsDao;", "", "deleteAllSettings", "", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSettings", "Lkotlinx/coroutines/flow/Flow;", "Lcom/example/aiagent/data/model/UserSettings;", "getSettingsOnce", "insertSettings", "settings", "(Lcom/example/aiagent/data/model/UserSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAutoUpload", "autoUpload", "", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateBatteryOptimizationRequested", "requested", "updateChannelName", "channelName", "", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGeminiApiKey", "apiKey", "updateSettings", "updateYouTubeServiceAccountPath", "path", "app_debug"})
@androidx.room.Dao()
public abstract interface UserSettingsDao {
    
    @androidx.room.Query(value = "SELECT * FROM user_settings WHERE id = 1")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.aiagent.data.model.UserSettings> getSettings();
    
    @androidx.room.Query(value = "SELECT * FROM user_settings WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getSettingsOnce(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.aiagent.data.model.UserSettings> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertSettings(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.UserSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateSettings(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.UserSettings settings, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_settings SET channelName = :channelName WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateChannelName(@org.jetbrains.annotations.NotNull()
    java.lang.String channelName, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_settings SET autoUpload = :autoUpload WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAutoUpload(boolean autoUpload, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_settings SET batteryOptimizationRequested = :requested WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateBatteryOptimizationRequested(boolean requested, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_settings SET geminiApiKey = :apiKey WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateGeminiApiKey(@org.jetbrains.annotations.Nullable()
    java.lang.String apiKey, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE user_settings SET youtubeServiceAccountPath = :path WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateYouTubeServiceAccountPath(@org.jetbrains.annotations.Nullable()
    java.lang.String path, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "DELETE FROM user_settings")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object deleteAllSettings(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}