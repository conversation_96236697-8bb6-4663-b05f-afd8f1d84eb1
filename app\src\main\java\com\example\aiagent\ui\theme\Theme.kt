package com.example.aiagent.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
    primary = YouTubeRed,
    onPrimary = TextOnDark,
    primaryContainer = YouTubeRedDark,
    onPrimaryContainer = TextOnDark,
    
    secondary = ModernBlue,
    onSecondary = TextOnDark,
    secondaryContainer = ModernBlueDark,
    onSecondaryContainer = TextOnDark,
    
    tertiary = AccentPurple,
    onTertiary = TextOnDark,
    tertiaryContainer = AccentPurple,
    onTertiaryContainer = TextOnDark,
    
    error = ErrorRed,
    onError = TextOnDark,
    errorContainer = ErrorRed,
    onErrorContainer = TextOnDark,
    
    background = BackgroundDark,
    onBackground = TextOnDark,
    surface = SurfaceDark,
    onSurface = TextOnDark,
    
    surfaceVariant = CardBackgroundDark,
    onSurfaceVariant = TextOnDark,
    outline = NeutralGray,
    outlineVariant = DarkGray
)

private val LightColorScheme = lightColorScheme(
    primary = YouTubeRed,
    onPrimary = TextOnDark,
    primaryContainer = YouTubeRedLight,
    onPrimaryContainer = TextOnLight,
    
    secondary = ModernBlue,
    onSecondary = TextOnDark,
    secondaryContainer = ModernBlueLight,
    onSecondaryContainer = TextOnLight,
    
    tertiary = AccentPurple,
    onTertiary = TextOnDark,
    tertiaryContainer = AccentPurple,
    onTertiaryContainer = TextOnDark,
    
    error = ErrorRed,
    onError = TextOnDark,
    errorContainer = ErrorRed,
    onErrorContainer = TextOnDark,
    
    background = BackgroundLight,
    onBackground = TextPrimary,
    surface = SurfaceLight,
    onSurface = TextPrimary,
    
    surfaceVariant = LightGray,
    onSurfaceVariant = TextSecondary,
    outline = NeutralGray,
    outlineVariant = LightGray
)

@Composable
fun AiAgentTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color is available on Android 12+
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }
    
    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
