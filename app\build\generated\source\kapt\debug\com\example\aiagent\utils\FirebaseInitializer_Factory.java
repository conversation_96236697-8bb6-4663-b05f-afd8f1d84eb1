package com.example.aiagent.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class FirebaseInitializer_Factory implements Factory<FirebaseInitializer> {
  private final Provider<Context> contextProvider;

  public FirebaseInitializer_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public FirebaseInitializer get() {
    return newInstance(contextProvider.get());
  }

  public static FirebaseInitializer_Factory create(Provider<Context> contextProvider) {
    return new FirebaseInitializer_Factory(contextProvider);
  }

  public static FirebaseInitializer newInstance(Context context) {
    return new FirebaseInitializer(context);
  }
}
