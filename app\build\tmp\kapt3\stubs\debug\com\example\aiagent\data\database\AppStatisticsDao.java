package com.example.aiagent.data.database;

/**
 * DA<PERSON> لإدارة إحصائيات التطبيق
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\t\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u000e\bg\u0018\u00002\u00020\u0001J\u0016\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0010\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\t0\bH'J\u0010\u0010\n\u001a\u0004\u0018\u00010\tH\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\f\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\r\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u000e\u0010\u000e\u001a\u00020\u0003H\u00a7@\u00a2\u0006\u0002\u0010\u000bJ\u0016\u0010\u000f\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0011J\u0016\u0010\u0012\u001a\u00020\u00032\u0006\u0010\u0013\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0014\u001a\u00020\u00032\u0006\u0010\u0015\u001a\u00020\u0005H\u00a7@\u00a2\u0006\u0002\u0010\u0006J\u0016\u0010\u0016\u001a\u00020\u00032\u0006\u0010\u0010\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\u0011\u00a8\u0006\u0017"}, d2 = {"Lcom/example/aiagent/data/database/AppStatisticsDao;", "", "addProcessingTime", "", "duration", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getStatistics", "Lkotlinx/coroutines/flow/Flow;", "Lcom/example/aiagent/data/model/AppStatistics;", "getStatisticsOnce", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "incrementFailedVideos", "incrementProcessedVideos", "incrementUploadedVideos", "insertStatistics", "statistics", "(Lcom/example/aiagent/data/model/AppStatistics;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAverageProcessingTime", "average", "updateLastUploadTime", "timestamp", "updateStatistics", "app_debug"})
@androidx.room.Dao()
public abstract interface AppStatisticsDao {
    
    @androidx.room.Query(value = "SELECT * FROM app_statistics WHERE id = 1")
    @org.jetbrains.annotations.NotNull()
    public abstract kotlinx.coroutines.flow.Flow<com.example.aiagent.data.model.AppStatistics> getStatistics();
    
    @androidx.room.Query(value = "SELECT * FROM app_statistics WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getStatisticsOnce(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.aiagent.data.model.AppStatistics> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object insertStatistics(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.AppStatistics statistics, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update()
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateStatistics(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.AppStatistics statistics, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE app_statistics SET totalVideosProcessed = totalVideosProcessed + 1 WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object incrementProcessedVideos(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE app_statistics SET totalVideosUploaded = totalVideosUploaded + 1 WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object incrementUploadedVideos(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE app_statistics SET totalVideosFailed = totalVideosFailed + 1 WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object incrementFailedVideos(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE app_statistics SET lastUploadTime = :timestamp WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateLastUploadTime(long timestamp, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE app_statistics SET totalProcessingTime = totalProcessingTime + :duration WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object addProcessingTime(long duration, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Query(value = "UPDATE app_statistics SET averageProcessingTime = :average WHERE id = 1")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateAverageProcessingTime(long average, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}