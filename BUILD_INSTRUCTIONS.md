# دليل بناء وتشغيل وكيل الفيديو الذكي

## 🛠️ المتطلبات الأساسية

### 1. تثبيت Android Studio
- حمل وثبت [Android Studio](https://developer.android.com/studio)
- ت<PERSON><PERSON><PERSON> من تثبيت Android SDK 34
- ثبت Android Build Tools 34.0.0

### 2. تثبيت Java Development Kit (JDK)
- ثبت JDK 17 أو أحدث
- تأكد من إعداد متغير البيئة JAVA_HOME

### 3. إعداد متغيرات البيئة
```bash
# Windows
set ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
set PATH=%PATH%;%ANDROID_HOME%\tools;%ANDROID_HOME%\platform-tools

# macOS/Linux
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools
```

## 🔧 خطوات البناء

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd ai-video-agent
```

### 2. إعداد Firebase
- اذهب إلى [Firebase Console](https://console.firebase.google.com/)
- أنشئ مشروع جديد
- أضف تطبيق Android بـ package name: `com.example.aiagent`
- حمل ملف `google-services.json`
- ضع الملف في مجلد `app/`

### 3. إعداد YouTube API
- اتبع التعليمات في [YOUTUBE_SETUP.md](YOUTUBE_SETUP.md)
- أنشئ Service Account وحمل ملف JSON

### 4. بناء التطبيق

#### باستخدام Android Studio:
1. افتح Android Studio
2. اختر "Open an existing project"
3. اختر مجلد المشروع
4. انتظر حتى ينتهي Gradle sync
5. اختر Build > Build Bundle(s) / APK(s) > Build APK(s)

#### باستخدام Command Line:
```bash
# Windows
gradlew.bat assembleDebug

# macOS/Linux
./gradlew assembleDebug
```

### 5. العثور على ملف APK
بعد البناء الناجح، ستجد ملف APK في:
```
app/build/outputs/apk/debug/app-debug.apk
```

## 📱 تثبيت التطبيق

### على جهاز Android:
1. فعل "Developer Options" و "USB Debugging"
2. وصل الجهاز بالكمبيوتر
3. استخدم ADB لتثبيت APK:
```bash
adb install app/build/outputs/apk/debug/app-debug.apk
```

### على محاكي Android:
1. افتح Android Studio
2. اذهب إلى Tools > AVD Manager
3. أنشئ محاكي جديد أو استخدم موجود
4. اسحب ملف APK إلى المحاكي

## 🚀 تشغيل التطبيق لأول مرة

### 1. الإعداد الأولي
1. افتح التطبيق
2. اذهب إلى الإعدادات
3. أدخل اسم القناة
4. أدخل Gemini API Key
5. ارفع ملف YouTube Service Account JSON

### 2. إنشاء أول فيديو
1. ارجع للشاشة الرئيسية
2. انقر على "إنشاء فيديو"
3. انتظر حتى ينتهي من المعالجة والرفع

### 3. تفعيل الوكيل الذكي
1. في الشاشة الرئيسية، فعل مفتاح "الوكيل نشط"
2. سيبدأ التطبيق في إنشاء ورفع فيديوهات تلقائياً

## 🔍 استكشاف الأخطاء

### خطأ "google-services.json not found"
- تأكد من وضع ملف google-services.json في مجلد app/
- تأكد من أن package name في الملف يطابق com.example.aiagent

### خطأ "SDK not found"
- تأكد من تثبيت Android SDK
- تحقق من متغير البيئة ANDROID_HOME

### خطأ "Java not found"
- تأكد من تثبيت JDK 17+
- تحقق من متغير البيئة JAVA_HOME

### خطأ في بناء Gradle
```bash
# نظف وأعد البناء
gradlew clean
gradlew assembleDebug
```

## 📦 إنشاء APK للإنتاج

### 1. إنشاء Keystore
```bash
keytool -genkey -v -keystore my-release-key.keystore -alias my-key-alias -keyalg RSA -keysize 2048 -validity 10000
```

### 2. إعداد signing في build.gradle
```kotlin
android {
    signingConfigs {
        release {
            storeFile file('my-release-key.keystore')
            storePassword 'your-store-password'
            keyAlias 'my-key-alias'
            keyPassword 'your-key-password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

### 3. بناء APK للإنتاج
```bash
gradlew assembleRelease
```

## 🔧 إعدادات إضافية

### تحسين الأداء
- فعل R8/ProGuard للتطبيقات الإنتاجية
- استخدم Bundle بدلاً من APK للنشر على Play Store

### الاختبار
```bash
# تشغيل الاختبارات
gradlew test

# تشغيل اختبارات UI
gradlew connectedAndroidTest
```

## 📋 قائمة التحقق قبل النشر

- [ ] اختبار جميع الوظائف
- [ ] التأكد من عمل Firebase
- [ ] التأكد من عمل YouTube API
- [ ] التأكد من عمل Gemini AI
- [ ] اختبار الرفع التلقائي
- [ ] اختبار الجدولة
- [ ] اختبار الإشعارات
- [ ] اختبار على أجهزة مختلفة
- [ ] مراجعة الصلاحيات
- [ ] مراجعة الأمان

## 🆘 الحصول على المساعدة

إذا واجهت أي مشاكل:
1. راجع ملفات الـ logs في Android Studio
2. تحقق من [Issues](https://github.com/your-repo/issues) في GitHub
3. اتصل بالدعم الفني

---

**ملاحظة**: تأكد من اتباع جميع الخطوات بالترتيب للحصول على أفضل النتائج.
