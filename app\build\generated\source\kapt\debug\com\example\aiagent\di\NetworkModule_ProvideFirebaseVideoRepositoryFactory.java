package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.data.firebase.FirebaseVideoService;
import com.example.aiagent.data.repository.FirebaseVideoRepository;
import com.example.aiagent.utils.VideoDownloader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideFirebaseVideoRepositoryFactory implements Factory<FirebaseVideoRepository> {
  private final Provider<Context> contextProvider;

  private final Provider<FirebaseVideoService> firebaseVideoServiceProvider;

  private final Provider<VideoDownloader> videoDownloaderProvider;

  public NetworkModule_ProvideFirebaseVideoRepositoryFactory(Provider<Context> contextProvider,
      Provider<FirebaseVideoService> firebaseVideoServiceProvider,
      Provider<VideoDownloader> videoDownloaderProvider) {
    this.contextProvider = contextProvider;
    this.firebaseVideoServiceProvider = firebaseVideoServiceProvider;
    this.videoDownloaderProvider = videoDownloaderProvider;
  }

  @Override
  public FirebaseVideoRepository get() {
    return provideFirebaseVideoRepository(contextProvider.get(), firebaseVideoServiceProvider.get(), videoDownloaderProvider.get());
  }

  public static NetworkModule_ProvideFirebaseVideoRepositoryFactory create(
      Provider<Context> contextProvider,
      Provider<FirebaseVideoService> firebaseVideoServiceProvider,
      Provider<VideoDownloader> videoDownloaderProvider) {
    return new NetworkModule_ProvideFirebaseVideoRepositoryFactory(contextProvider, firebaseVideoServiceProvider, videoDownloaderProvider);
  }

  public static FirebaseVideoRepository provideFirebaseVideoRepository(Context context,
      FirebaseVideoService firebaseVideoService, VideoDownloader videoDownloader) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideFirebaseVideoRepository(context, firebaseVideoService, videoDownloader));
  }
}
