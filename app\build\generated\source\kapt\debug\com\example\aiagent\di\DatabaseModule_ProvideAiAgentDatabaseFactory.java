package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.data.database.AiAgentDatabase;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvideAiAgentDatabaseFactory implements Factory<AiAgentDatabase> {
  private final Provider<Context> contextProvider;

  public DatabaseModule_ProvideAiAgentDatabaseFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public AiAgentDatabase get() {
    return provideAiAgentDatabase(contextProvider.get());
  }

  public static DatabaseModule_ProvideAiAgentDatabaseFactory create(
      Provider<Context> contextProvider) {
    return new DatabaseModule_ProvideAiAgentDatabaseFactory(contextProvider);
  }

  public static AiAgentDatabase provideAiAgentDatabase(Context context) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideAiAgentDatabase(context));
  }
}
