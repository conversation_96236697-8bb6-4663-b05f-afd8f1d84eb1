package com.example.aiagent.data.database;

import androidx.annotation.NonNull;
import androidx.room.DatabaseConfiguration;
import androidx.room.InvalidationTracker;
import androidx.room.RoomDatabase;
import androidx.room.RoomOpenHelper;
import androidx.room.migration.AutoMigrationSpec;
import androidx.room.migration.Migration;
import androidx.room.util.DBUtil;
import androidx.room.util.TableInfo;
import androidx.sqlite.db.SupportSQLiteDatabase;
import androidx.sqlite.db.SupportSQLiteOpenHelper;
import java.lang.Class;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AiAgentDatabase_Impl extends AiAgentDatabase {
  private volatile VideoProjectDao _videoProjectDao;

  private volatile UserSettingsDao _userSettingsDao;

  private volatile AppStatisticsDao _appStatisticsDao;

  private volatile OperationLogDao _operationLogDao;

  @Override
  @NonNull
  protected SupportSQLiteOpenHelper createOpenHelper(@NonNull final DatabaseConfiguration config) {
    final SupportSQLiteOpenHelper.Callback _openCallback = new RoomOpenHelper(config, new RoomOpenHelper.Delegate(1) {
      @Override
      public void createAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("CREATE TABLE IF NOT EXISTS `video_projects` (`id` TEXT NOT NULL, `title` TEXT NOT NULL, `description` TEXT NOT NULL, `hashtags` TEXT NOT NULL, `sourceVideoUrl` TEXT NOT NULL, `processedVideoPath` TEXT, `thumbnailPath` TEXT, `watermarkText` TEXT NOT NULL, `status` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `scheduledUploadTime` INTEGER, `youtubeVideoId` TEXT, `uploadProgress` INTEGER NOT NULL, `errorMessage` TEXT, `retryCount` INTEGER NOT NULL, `maxRetries` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `user_settings` (`id` INTEGER NOT NULL, `channelName` TEXT NOT NULL, `watermarkStyle` TEXT NOT NULL, `uploadSchedule` TEXT NOT NULL, `videoQuality` TEXT NOT NULL, `autoUpload` INTEGER NOT NULL, `batteryOptimizationRequested` INTEGER NOT NULL, `geminiApiKey` TEXT, `youtubeServiceAccountPath` TEXT, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `app_statistics` (`id` INTEGER NOT NULL, `totalVideosProcessed` INTEGER NOT NULL, `totalVideosUploaded` INTEGER NOT NULL, `totalVideosFailed` INTEGER NOT NULL, `lastUploadTime` INTEGER, `totalProcessingTime` INTEGER NOT NULL, `averageProcessingTime` INTEGER NOT NULL, PRIMARY KEY(`id`))");
        db.execSQL("CREATE TABLE IF NOT EXISTS `operation_logs` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `videoProjectId` TEXT NOT NULL, `operation` TEXT NOT NULL, `status` TEXT NOT NULL, `message` TEXT NOT NULL, `timestamp` INTEGER NOT NULL, `duration` INTEGER)");
        db.execSQL("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        db.execSQL("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '35176bc0cbce7a5458983d2e21e349f5')");
      }

      @Override
      public void dropAllTables(@NonNull final SupportSQLiteDatabase db) {
        db.execSQL("DROP TABLE IF EXISTS `video_projects`");
        db.execSQL("DROP TABLE IF EXISTS `user_settings`");
        db.execSQL("DROP TABLE IF EXISTS `app_statistics`");
        db.execSQL("DROP TABLE IF EXISTS `operation_logs`");
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onDestructiveMigration(db);
          }
        }
      }

      @Override
      public void onCreate(@NonNull final SupportSQLiteDatabase db) {
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onCreate(db);
          }
        }
      }

      @Override
      public void onOpen(@NonNull final SupportSQLiteDatabase db) {
        mDatabase = db;
        internalInitInvalidationTracker(db);
        final List<? extends RoomDatabase.Callback> _callbacks = mCallbacks;
        if (_callbacks != null) {
          for (RoomDatabase.Callback _callback : _callbacks) {
            _callback.onOpen(db);
          }
        }
      }

      @Override
      public void onPreMigrate(@NonNull final SupportSQLiteDatabase db) {
        DBUtil.dropFtsSyncTriggers(db);
      }

      @Override
      public void onPostMigrate(@NonNull final SupportSQLiteDatabase db) {
      }

      @Override
      @NonNull
      public RoomOpenHelper.ValidationResult onValidateSchema(
          @NonNull final SupportSQLiteDatabase db) {
        final HashMap<String, TableInfo.Column> _columnsVideoProjects = new HashMap<String, TableInfo.Column>(16);
        _columnsVideoProjects.put("id", new TableInfo.Column("id", "TEXT", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("title", new TableInfo.Column("title", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("description", new TableInfo.Column("description", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("hashtags", new TableInfo.Column("hashtags", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("sourceVideoUrl", new TableInfo.Column("sourceVideoUrl", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("processedVideoPath", new TableInfo.Column("processedVideoPath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("thumbnailPath", new TableInfo.Column("thumbnailPath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("watermarkText", new TableInfo.Column("watermarkText", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("status", new TableInfo.Column("status", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("createdAt", new TableInfo.Column("createdAt", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("scheduledUploadTime", new TableInfo.Column("scheduledUploadTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("youtubeVideoId", new TableInfo.Column("youtubeVideoId", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("uploadProgress", new TableInfo.Column("uploadProgress", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("errorMessage", new TableInfo.Column("errorMessage", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("retryCount", new TableInfo.Column("retryCount", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsVideoProjects.put("maxRetries", new TableInfo.Column("maxRetries", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysVideoProjects = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesVideoProjects = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoVideoProjects = new TableInfo("video_projects", _columnsVideoProjects, _foreignKeysVideoProjects, _indicesVideoProjects);
        final TableInfo _existingVideoProjects = TableInfo.read(db, "video_projects");
        if (!_infoVideoProjects.equals(_existingVideoProjects)) {
          return new RoomOpenHelper.ValidationResult(false, "video_projects(com.example.aiagent.data.model.VideoProject).\n"
                  + " Expected:\n" + _infoVideoProjects + "\n"
                  + " Found:\n" + _existingVideoProjects);
        }
        final HashMap<String, TableInfo.Column> _columnsUserSettings = new HashMap<String, TableInfo.Column>(9);
        _columnsUserSettings.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserSettings.put("channelName", new TableInfo.Column("channelName", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserSettings.put("watermarkStyle", new TableInfo.Column("watermarkStyle", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserSettings.put("uploadSchedule", new TableInfo.Column("uploadSchedule", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserSettings.put("videoQuality", new TableInfo.Column("videoQuality", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserSettings.put("autoUpload", new TableInfo.Column("autoUpload", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserSettings.put("batteryOptimizationRequested", new TableInfo.Column("batteryOptimizationRequested", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserSettings.put("geminiApiKey", new TableInfo.Column("geminiApiKey", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsUserSettings.put("youtubeServiceAccountPath", new TableInfo.Column("youtubeServiceAccountPath", "TEXT", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysUserSettings = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesUserSettings = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoUserSettings = new TableInfo("user_settings", _columnsUserSettings, _foreignKeysUserSettings, _indicesUserSettings);
        final TableInfo _existingUserSettings = TableInfo.read(db, "user_settings");
        if (!_infoUserSettings.equals(_existingUserSettings)) {
          return new RoomOpenHelper.ValidationResult(false, "user_settings(com.example.aiagent.data.model.UserSettings).\n"
                  + " Expected:\n" + _infoUserSettings + "\n"
                  + " Found:\n" + _existingUserSettings);
        }
        final HashMap<String, TableInfo.Column> _columnsAppStatistics = new HashMap<String, TableInfo.Column>(7);
        _columnsAppStatistics.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppStatistics.put("totalVideosProcessed", new TableInfo.Column("totalVideosProcessed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppStatistics.put("totalVideosUploaded", new TableInfo.Column("totalVideosUploaded", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppStatistics.put("totalVideosFailed", new TableInfo.Column("totalVideosFailed", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppStatistics.put("lastUploadTime", new TableInfo.Column("lastUploadTime", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppStatistics.put("totalProcessingTime", new TableInfo.Column("totalProcessingTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsAppStatistics.put("averageProcessingTime", new TableInfo.Column("averageProcessingTime", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysAppStatistics = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesAppStatistics = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoAppStatistics = new TableInfo("app_statistics", _columnsAppStatistics, _foreignKeysAppStatistics, _indicesAppStatistics);
        final TableInfo _existingAppStatistics = TableInfo.read(db, "app_statistics");
        if (!_infoAppStatistics.equals(_existingAppStatistics)) {
          return new RoomOpenHelper.ValidationResult(false, "app_statistics(com.example.aiagent.data.model.AppStatistics).\n"
                  + " Expected:\n" + _infoAppStatistics + "\n"
                  + " Found:\n" + _existingAppStatistics);
        }
        final HashMap<String, TableInfo.Column> _columnsOperationLogs = new HashMap<String, TableInfo.Column>(7);
        _columnsOperationLogs.put("id", new TableInfo.Column("id", "INTEGER", true, 1, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLogs.put("videoProjectId", new TableInfo.Column("videoProjectId", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLogs.put("operation", new TableInfo.Column("operation", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLogs.put("status", new TableInfo.Column("status", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLogs.put("message", new TableInfo.Column("message", "TEXT", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLogs.put("timestamp", new TableInfo.Column("timestamp", "INTEGER", true, 0, null, TableInfo.CREATED_FROM_ENTITY));
        _columnsOperationLogs.put("duration", new TableInfo.Column("duration", "INTEGER", false, 0, null, TableInfo.CREATED_FROM_ENTITY));
        final HashSet<TableInfo.ForeignKey> _foreignKeysOperationLogs = new HashSet<TableInfo.ForeignKey>(0);
        final HashSet<TableInfo.Index> _indicesOperationLogs = new HashSet<TableInfo.Index>(0);
        final TableInfo _infoOperationLogs = new TableInfo("operation_logs", _columnsOperationLogs, _foreignKeysOperationLogs, _indicesOperationLogs);
        final TableInfo _existingOperationLogs = TableInfo.read(db, "operation_logs");
        if (!_infoOperationLogs.equals(_existingOperationLogs)) {
          return new RoomOpenHelper.ValidationResult(false, "operation_logs(com.example.aiagent.data.model.OperationLog).\n"
                  + " Expected:\n" + _infoOperationLogs + "\n"
                  + " Found:\n" + _existingOperationLogs);
        }
        return new RoomOpenHelper.ValidationResult(true, null);
      }
    }, "35176bc0cbce7a5458983d2e21e349f5", "600db41099e13ab39f9ac1279fe70921");
    final SupportSQLiteOpenHelper.Configuration _sqliteConfig = SupportSQLiteOpenHelper.Configuration.builder(config.context).name(config.name).callback(_openCallback).build();
    final SupportSQLiteOpenHelper _helper = config.sqliteOpenHelperFactory.create(_sqliteConfig);
    return _helper;
  }

  @Override
  @NonNull
  protected InvalidationTracker createInvalidationTracker() {
    final HashMap<String, String> _shadowTablesMap = new HashMap<String, String>(0);
    final HashMap<String, Set<String>> _viewTables = new HashMap<String, Set<String>>(0);
    return new InvalidationTracker(this, _shadowTablesMap, _viewTables, "video_projects","user_settings","app_statistics","operation_logs");
  }

  @Override
  public void clearAllTables() {
    super.assertNotMainThread();
    final SupportSQLiteDatabase _db = super.getOpenHelper().getWritableDatabase();
    try {
      super.beginTransaction();
      _db.execSQL("DELETE FROM `video_projects`");
      _db.execSQL("DELETE FROM `user_settings`");
      _db.execSQL("DELETE FROM `app_statistics`");
      _db.execSQL("DELETE FROM `operation_logs`");
      super.setTransactionSuccessful();
    } finally {
      super.endTransaction();
      _db.query("PRAGMA wal_checkpoint(FULL)").close();
      if (!_db.inTransaction()) {
        _db.execSQL("VACUUM");
      }
    }
  }

  @Override
  @NonNull
  protected Map<Class<?>, List<Class<?>>> getRequiredTypeConverters() {
    final HashMap<Class<?>, List<Class<?>>> _typeConvertersMap = new HashMap<Class<?>, List<Class<?>>>();
    _typeConvertersMap.put(VideoProjectDao.class, VideoProjectDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(UserSettingsDao.class, UserSettingsDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(AppStatisticsDao.class, AppStatisticsDao_Impl.getRequiredConverters());
    _typeConvertersMap.put(OperationLogDao.class, OperationLogDao_Impl.getRequiredConverters());
    return _typeConvertersMap;
  }

  @Override
  @NonNull
  public Set<Class<? extends AutoMigrationSpec>> getRequiredAutoMigrationSpecs() {
    final HashSet<Class<? extends AutoMigrationSpec>> _autoMigrationSpecsSet = new HashSet<Class<? extends AutoMigrationSpec>>();
    return _autoMigrationSpecsSet;
  }

  @Override
  @NonNull
  public List<Migration> getAutoMigrations(
      @NonNull final Map<Class<? extends AutoMigrationSpec>, AutoMigrationSpec> autoMigrationSpecs) {
    final List<Migration> _autoMigrations = new ArrayList<Migration>();
    return _autoMigrations;
  }

  @Override
  public VideoProjectDao videoProjectDao() {
    if (_videoProjectDao != null) {
      return _videoProjectDao;
    } else {
      synchronized(this) {
        if(_videoProjectDao == null) {
          _videoProjectDao = new VideoProjectDao_Impl(this);
        }
        return _videoProjectDao;
      }
    }
  }

  @Override
  public UserSettingsDao userSettingsDao() {
    if (_userSettingsDao != null) {
      return _userSettingsDao;
    } else {
      synchronized(this) {
        if(_userSettingsDao == null) {
          _userSettingsDao = new UserSettingsDao_Impl(this);
        }
        return _userSettingsDao;
      }
    }
  }

  @Override
  public AppStatisticsDao appStatisticsDao() {
    if (_appStatisticsDao != null) {
      return _appStatisticsDao;
    } else {
      synchronized(this) {
        if(_appStatisticsDao == null) {
          _appStatisticsDao = new AppStatisticsDao_Impl(this);
        }
        return _appStatisticsDao;
      }
    }
  }

  @Override
  public OperationLogDao operationLogDao() {
    if (_operationLogDao != null) {
      return _operationLogDao;
    } else {
      synchronized(this) {
        if(_operationLogDao == null) {
          _operationLogDao = new OperationLogDao_Impl(this);
        }
        return _operationLogDao;
      }
    }
  }
}
