package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.utils.VideoDownloader;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideVideoDownloaderFactory implements Factory<VideoDownloader> {
  private final Provider<OkHttpClient> okHttpClientProvider;

  private final Provider<Context> contextProvider;

  public NetworkModule_ProvideVideoDownloaderFactory(Provider<OkHttpClient> okHttpClientProvider,
      Provider<Context> contextProvider) {
    this.okHttpClientProvider = okHttpClientProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public VideoDownloader get() {
    return provideVideoDownloader(okHttpClientProvider.get(), contextProvider.get());
  }

  public static NetworkModule_ProvideVideoDownloaderFactory create(
      Provider<OkHttpClient> okHttpClientProvider, Provider<Context> contextProvider) {
    return new NetworkModule_ProvideVideoDownloaderFactory(okHttpClientProvider, contextProvider);
  }

  public static VideoDownloader provideVideoDownloader(OkHttpClient okHttpClient, Context context) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideVideoDownloader(okHttpClient, context));
  }
}
