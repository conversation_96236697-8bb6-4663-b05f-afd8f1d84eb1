package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.utils.YouTubeAuthManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideYouTubeAuthManagerFactory implements Factory<YouTubeAuthManager> {
  private final Provider<Context> contextProvider;

  public NetworkModule_ProvideYouTubeAuthManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public YouTubeAuthManager get() {
    return provideYouTubeAuthManager(contextProvider.get());
  }

  public static NetworkModule_ProvideYouTubeAuthManagerFactory create(
      Provider<Context> contextProvider) {
    return new NetworkModule_ProvideYouTubeAuthManagerFactory(contextProvider);
  }

  public static YouTubeAuthManager provideYouTubeAuthManager(Context context) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideYouTubeAuthManager(context));
  }
}
