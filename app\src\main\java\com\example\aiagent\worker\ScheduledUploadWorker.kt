package com.example.aiagent.worker

import android.content.Context
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import androidx.work.workDataOf
import com.example.aiagent.data.model.VideoStatus
import com.example.aiagent.data.repository.VideoProjectRepository
import com.example.aiagent.service.VideoProcessingService
import com.example.aiagent.service.YouTubeUploadService
import com.example.aiagent.utils.NotificationHelper
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * عامل المهام المجدولة
 * يتعامل مع تنفيذ المهام المجدولة في الخلفية
 */
@HiltWorker
class ScheduledUploadWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val videoProjectRepository: VideoProjectRepository,
    private val notificationHelper: NotificationHelper
) : CoroutineWorker(context, workerParams) {

    companion object {
        const val KEY_PROJECT_ID = "project_id"
        const val TAG = "ScheduledUploadWorker"
    }

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            val projectId = inputData.getString(KEY_PROJECT_ID)
            
            if (projectId != null) {
                // معالجة مشروع محدد
                processSpecificProject(projectId)
            } else {
                // فحص جميع المشاريع المجدولة
                processScheduledProjects()
            }
            
            Result.success()
        } catch (e: Exception) {
            Log.e(TAG, "Error in scheduled upload worker", e)
            Result.failure(
                workDataOf(
                    "error" to (e.message ?: "Unknown error")
                )
            )
        }
    }

    /**
     * معالجة مشروع محدد
     */
    private suspend fun processSpecificProject(projectId: String) {
        try {
            val project = videoProjectRepository.getProjectById(projectId)
            if (project == null) {
                Log.w(TAG, "Project not found: $projectId")
                return
            }

            Log.d(TAG, "Processing scheduled project: ${project.title}")

            when (project.status) {
                VideoStatus.PENDING -> {
                    // بدء معالجة الفيديو
                    VideoProcessingService.startProcessing(applicationContext, projectId)
                }
                VideoStatus.READY_TO_UPLOAD -> {
                    // بدء رفع الفيديو
                    YouTubeUploadService.startUpload(applicationContext, projectId)
                }
                VideoStatus.PROCESSING, VideoStatus.UPLOADING -> {
                    // المشروع قيد التنفيذ بالفعل
                    Log.d(TAG, "Project already in progress: $projectId")
                }
                VideoStatus.UPLOADED -> {
                    // المشروع مكتمل بالفعل
                    Log.d(TAG, "Project already uploaded: $projectId")
                }
                VideoStatus.FAILED -> {
                    // إعادة المحاولة إذا لم يتم استنفاد المحاولات
                    if (project.retryCount < project.maxRetries) {
                        Log.d(TAG, "Retrying failed project: $projectId")
                        videoProjectRepository.updateProjectStatus(projectId, VideoStatus.PENDING)
                        VideoProcessingService.startProcessing(applicationContext, projectId)
                    } else {
                        Log.w(TAG, "Project failed permanently: $projectId")
                    }
                }
                else -> {
                    Log.d(TAG, "Project status not suitable for processing: ${project.status}")
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error processing specific project: $projectId", e)
            notificationHelper.showError(
                "خطأ في المعالجة المجدولة",
                "فشل في معالجة المشروع: ${e.message}"
            )
        }
    }

    /**
     * فحص ومعالجة جميع المشاريع المجدولة
     */
    private suspend fun processScheduledProjects() {
        try {
            // الحصول على المشاريع المجدولة للرفع
            val scheduledProjects = videoProjectRepository.getScheduledProjects()
            
            Log.d(TAG, "Found ${scheduledProjects.size} scheduled projects")

            if (scheduledProjects.isEmpty()) {
                return
            }

            // معالجة كل مشروع
            for (project in scheduledProjects) {
                try {
                    when (project.status) {
                        VideoStatus.PENDING -> {
                            Log.d(TAG, "Starting processing for scheduled project: ${project.title}")
                            VideoProcessingService.startProcessing(applicationContext, project.id)
                        }
                        VideoStatus.READY_TO_UPLOAD -> {
                            Log.d(TAG, "Starting upload for scheduled project: ${project.title}")
                            YouTubeUploadService.startUpload(applicationContext, project.id)
                        }
                        else -> {
                            Log.d(TAG, "Skipping project with status ${project.status}: ${project.title}")
                        }
                    }
                    
                    // تأخير قصير بين المشاريع لتجنب الحمل الزائد
                    kotlinx.coroutines.delay(1000)
                    
                } catch (e: Exception) {
                    Log.e(TAG, "Error processing scheduled project: ${project.id}", e)
                    // الاستمرار مع المشاريع الأخرى
                }
            }

            // إشعار بعدد المشاريع المعالجة
            if (scheduledProjects.isNotEmpty()) {
                notificationHelper.showInfo(
                    "معالجة مجدولة",
                    "تم بدء معالجة ${scheduledProjects.size} مشروع مجدول"
                )
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error processing scheduled projects", e)
            notificationHelper.showError(
                "خطأ في المعالجة المجدولة",
                "فشل في فحص المشاريع المجدولة: ${e.message}"
            )
        }
    }
}
