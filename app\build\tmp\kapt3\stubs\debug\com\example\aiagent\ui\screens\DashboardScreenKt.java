package com.example.aiagent.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u0007\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\u001a&\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0006\u0010\u0004\u001a\u00020\u00052\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00010\u0007H\u0003\u001a\u001a\u0010\b\u001a\u00020\u00012\u0006\u0010\t\u001a\u00020\n2\b\b\u0002\u0010\u000b\u001a\u00020\fH\u0007\u001a(\u0010\r\u001a\u00020\u00012\u0006\u0010\u000e\u001a\u00020\u000f2\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0011\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u0013H\u0003\u001a\u0010\u0010\u0014\u001a\u00020\u00012\u0006\u0010\u0015\u001a\u00020\u0016H\u0003\u00a8\u0006\u0017"}, d2 = {"DashboardHeader", "", "channelName", "", "isAgentActive", "", "onToggleAgent", "Lkotlin/Function0;", "DashboardScreen", "navController", "Landroidx/navigation/NavController;", "viewModel", "Lcom/example/aiagent/ui/viewmodel/DashboardViewModel;", "QuickStatsSection", "totalVideos", "", "uploadedToday", "pendingVideos", "successRate", "", "StatCardItem", "stat", "Lcom/example/aiagent/ui/screens/StatCard;", "app_debug"})
public final class DashboardScreenKt {
    
    /**
     * شاشة الرئيسية - Dashboard
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void DashboardScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.ui.viewmodel.DashboardViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void DashboardHeader(java.lang.String channelName, boolean isAgentActive, kotlin.jvm.functions.Function0<kotlin.Unit> onToggleAgent) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void QuickStatsSection(int totalVideos, int uploadedToday, int pendingVideos, float successRate) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatCardItem(com.example.aiagent.ui.screens.StatCard stat) {
    }
}