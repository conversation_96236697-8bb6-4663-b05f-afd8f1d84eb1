package com.example.aiagent.di

import android.content.Context
import com.example.aiagent.data.api.GeminiApiService
import com.example.aiagent.data.api.YouTubeApiService
import com.example.aiagent.data.firebase.FirebaseVideoService
import com.example.aiagent.data.repository.GeminiRepository
import com.example.aiagent.data.repository.YouTubeRepository
import com.example.aiagent.data.repository.FirebaseVideoRepository
import com.example.aiagent.data.repository.UserSettingsRepository
import com.example.aiagent.utils.VideoDownloader
import com.example.aiagent.utils.SmartContentManager
import com.example.aiagent.utils.YouTubeSetupManager
import com.example.aiagent.utils.NotificationHelper
import com.example.aiagent.utils.SchedulerManager
import com.example.aiagent.core.AiAgentManager
import com.google.ai.client.generativeai.GenerativeModel
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Singleton

/**
 * وحدة Hilt للشبكة والـ APIs
 */
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    @Provides
    @Singleton
    fun provideOkHttpClient(): OkHttpClient {
        val loggingInterceptor = HttpLoggingInterceptor().apply {
            level = HttpLoggingInterceptor.Level.BODY
        }

        return OkHttpClient.Builder()
            .addInterceptor(loggingInterceptor)
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .build()
    }

    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl("https://www.googleapis.com/")
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }

    @Provides
    @Singleton
    fun provideYouTubeApiService(retrofit: Retrofit): YouTubeApiService {
        return retrofit.create(YouTubeApiService::class.java)
    }

    @Provides
    @Singleton
    fun provideGeminiApiService(@ApplicationContext context: Context): GeminiApiService {
        return GeminiApiService(context)
    }

    @Provides
    @Singleton
    fun provideYouTubeRepository(
        apiService: YouTubeApiService,
        @ApplicationContext context: Context
    ): YouTubeRepository {
        return YouTubeRepository(apiService, context)
    }

    @Provides
    @Singleton
    fun provideGeminiRepository(
        apiService: GeminiApiService
    ): GeminiRepository {
        return GeminiRepository(apiService)
    }

    @Provides
    @Singleton
    fun provideVideoDownloader(
        okHttpClient: OkHttpClient,
        @ApplicationContext context: Context
    ): VideoDownloader {
        return VideoDownloader(context, okHttpClient)
    }

    @Provides
    @Singleton
    fun provideFirebaseVideoService(): FirebaseVideoService {
        return FirebaseVideoService()
    }

    @Provides
    @Singleton
    fun provideFirebaseVideoRepository(
        @ApplicationContext context: Context,
        firebaseVideoService: FirebaseVideoService,
        videoDownloader: VideoDownloader
    ): FirebaseVideoRepository {
        return FirebaseVideoRepository(context, firebaseVideoService, videoDownloader)
    }

    @Provides
    @Singleton
    fun provideSmartContentManager(
        @ApplicationContext context: Context,
        geminiRepository: GeminiRepository
    ): SmartContentManager {
        return SmartContentManager(context, geminiRepository)
    }

    @Provides
    @Singleton
    fun provideUserSettingsRepository(
        userSettingsDao: com.example.aiagent.data.database.UserSettingsDao
    ): UserSettingsRepository {
        return UserSettingsRepository(userSettingsDao)
    }

    @Provides
    @Singleton
    fun provideNotificationHelper(
        @ApplicationContext context: Context
    ): NotificationHelper {
        return NotificationHelper(context)
    }

    @Provides
    @Singleton
    fun provideSchedulerManager(
        @ApplicationContext context: Context
    ): SchedulerManager {
        return SchedulerManager(context)
    }

    @Provides
    @Singleton
    fun provideYouTubeSetupManager(
        @ApplicationContext context: Context,
        youTubeAuthManager: com.example.aiagent.utils.YouTubeAuthManager,
        notificationHelper: NotificationHelper
    ): YouTubeSetupManager {
        return YouTubeSetupManager(context, youTubeAuthManager, notificationHelper)
    }

    @Provides
    @Singleton
    fun provideYouTubeAuthManager(
        @ApplicationContext context: Context
    ): com.example.aiagent.utils.YouTubeAuthManager {
        return com.example.aiagent.utils.YouTubeAuthManager(context)
    }

    @Provides
    @Singleton
    fun provideAiAgentManager(
        @ApplicationContext context: Context,
        firebaseVideoRepository: FirebaseVideoRepository,
        videoProjectRepository: com.example.aiagent.data.repository.VideoProjectRepository,
        geminiRepository: GeminiRepository,
        schedulerManager: SchedulerManager,
        notificationHelper: NotificationHelper,
        smartContentManager: SmartContentManager
    ): AiAgentManager {
        return AiAgentManager(
            context,
            firebaseVideoRepository,
            videoProjectRepository,
            geminiRepository,
            schedulerManager,
            notificationHelper,
            smartContentManager
        )
    }
}
