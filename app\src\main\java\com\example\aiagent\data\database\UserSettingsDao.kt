package com.example.aiagent.data.database

import androidx.room.*
import com.example.aiagent.data.model.UserSettings
import kotlinx.coroutines.flow.Flow

/**
 * DAO لإدارة إعدادات المستخدم
 */
@Dao
interface UserSettingsDao {
    
    @Query("SELECT * FROM user_settings WHERE id = 1")
    fun getSettings(): Flow<UserSettings?>
    
    @Query("SELECT * FROM user_settings WHERE id = 1")
    suspend fun getSettingsOnce(): UserSettings?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSettings(settings: UserSettings)
    
    @Update
    suspend fun updateSettings(settings: UserSettings)
    
    @Query("UPDATE user_settings SET channelName = :channelName WHERE id = 1")
    suspend fun updateChannelName(channelName: String)
    
    @Query("UPDATE user_settings SET autoUpload = :autoUpload WHERE id = 1")
    suspend fun updateAutoUpload(autoUpload: Boolean)
    
    @Query("UPDATE user_settings SET batteryOptimizationRequested = :requested WHERE id = 1")
    suspend fun updateBatteryOptimizationRequested(requested: Boolean)
    
    @Query("UPDATE user_settings SET geminiApiKey = :apiKey WHERE id = 1")
    suspend fun updateGeminiApiKey(apiKey: String?)
    
    @Query("UPDATE user_settings SET youtubeServiceAccountPath = :path WHERE id = 1")
    suspend fun updateYouTubeServiceAccountPath(path: String?)
    
    @Query("DELETE FROM user_settings")
    suspend fun deleteAllSettings()
}

/**
 * DAO لإدارة إحصائيات التطبيق
 */
@Dao
interface AppStatisticsDao {
    
    @Query("SELECT * FROM app_statistics WHERE id = 1")
    fun getStatistics(): Flow<com.example.aiagent.data.model.AppStatistics?>
    
    @Query("SELECT * FROM app_statistics WHERE id = 1")
    suspend fun getStatisticsOnce(): com.example.aiagent.data.model.AppStatistics?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertStatistics(statistics: com.example.aiagent.data.model.AppStatistics)
    
    @Update
    suspend fun updateStatistics(statistics: com.example.aiagent.data.model.AppStatistics)
    
    @Query("UPDATE app_statistics SET totalVideosProcessed = totalVideosProcessed + 1 WHERE id = 1")
    suspend fun incrementProcessedVideos()
    
    @Query("UPDATE app_statistics SET totalVideosUploaded = totalVideosUploaded + 1 WHERE id = 1")
    suspend fun incrementUploadedVideos()
    
    @Query("UPDATE app_statistics SET totalVideosFailed = totalVideosFailed + 1 WHERE id = 1")
    suspend fun incrementFailedVideos()
    
    @Query("UPDATE app_statistics SET lastUploadTime = :timestamp WHERE id = 1")
    suspend fun updateLastUploadTime(timestamp: Long)
    
    @Query("UPDATE app_statistics SET totalProcessingTime = totalProcessingTime + :duration WHERE id = 1")
    suspend fun addProcessingTime(duration: Long)
    
    @Query("UPDATE app_statistics SET averageProcessingTime = :average WHERE id = 1")
    suspend fun updateAverageProcessingTime(average: Long)
}

/**
 * DAO لإدارة سجل العمليات
 */
@Dao
interface OperationLogDao {
    
    @Query("SELECT * FROM operation_logs ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentLogs(limit: Int = 100): Flow<List<com.example.aiagent.data.model.OperationLog>>
    
    @Query("SELECT * FROM operation_logs WHERE videoProjectId = :projectId ORDER BY timestamp DESC")
    suspend fun getLogsByProjectId(projectId: String): List<com.example.aiagent.data.model.OperationLog>
    
    @Query("SELECT * FROM operation_logs WHERE status = :status ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getLogsByStatus(status: String, limit: Int = 50): List<com.example.aiagent.data.model.OperationLog>
    
    @Insert
    suspend fun insertLog(log: com.example.aiagent.data.model.OperationLog)
    
    @Insert
    suspend fun insertLogs(logs: List<com.example.aiagent.data.model.OperationLog>)
    
    @Query("DELETE FROM operation_logs WHERE timestamp < :timestamp")
    suspend fun deleteOldLogs(timestamp: Long)
    
    @Query("DELETE FROM operation_logs WHERE videoProjectId = :projectId")
    suspend fun deleteLogsByProjectId(projectId: String)
    
    @Query("SELECT COUNT(*) FROM operation_logs")
    suspend fun getLogCount(): Int
}
