package com.example.aiagent.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.aiagent.data.model.AppStatistics;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class AppStatisticsDao_Impl implements AppStatisticsDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<AppStatistics> __insertionAdapterOfAppStatistics;

  private final EntityDeletionOrUpdateAdapter<AppStatistics> __updateAdapterOfAppStatistics;

  private final SharedSQLiteStatement __preparedStmtOfIncrementProcessedVideos;

  private final SharedSQLiteStatement __preparedStmtOfIncrementUploadedVideos;

  private final SharedSQLiteStatement __preparedStmtOfIncrementFailedVideos;

  private final SharedSQLiteStatement __preparedStmtOfUpdateLastUploadTime;

  private final SharedSQLiteStatement __preparedStmtOfAddProcessingTime;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAverageProcessingTime;

  public AppStatisticsDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfAppStatistics = new EntityInsertionAdapter<AppStatistics>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `app_statistics` (`id`,`totalVideosProcessed`,`totalVideosUploaded`,`totalVideosFailed`,`lastUploadTime`,`totalProcessingTime`,`averageProcessingTime`) VALUES (?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AppStatistics entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTotalVideosProcessed());
        statement.bindLong(3, entity.getTotalVideosUploaded());
        statement.bindLong(4, entity.getTotalVideosFailed());
        if (entity.getLastUploadTime() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getLastUploadTime());
        }
        statement.bindLong(6, entity.getTotalProcessingTime());
        statement.bindLong(7, entity.getAverageProcessingTime());
      }
    };
    this.__updateAdapterOfAppStatistics = new EntityDeletionOrUpdateAdapter<AppStatistics>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `app_statistics` SET `id` = ?,`totalVideosProcessed` = ?,`totalVideosUploaded` = ?,`totalVideosFailed` = ?,`lastUploadTime` = ?,`totalProcessingTime` = ?,`averageProcessingTime` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final AppStatistics entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getTotalVideosProcessed());
        statement.bindLong(3, entity.getTotalVideosUploaded());
        statement.bindLong(4, entity.getTotalVideosFailed());
        if (entity.getLastUploadTime() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getLastUploadTime());
        }
        statement.bindLong(6, entity.getTotalProcessingTime());
        statement.bindLong(7, entity.getAverageProcessingTime());
        statement.bindLong(8, entity.getId());
      }
    };
    this.__preparedStmtOfIncrementProcessedVideos = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE app_statistics SET totalVideosProcessed = totalVideosProcessed + 1 WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementUploadedVideos = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE app_statistics SET totalVideosUploaded = totalVideosUploaded + 1 WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementFailedVideos = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE app_statistics SET totalVideosFailed = totalVideosFailed + 1 WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateLastUploadTime = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE app_statistics SET lastUploadTime = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfAddProcessingTime = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE app_statistics SET totalProcessingTime = totalProcessingTime + ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAverageProcessingTime = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE app_statistics SET averageProcessingTime = ? WHERE id = 1";
        return _query;
      }
    };
  }

  @Override
  public Object insertStatistics(final AppStatistics statistics,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfAppStatistics.insert(statistics);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateStatistics(final AppStatistics statistics,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfAppStatistics.handle(statistics);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object incrementProcessedVideos(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementProcessedVideos.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfIncrementProcessedVideos.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object incrementUploadedVideos(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementUploadedVideos.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfIncrementUploadedVideos.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object incrementFailedVideos(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementFailedVideos.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfIncrementFailedVideos.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateLastUploadTime(final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateLastUploadTime.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateLastUploadTime.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object addProcessingTime(final long duration,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfAddProcessingTime.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, duration);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfAddProcessingTime.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAverageProcessingTime(final long average,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAverageProcessingTime.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, average);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAverageProcessingTime.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<AppStatistics> getStatistics() {
    final String _sql = "SELECT * FROM app_statistics WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"app_statistics"}, new Callable<AppStatistics>() {
      @Override
      @Nullable
      public AppStatistics call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTotalVideosProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "totalVideosProcessed");
          final int _cursorIndexOfTotalVideosUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "totalVideosUploaded");
          final int _cursorIndexOfTotalVideosFailed = CursorUtil.getColumnIndexOrThrow(_cursor, "totalVideosFailed");
          final int _cursorIndexOfLastUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUploadTime");
          final int _cursorIndexOfTotalProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalProcessingTime");
          final int _cursorIndexOfAverageProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "averageProcessingTime");
          final AppStatistics _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpTotalVideosProcessed;
            _tmpTotalVideosProcessed = _cursor.getInt(_cursorIndexOfTotalVideosProcessed);
            final int _tmpTotalVideosUploaded;
            _tmpTotalVideosUploaded = _cursor.getInt(_cursorIndexOfTotalVideosUploaded);
            final int _tmpTotalVideosFailed;
            _tmpTotalVideosFailed = _cursor.getInt(_cursorIndexOfTotalVideosFailed);
            final Long _tmpLastUploadTime;
            if (_cursor.isNull(_cursorIndexOfLastUploadTime)) {
              _tmpLastUploadTime = null;
            } else {
              _tmpLastUploadTime = _cursor.getLong(_cursorIndexOfLastUploadTime);
            }
            final long _tmpTotalProcessingTime;
            _tmpTotalProcessingTime = _cursor.getLong(_cursorIndexOfTotalProcessingTime);
            final long _tmpAverageProcessingTime;
            _tmpAverageProcessingTime = _cursor.getLong(_cursorIndexOfAverageProcessingTime);
            _result = new AppStatistics(_tmpId,_tmpTotalVideosProcessed,_tmpTotalVideosUploaded,_tmpTotalVideosFailed,_tmpLastUploadTime,_tmpTotalProcessingTime,_tmpAverageProcessingTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getStatisticsOnce(final Continuation<? super AppStatistics> $completion) {
    final String _sql = "SELECT * FROM app_statistics WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<AppStatistics>() {
      @Override
      @Nullable
      public AppStatistics call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTotalVideosProcessed = CursorUtil.getColumnIndexOrThrow(_cursor, "totalVideosProcessed");
          final int _cursorIndexOfTotalVideosUploaded = CursorUtil.getColumnIndexOrThrow(_cursor, "totalVideosUploaded");
          final int _cursorIndexOfTotalVideosFailed = CursorUtil.getColumnIndexOrThrow(_cursor, "totalVideosFailed");
          final int _cursorIndexOfLastUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "lastUploadTime");
          final int _cursorIndexOfTotalProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "totalProcessingTime");
          final int _cursorIndexOfAverageProcessingTime = CursorUtil.getColumnIndexOrThrow(_cursor, "averageProcessingTime");
          final AppStatistics _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final int _tmpTotalVideosProcessed;
            _tmpTotalVideosProcessed = _cursor.getInt(_cursorIndexOfTotalVideosProcessed);
            final int _tmpTotalVideosUploaded;
            _tmpTotalVideosUploaded = _cursor.getInt(_cursorIndexOfTotalVideosUploaded);
            final int _tmpTotalVideosFailed;
            _tmpTotalVideosFailed = _cursor.getInt(_cursorIndexOfTotalVideosFailed);
            final Long _tmpLastUploadTime;
            if (_cursor.isNull(_cursorIndexOfLastUploadTime)) {
              _tmpLastUploadTime = null;
            } else {
              _tmpLastUploadTime = _cursor.getLong(_cursorIndexOfLastUploadTime);
            }
            final long _tmpTotalProcessingTime;
            _tmpTotalProcessingTime = _cursor.getLong(_cursorIndexOfTotalProcessingTime);
            final long _tmpAverageProcessingTime;
            _tmpAverageProcessingTime = _cursor.getLong(_cursorIndexOfAverageProcessingTime);
            _result = new AppStatistics(_tmpId,_tmpTotalVideosProcessed,_tmpTotalVideosUploaded,_tmpTotalVideosFailed,_tmpLastUploadTime,_tmpTotalProcessingTime,_tmpAverageProcessingTime);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
