&com.example.aiagent.AiAgentApplication com.example.aiagent.MainActivity=com.example.aiagent.data.api.VideoContentResponse.$serializer1com.example.aiagent.data.database.AiAgentDatabase8com.example.aiagent.data.model.FirebaseVideo.$serializer,com.example.aiagent.data.model.VideoCategory(com.example.aiagent.data.model.VideoMood(com.example.aiagent.data.model.AgeRating>com.example.aiagent.data.model.VideoSearchCriteria.$serializer5com.example.aiagent.data.model.VideoStats.$serializer8com.example.aiagent.data.model.VideoUsageLog.$serializer7com.example.aiagent.data.model.VideoProject.$serializer*com.example.aiagent.data.model.VideoStatus7com.example.aiagent.data.model.UserSettings.$serializer-com.example.aiagent.data.model.WatermarkStyle9com.example.aiagent.data.model.UploadSchedule.$serializer+com.example.aiagent.data.model.VideoQuality8com.example.aiagent.data.model.AppStatistics.$serializer7com.example.aiagent.data.model.OperationLog.$serializer*com.example.aiagent.receiver.AlarmReceiver)com.example.aiagent.receiver.BootReceiver2com.example.aiagent.service.VideoProcessingService0com.example.aiagent.service.YouTubeUploadService3com.example.aiagent.ui.viewmodel.DashboardViewModel2com.example.aiagent.ui.viewmodel.SettingsViewModel-com.example.aiagent.utils.ProgressRequestBody:com.example.aiagent.utils.ProgressRequestBody.ProgressSink&com.example.aiagent.utils.VideoQuality,com.example.aiagent.utils.YouTubeSetupStatus0com.example.aiagent.worker.ScheduledUploadWorker                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    