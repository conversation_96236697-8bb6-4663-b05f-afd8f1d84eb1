{"logs": [{"outputFile": "com.example.aiagent.app-mergeDebugResources-88:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1fd568d5a1c4f4d2aea1aab03507053\\transformed\\ui-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,386,487,573,650,741,833,918,998,1083,1156,1233,1311,1387,1466,1536", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "197,284,381,482,568,645,736,828,913,993,1078,1151,1228,1306,1382,1461,1531,1649"}, "to": {"startLines": "68,69,88,89,90,146,147,264,265,267,268,272,274,275,276,278,279,280", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5325,5422,7679,7776,7877,12000,12077,23128,23220,23385,23465,23796,23946,24023,24101,24278,24357,24427", "endColumns": "96,86,96,100,85,76,90,91,84,79,84,72,76,77,75,78,69,117", "endOffsets": "5417,5504,7771,7872,7958,12072,12163,23215,23300,23460,23545,23864,24018,24096,24172,24352,24422,24540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7478e3cc73cddc4127d485bacc71e97\\transformed\\play-services-base-18.5.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "70,71,72,73,74,75,76,77,79,80,81,82,83,84,85,86,87", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5509,5615,5771,5897,6007,6161,6288,6400,6632,6781,6888,7048,7175,7324,7466,7534,7599", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "5610,5766,5892,6002,6156,6283,6395,6497,6776,6883,7043,7170,7319,7461,7529,7594,7674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c1cbac527d871b7e6d27789e9f88bba3\\transformed\\foundation-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,89", "endOffsets": "138,228"}, "to": {"startLines": "281,282", "startColumns": "4,4", "startOffsets": "24545,24633", "endColumns": "87,89", "endOffsets": "24628,24718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d592e4e76d99b11c1d7a6fa57b286ce0\\transformed\\appcompat-1.7.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,269", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1133,1240,1341,1447,1533,1637,1759,1843,1924,2015,2108,2203,2297,2397,2490,2585,2690,2781,2872,2958,3063,3169,3272,3378,3487,3594,3764,23550", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "1235,1336,1442,1528,1632,1754,1838,1919,2010,2103,2198,2292,2392,2485,2580,2685,2776,2867,2953,3058,3164,3267,3373,3482,3589,3759,3856,23632"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ddb98e863a1a5c2e78bcacdf9de3476\\transformed\\core-1.16.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "58,59,60,61,62,63,64,277", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4293,4391,4493,4590,4694,4798,4903,24177", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "4386,4488,4585,4689,4793,4898,5014,24273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e773aa8fe135cf8e64f2a724d4a97259\\transformed\\exoplayer-core-2.19.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,190,255,326,404,476,563,646", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "124,185,250,321,399,471,558,641,714"}, "to": {"startLines": "117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10054,10128,10189,10254,10325,10403,10475,10562,10645", "endColumns": "73,60,64,70,77,71,86,82,72", "endOffsets": "10123,10184,10249,10320,10398,10470,10557,10640,10713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ba371c8e6e80451905d49b7ece1194da\\transformed\\play-services-basement-18.4.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "78", "startColumns": "4", "startOffsets": "6502", "endColumns": "129", "endOffsets": "6627"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\81e789d1c3cb03c2f66092857ae417ac\\transformed\\material3-release\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,287,414,530,628,722,833,969,1088,1230,1315,1415,1510,1608,1724,1849,1954,2095,2235,2368,2548,2673,2793,2918,3040,3136,3234,3351,3481,3581,3683,3792,3934,4083,4192,4295,4372,4470,4568,4677,4766,4852,4959,5039,5122,5219,5322,5415,5513,5600,5708,5805,5907,6040,6120,6227", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "166,282,409,525,623,717,828,964,1083,1225,1310,1410,1505,1603,1719,1844,1949,2090,2230,2363,2543,2668,2788,2913,3035,3131,3229,3346,3476,3576,3678,3787,3929,4078,4187,4290,4367,4465,4563,4672,4761,4847,4954,5034,5117,5214,5317,5410,5508,5595,5703,5800,5902,6035,6115,6222,6319"}, "to": {"startLines": "149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12231,12347,12463,12590,12706,12804,12898,13009,13145,13264,13406,13491,13591,13686,13784,13900,14025,14130,14271,14411,14544,14724,14849,14969,15094,15216,15312,15410,15527,15657,15757,15859,15968,16110,16259,16368,16471,16548,16646,16744,16853,16942,17028,17135,17215,17298,17395,17498,17591,17689,17776,17884,17981,18083,18216,18296,18403", "endColumns": "115,115,126,115,97,93,110,135,118,141,84,99,94,97,115,124,104,140,139,132,179,124,119,124,121,95,97,116,129,99,101,108,141,148,108,102,76,97,97,108,88,85,106,79,82,96,102,92,97,86,107,96,101,132,79,106,96", "endOffsets": "12342,12458,12585,12701,12799,12893,13004,13140,13259,13401,13486,13586,13681,13779,13895,14020,14125,14266,14406,14539,14719,14844,14964,15089,15211,15307,15405,15522,15652,15752,15854,15963,16105,16254,16363,16466,16543,16641,16739,16848,16937,17023,17130,17210,17293,17390,17493,17586,17684,17771,17879,17976,18078,18211,18291,18398,18495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\74b501ce892fe2a4ee48d34996dbeeea\\transformed\\material-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1121,1187,1279,1347,1410,1513,1573,1639,1695,1766,1826,1880,1992,2049,2110,2164,2240,2365,2451,2528,2621,2705,2788,2926,3007,3090,3221,3309,3387,3441,3497,3563,3637,3715,3786,3868,3943,4019,4094,4165,4272,4362,4435,4527,4623,4695,4771,4867,4920,5002,5069,5156,5243,5305,5369,5432,5501,5606,5716,5812,5920,5978,6038,6118,6201,6277", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,75,75,79,106,92,93,130,80,61,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "316,392,468,548,655,748,842,973,1054,1116,1182,1274,1342,1405,1508,1568,1634,1690,1761,1821,1875,1987,2044,2105,2159,2235,2360,2446,2523,2616,2700,2783,2921,3002,3085,3216,3304,3382,3436,3492,3558,3632,3710,3781,3863,3938,4014,4089,4160,4267,4357,4430,4522,4618,4690,4766,4862,4915,4997,5064,5151,5238,5300,5364,5427,5496,5601,5711,5807,5915,5973,6033,6113,6196,6272,6349"}, "to": {"startLines": "21,53,54,55,56,57,65,66,67,91,92,144,145,148,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,266,270,271,273", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "912,3861,3937,4013,4093,4200,5019,5113,5244,7963,8025,11840,11932,12168,18500,18603,18663,18729,18785,18856,18916,18970,19082,19139,19200,19254,19330,19455,19541,19618,19711,19795,19878,20016,20097,20180,20311,20399,20477,20531,20587,20653,20727,20805,20876,20958,21033,21109,21184,21255,21362,21452,21525,21617,21713,21785,21861,21957,22010,22092,22159,22246,22333,22395,22459,22522,22591,22696,22806,22902,23010,23068,23305,23637,23720,23869", "endLines": "25,53,54,55,56,57,65,66,67,91,92,144,145,148,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,266,270,271,273", "endColumns": "12,75,75,79,106,92,93,130,80,61,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "1128,3932,4008,4088,4195,4288,5108,5239,5320,8020,8086,11927,11995,12226,18598,18658,18724,18780,18851,18911,18965,19077,19134,19195,19249,19325,19450,19536,19613,19706,19790,19873,20011,20092,20175,20306,20394,20472,20526,20582,20648,20722,20800,20871,20953,21028,21104,21179,21250,21357,21447,21520,21612,21708,21780,21856,21952,22005,22087,22154,22241,22328,22390,22454,22517,22586,22691,22801,22897,23005,23063,23123,23380,23715,23791,23941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0a10126c0c2cfe9f025de992aa0245b6\\transformed\\exoplayer-ui-2.19.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,11,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,556,817,898,980,1062,1151,1242,1312,1378,1471,1565,1633,1697,1760,1832,1939,2049,2158,2234,2321,2394,2465,2556,2648,2715,2780,2833,2891,2939,3000,3066,3130,3193,3258,3322,3383,3449,3514,3580,3632,3694,3770,3846", "endLines": "10,15,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,106,109,108,75,86,72,70,90,91,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "282,551,812,893,975,1057,1146,1237,1307,1373,1466,1560,1628,1692,1755,1827,1934,2044,2153,2229,2316,2389,2460,2551,2643,2710,2775,2828,2886,2934,2995,3061,3125,3188,3253,3317,3378,3444,3509,3575,3627,3689,3765,3841,3897"}, "to": {"startLines": "2,11,16,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,651,8091,8172,8254,8336,8425,8516,8586,8652,8745,8839,8907,8971,9034,9106,9213,9323,9432,9508,9595,9668,9739,9830,9922,9989,10718,10771,10829,10877,10938,11004,11068,11131,11196,11260,11321,11387,11452,11518,11570,11632,11708,11784", "endLines": "10,15,20,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143", "endColumns": "17,12,12,80,81,81,88,90,69,65,92,93,67,63,62,71,106,109,108,75,86,72,70,90,91,66,64,52,57,47,60,65,63,62,64,63,60,65,64,65,51,61,75,75,55", "endOffsets": "377,646,907,8167,8249,8331,8420,8511,8581,8647,8740,8834,8902,8966,9029,9101,9208,9318,9427,9503,9590,9663,9734,9825,9917,9984,10049,10766,10824,10872,10933,10999,11063,11126,11191,11255,11316,11382,11447,11513,11565,11627,11703,11779,11835"}}]}]}