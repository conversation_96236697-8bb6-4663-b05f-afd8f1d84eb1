package com.example.aiagent.data.model;

/**
 * فئات الفيديو
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u0012\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\fj\u0002\b\rj\u0002\b\u000ej\u0002\b\u000fj\u0002\b\u0010j\u0002\b\u0011j\u0002\b\u0012\u00a8\u0006\u0013"}, d2 = {"Lcom/example/aiagent/data/model/VideoCategory;", "", "(Ljava/lang/String;I)V", "FUNNY", "EDUCATIONAL", "ENTERTAINMENT", "SPORTS", "MUSIC", "COOKING", "TRAVEL", "TECHNOLOGY", "ANIMALS", "KIDS", "LIFESTYLE", "NEWS", "GAMING", "FASHION", "HEALTH", "OTHER", "app_debug"})
public enum VideoCategory {
    /*public static final*/ FUNNY /* = new FUNNY() */,
    /*public static final*/ EDUCATIONAL /* = new EDUCATIONAL() */,
    /*public static final*/ ENTERTAINMENT /* = new ENTERTAINMENT() */,
    /*public static final*/ SPORTS /* = new SPORTS() */,
    /*public static final*/ MUSIC /* = new MUSIC() */,
    /*public static final*/ COOKING /* = new COOKING() */,
    /*public static final*/ TRAVEL /* = new TRAVEL() */,
    /*public static final*/ TECHNOLOGY /* = new TECHNOLOGY() */,
    /*public static final*/ ANIMALS /* = new ANIMALS() */,
    /*public static final*/ KIDS /* = new KIDS() */,
    /*public static final*/ LIFESTYLE /* = new LIFESTYLE() */,
    /*public static final*/ NEWS /* = new NEWS() */,
    /*public static final*/ GAMING /* = new GAMING() */,
    /*public static final*/ FASHION /* = new FASHION() */,
    /*public static final*/ HEALTH /* = new HEALTH() */,
    /*public static final*/ OTHER /* = new OTHER() */;
    
    VideoCategory() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.aiagent.data.model.VideoCategory> getEntries() {
        return null;
    }
}