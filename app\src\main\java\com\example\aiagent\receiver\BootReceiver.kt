package com.example.aiagent.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.example.aiagent.worker.ScheduledUploadWorker
import com.example.aiagent.utils.SchedulerManager
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

/**
 * مستقبل إعادة تشغيل الجهاز
 * يعيد تشغيل المهام المجدولة بعد إعادة تشغيل الجهاز
 */
@AndroidEntryPoint
class BootReceiver : BroadcastReceiver() {

    @Inject
    lateinit var schedulerManager: SchedulerManager

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                Log.d("BootReceiver", "Devi<PERSON> booted or app updated, restarting scheduled tasks")
                
                try {
                    // إعادة جدولة المهام المؤجلة
                    restartScheduledTasks(context)
                    
                    // إعادة تشغيل مراقب الجدولة
                    schedulerManager.restartScheduler()
                    
                } catch (e: Exception) {
                    Log.e("BootReceiver", "Error restarting scheduled tasks", e)
                }
            }
        }
    }

    /**
     * إعادة تشغيل المهام المجدولة
     */
    private fun restartScheduledTasks(context: Context) {
        val workManager = WorkManager.getInstance(context)
        
        // إنشاء مهمة فورية للتحقق من المشاريع المجدولة
        val checkScheduledWork = OneTimeWorkRequestBuilder<ScheduledUploadWorker>()
            .addTag("boot_check")
            .build()
        
        workManager.enqueueUniqueWork(
            "boot_scheduled_check",
            ExistingWorkPolicy.REPLACE,
            checkScheduledWork
        )
        
        Log.d("BootReceiver", "Scheduled upload check work enqueued")
    }
}
