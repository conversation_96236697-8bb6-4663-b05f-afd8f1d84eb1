# 🚀 دليل البدء السريع - وكيل الفيديو الذكي

## 📱 تثبيت سريع (للاختبار)

### الطريقة الأولى: استخدام Android Studio (الموصى بها)

1. **تحميل وتثبيت Android Studio**
   - حمل من: https://developer.android.com/studio
   - ثبت مع Android SDK 34

2. **فتح المشروع**
   - افتح Android Studio
   - اختر "Open an existing project"
   - اختر مجلد `ai-video-agent`

3. **إعداد Firebase (مؤقت للاختبار)**
   - ملف `google-services.json` موجود مسبقاً للاختبار
   - للاستخدام الفعلي، استبدله بملفك من Firebase Console

4. **بناء التطبيق**
   - انتظر انتهاء Gradle Sync
   - اختر Build > Build Bundle(s) / APK(s) > Build APK(s)
   - ملف APK سيكون في: `app/build/outputs/apk/debug/`

### الطريقة الثانية: استخدام Command Line

```bash
# في مجلد المشروع
cd ai-video-agent

# بناء APK للتطوير
./gradlew assembleDebug

# أو على Windows
gradlew.bat assembleDebug
```

## 🔧 الإعداد السريع

### 1. تثبيت APK
```bash
# تثبيت على جهاز متصل
adb install app/build/outputs/apk/debug/app-debug.apk

# أو اسحب APK إلى محاكي Android
```

### 2. الإعداد الأولي في التطبيق

#### أ. إعدادات أساسية
1. افتح التطبيق
2. اذهب إلى "الإعدادات"
3. أدخل اسم القناة: `قناة الاختبار`

#### ب. إعداد Gemini AI (اختياري للاختبار)
1. احصل على API Key من: https://makersuite.google.com/
2. أدخله في إعدادات الذكاء الاصطناعي
3. أو اتركه فارغاً للاختبار بدون AI

#### ج. إعداد YouTube (للاختبار المتقدم)
1. اتبع دليل [YOUTUBE_SETUP.md](YOUTUBE_SETUP.md)
2. أو اتركه للاختبار المحلي فقط

## 🎮 اختبار سريع

### 1. اختبار الواجهة
- تصفح جميع الشاشات
- جرب الأزرار والقوائم
- تحقق من الثيم والألوان

### 2. اختبار المعالجة (بدون رفع)
1. في الشاشة الرئيسية، انقر "إنشاء فيديو"
2. راقب التقدم في الإشعارات
3. تحقق من حالة المشروع

### 3. اختبار الإعدادات
- غير إعدادات مختلفة
- جرب تفعيل/إلغاء الوكيل الذكي
- اختبر فترات الرفع

## 📁 هيكل الملفات المهمة

```
ai-video-agent/
├── app/
│   ├── build/outputs/apk/debug/    # ملفات APK
│   ├── google-services.json        # إعدادات Firebase
│   └── src/main/                   # كود المصدر
├── BUILD_INSTRUCTIONS.md           # دليل البناء التفصيلي
├── FIREBASE_SETUP.md              # دليل إعداد Firebase
├── YOUTUBE_SETUP.md               # دليل إعداد YouTube
└── README.md                      # دليل المشروع الشامل
```

## 🔍 استكشاف الأخطاء السريع

### مشكلة: Gradle Sync فشل
```bash
# حل سريع
./gradlew clean
./gradlew build --refresh-dependencies
```

### مشكلة: APK لا يعمل
- تأكد من تفعيل "Unknown Sources" في الأندرويد
- تحقق من إصدار Android (يحتاج 7.0+)

### مشكلة: التطبيق يتوقف
- تحقق من Logcat في Android Studio
- تأكد من الصلاحيات في الإعدادات

## 🎯 نصائح للاختبار

### 1. اختبار بدون إنترنت
- جرب فتح التطبيق بدون إنترنت
- تحقق من رسائل الخطأ

### 2. اختبار الذاكرة
- راقب استهلاك الذاكرة
- جرب إنشاء عدة فيديوهات

### 3. اختبار البطارية
- راقب استهلاك البطارية
- جرب تشغيل الوكيل لفترة طويلة

## 📞 الدعم السريع

### مشاكل شائعة وحلولها:

| المشكلة | الحل السريع |
|---------|-------------|
| Gradle بطيء | أضف `org.gradle.parallel=true` في gradle.properties |
| نفاد الذاكرة | زد `org.gradle.jvmargs=-Xmx4g` |
| APK كبير | فعل ProGuard في build.gradle |
| التطبيق بطيء | قلل جودة الفيديو في الإعدادات |

### للمساعدة الفورية:
1. تحقق من Android Studio Logcat
2. راجع ملف README.md الشامل
3. اتبع دليل BUILD_INSTRUCTIONS.md

---

## 🎉 مبروك!

إذا وصلت هنا، فالتطبيق جاهز للاختبار! 

**الخطوة التالية**: جرب إنشاء أول فيديو واستمتع بالوكيل الذكي! 🤖📱
