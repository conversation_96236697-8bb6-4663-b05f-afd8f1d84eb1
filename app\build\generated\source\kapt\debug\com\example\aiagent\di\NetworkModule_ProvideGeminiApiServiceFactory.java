package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.data.api.GeminiApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideGeminiApiServiceFactory implements Factory<GeminiApiService> {
  private final Provider<Context> contextProvider;

  public NetworkModule_ProvideGeminiApiServiceFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public GeminiApiService get() {
    return provideGeminiApiService(contextProvider.get());
  }

  public static NetworkModule_ProvideGeminiApiServiceFactory create(
      Provider<Context> contextProvider) {
    return new NetworkModule_ProvideGeminiApiServiceFactory(contextProvider);
  }

  public static GeminiApiService provideGeminiApiService(Context context) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideGeminiApiService(context));
  }
}
