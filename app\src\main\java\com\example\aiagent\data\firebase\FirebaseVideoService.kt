package com.example.aiagent.data.firebase

import android.util.Log
import com.example.aiagent.data.model.*
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

/**
 * خدمة Firebase للفيديوهات
 * تتعامل مع جلب وإدارة الفيديوهات من قاعدة بيانات Firebase
 */
@Singleton
class FirebaseVideoService @Inject constructor() {
    
    private val firestore = FirebaseFirestore.getInstance()
    private val videosCollection = firestore.collection("videos")
    private val usageLogsCollection = firestore.collection("video_usage_logs")
    private val statsCollection = firestore.collection("video_stats")
    
    companion object {
        private const val TAG = "FirebaseVideoService"
    }

    /**
     * جلب فيديو عشوائي حسب المعايير
     */
    suspend fun getRandomVideo(criteria: VideoSearchCriteria): Result<FirebaseVideo?> {
        return try {
            Log.d(TAG, "Fetching random video with criteria: $criteria")
            
            var query: Query = videosCollection
                .whereEqualTo("is_active", true)
            
            // تطبيق المعايير
            criteria.category?.let { 
                query = query.whereEqualTo("category", it.name)
            }
            
            criteria.mood?.let {
                query = query.whereEqualTo("mood", it.name)
            }
            
            criteria.language?.let {
                query = query.whereEqualTo("language", it)
            }
            
            criteria.maxDuration?.let {
                query = query.whereLessThanOrEqualTo("duration", it)
            }
            
            criteria.minRating?.let {
                query = query.whereGreaterThanOrEqualTo("rating", it)
            }
            
            // تطبيق الحد الأقصى
            query = query.limit(criteria.limit.toLong())
            
            val querySnapshot = query.get().await()
            val videos = querySnapshot.toObjects(FirebaseVideo::class.java)
            
            // فلترة الفيديوهات المستخدمة إذا كان مطلوباً
            val filteredVideos = if (criteria.excludeUsedVideos) {
                filterUnusedVideos(videos)
            } else {
                videos
            }
            
            // اختيار فيديو عشوائي
            val randomVideo = filteredVideos.randomOrNull()
            
            Log.d(TAG, "Found ${filteredVideos.size} videos, selected: ${randomVideo?.id}")
            Result.success(randomVideo)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching random video", e)
            Result.failure(e)
        }
    }

    /**
     * جلب عدة فيديوهات حسب المعايير
     */
    suspend fun getVideos(criteria: VideoSearchCriteria): Result<List<FirebaseVideo>> {
        return try {
            Log.d(TAG, "Fetching videos with criteria: $criteria")
            
            var query: Query = videosCollection
                .whereEqualTo("is_active", true)
                .orderBy("rating", Query.Direction.DESCENDING)
            
            // تطبيق المعايير (نفس المنطق السابق)
            criteria.category?.let { 
                query = query.whereEqualTo("category", it.name)
            }
            
            criteria.mood?.let {
                query = query.whereEqualTo("mood", it.name)
            }
            
            criteria.language?.let {
                query = query.whereEqualTo("language", it)
            }
            
            criteria.maxDuration?.let {
                query = query.whereLessThanOrEqualTo("duration", it)
            }
            
            criteria.minRating?.let {
                query = query.whereGreaterThanOrEqualTo("rating", it)
            }
            
            query = query.limit(criteria.limit.toLong())
            
            val querySnapshot = query.get().await()
            val videos = querySnapshot.toObjects(FirebaseVideo::class.java)
            
            val filteredVideos = if (criteria.excludeUsedVideos) {
                filterUnusedVideos(videos)
            } else {
                videos
            }
            
            Log.d(TAG, "Found ${filteredVideos.size} videos")
            Result.success(filteredVideos)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching videos", e)
            Result.failure(e)
        }
    }

    /**
     * البحث في الفيديوهات بالكلمات المفتاحية
     */
    suspend fun searchVideos(
        searchQuery: String,
        criteria: VideoSearchCriteria
    ): Result<List<FirebaseVideo>> {
        return try {
            Log.d(TAG, "Searching videos with query: $searchQuery")
            
            // البحث في العناوين والكلمات المفتاحية
            val titleQuery = videosCollection
                .whereEqualTo("is_active", true)
                .whereGreaterThanOrEqualTo("title", searchQuery)
                .whereLessThanOrEqualTo("title", searchQuery + "\uf8ff")
                .limit(criteria.limit.toLong())
            
            val keywordQuery = videosCollection
                .whereEqualTo("is_active", true)
                .whereArrayContains("keywords", searchQuery)
                .limit(criteria.limit.toLong())
            
            // تنفيذ الاستعلامات
            val titleResults = titleQuery.get().await().toObjects(FirebaseVideo::class.java)
            val keywordResults = keywordQuery.get().await().toObjects(FirebaseVideo::class.java)
            
            // دمج النتائج وإزالة المكررات
            val allResults = (titleResults + keywordResults).distinctBy { it.id }
            
            val filteredVideos = if (criteria.excludeUsedVideos) {
                filterUnusedVideos(allResults)
            } else {
                allResults
            }
            
            Log.d(TAG, "Search found ${filteredVideos.size} videos")
            Result.success(filteredVideos.take(criteria.limit))
            
        } catch (e: Exception) {
            Log.e(TAG, "Error searching videos", e)
            Result.failure(e)
        }
    }

    /**
     * تسجيل استخدام فيديو
     */
    suspend fun logVideoUsage(
        videoId: String,
        userId: String,
        success: Boolean = true,
        youtubeVideoId: String? = null,
        errorMessage: String? = null
    ): Result<Unit> {
        return try {
            val usageLog = VideoUsageLog(
                videoId = videoId,
                userId = userId,
                downloadDate = System.currentTimeMillis(),
                uploadDate = if (success) System.currentTimeMillis() else null,
                youtubeVideoId = youtubeVideoId,
                success = success,
                errorMessage = errorMessage
            )
            
            usageLogsCollection.add(usageLog).await()
            
            // تحديث عداد التحميل
            if (success) {
                updateVideoDownloadCount(videoId)
            }
            
            Log.d(TAG, "Video usage logged for video: $videoId")
            Result.success(Unit)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error logging video usage", e)
            Result.failure(e)
        }
    }

    /**
     * الحصول على إحصائيات الفيديوهات
     */
    suspend fun getVideoStats(): Result<VideoStats> {
        return try {
            val statsDoc = statsCollection.document("global").get().await()
            
            if (statsDoc.exists()) {
                val stats = statsDoc.toObject(VideoStats::class.java) ?: VideoStats()
                Result.success(stats)
            } else {
                // إنشاء إحصائيات جديدة
                val newStats = calculateStats()
                Result.success(newStats)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching video stats", e)
            Result.failure(e)
        }
    }

    /**
     * الحصول على الفيديوهات الأكثر شعبية
     */
    suspend fun getPopularVideos(limit: Int = 10): Result<List<FirebaseVideo>> {
        return try {
            val query = videosCollection
                .whereEqualTo("is_active", true)
                .orderBy("download_count", Query.Direction.DESCENDING)
                .orderBy("rating", Query.Direction.DESCENDING)
                .limit(limit.toLong())
            
            val querySnapshot = query.get().await()
            val videos = querySnapshot.toObjects(FirebaseVideo::class.java)
            
            Result.success(videos)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error fetching popular videos", e)
            Result.failure(e)
        }
    }

    /**
     * فلترة الفيديوهات غير المستخدمة
     */
    private suspend fun filterUnusedVideos(videos: List<FirebaseVideo>): List<FirebaseVideo> {
        return try {
            // الحصول على قائمة الفيديوهات المستخدمة مؤخراً
            val recentUsageQuery = usageLogsCollection
                .whereGreaterThan("download_date", System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)) // آخر 7 أيام
                .whereEqualTo("success", true)
            
            val usedVideoIds = recentUsageQuery.get().await()
                .toObjects(VideoUsageLog::class.java)
                .map { it.videoId }
                .toSet()
            
            videos.filter { it.id !in usedVideoIds }
        } catch (e: Exception) {
            Log.w(TAG, "Error filtering unused videos, returning all", e)
            videos
        }
    }

    /**
     * تحديث عداد التحميل للفيديو
     */
    private suspend fun updateVideoDownloadCount(videoId: String) {
        try {
            val videoRef = videosCollection.document(videoId)
            firestore.runTransaction { transaction ->
                val snapshot = transaction.get(videoRef)
                val currentCount = snapshot.getLong("download_count") ?: 0
                transaction.update(videoRef, "download_count", currentCount + 1)
            }.await()
        } catch (e: Exception) {
            Log.w(TAG, "Error updating download count for video: $videoId", e)
        }
    }

    /**
     * حساب الإحصائيات
     */
    private suspend fun calculateStats(): VideoStats {
        return try {
            val allVideos = videosCollection
                .whereEqualTo("is_active", true)
                .get().await()
                .toObjects(FirebaseVideo::class.java)
            
            val videosByCategory = allVideos.groupBy { it.category }.mapValues { it.value.size }
            val videosByMood = allVideos.groupBy { it.mood }.mapValues { it.value.size }
            val averageRating = allVideos.map { it.rating }.average().toFloat()
            val totalDownloads = allVideos.sumOf { it.downloadCount }
            
            // أكثر التاغات شعبية
            val allTags = allVideos.flatMap { it.tags }
            val mostPopularTags = allTags.groupBy { it }
                .mapValues { it.value.size }
                .toList()
                .sortedByDescending { it.second }
                .take(10)
                .map { it.first }
            
            VideoStats(
                totalVideos = allVideos.size,
                videosByCategory = videosByCategory,
                videosByMood = videosByMood,
                averageRating = averageRating,
                totalDownloads = totalDownloads,
                mostPopularTags = mostPopularTags
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating stats", e)
            VideoStats()
        }
    }
}
