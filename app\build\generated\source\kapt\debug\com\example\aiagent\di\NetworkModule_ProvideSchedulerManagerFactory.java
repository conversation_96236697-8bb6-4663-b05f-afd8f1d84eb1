package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.utils.SchedulerManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideSchedulerManagerFactory implements Factory<SchedulerManager> {
  private final Provider<Context> contextProvider;

  public NetworkModule_ProvideSchedulerManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SchedulerManager get() {
    return provideSchedulerManager(contextProvider.get());
  }

  public static NetworkModule_ProvideSchedulerManagerFactory create(
      Provider<Context> contextProvider) {
    return new NetworkModule_ProvideSchedulerManagerFactory(contextProvider);
  }

  public static SchedulerManager provideSchedulerManager(Context context) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideSchedulerManager(context));
  }
}
