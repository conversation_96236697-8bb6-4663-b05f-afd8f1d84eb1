package com.example.aiagent.data.repository;

/**
 * مستودع Firebase للفيديوهات
 * يدير جلب وتحميل الفيديوهات من Firebase
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000d\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\t\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\f\b\u0007\u0018\u0000 72\u00020\u0001:\u00017B!\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\t\u001a\u00020\nJ<\u0010\u000b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u000e0\r0\f2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u00122\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0015\u0010\u0016J6\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u000e0\f2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0018\u001a\u00020\u00192\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001a\u0010\u001bJ.\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u000e0\f2\u0006\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001d\u0010\u001eJ:\u0010\u001f\u001a\b\u0012\u0004\u0012\u00020\u00100\f2\u0006\u0010 \u001a\u00020!2\u0014\b\u0002\u0010\"\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\n0#H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b$\u0010%J,\u0010&\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\r0\f2\b\b\u0002\u0010'\u001a\u00020\u0012H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b(\u0010)J\b\u0010*\u001a\u00020\u0010H\u0002J\u001c\u0010+\u001a\b\u0012\u0004\u0012\u00020,0\fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b-\u0010.J,\u0010/\u001a\b\u0012\u0004\u0012\u00020\n0\f2\u0006\u00100\u001a\u00020\u00102\u0006\u00101\u001a\u00020\u0010H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b2\u00103J4\u00104\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020!0\r0\f2\u0006\u00105\u001a\u00020\u00102\b\b\u0002\u0010\u0013\u001a\u00020\u0014H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u0010\u001eR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00068"}, d2 = {"Lcom/example/aiagent/data/repository/FirebaseVideoRepository;", "", "context", "Landroid/content/Context;", "firebaseVideoService", "Lcom/example/aiagent/data/firebase/FirebaseVideoService;", "videoDownloader", "Lcom/example/aiagent/utils/VideoDownloader;", "(Landroid/content/Context;Lcom/example/aiagent/data/firebase/FirebaseVideoService;Lcom/example/aiagent/utils/VideoDownloader;)V", "cleanupTempFiles", "", "createMultipleVideoProjects", "Lkotlin/Result;", "", "Lcom/example/aiagent/data/model/VideoProject;", "channelName", "", "count", "", "criteria", "Lcom/example/aiagent/data/model/VideoSearchCriteria;", "createMultipleVideoProjects-BWLJW6A", "(Ljava/lang/String;ILcom/example/aiagent/data/model/VideoSearchCriteria;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createScheduledVideoProject", "scheduledTime", "", "createScheduledVideoProject-BWLJW6A", "(Ljava/lang/String;JLcom/example/aiagent/data/model/VideoSearchCriteria;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "createVideoProjectFromFirebase", "createVideoProjectFromFirebase-0E7RQCE", "(Ljava/lang/String;Lcom/example/aiagent/data/model/VideoSearchCriteria;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "downloadVideoFromFirebase", "firebaseVideo", "Lcom/example/aiagent/data/model/FirebaseVideo;", "onProgress", "Lkotlin/Function1;", "downloadVideoFromFirebase-0E7RQCE", "(Lcom/example/aiagent/data/model/FirebaseVideo;Lkotlin/jvm/functions/Function1;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getPopularVideos", "limit", "getPopularVideos-gIAlu-s", "(ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getUserId", "getVideoStatistics", "Lcom/example/aiagent/data/model/VideoStats;", "getVideoStatistics-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "logSuccessfulUpload", "projectId", "youtubeVideoId", "logSuccessfulUpload-0E7RQCE", "(Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "searchFirebaseVideos", "query", "searchFirebaseVideos-0E7RQCE", "Companion", "app_debug"})
public final class FirebaseVideoRepository {
    @org.jetbrains.annotations.NotNull()
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.data.firebase.FirebaseVideoService firebaseVideoService = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.utils.VideoDownloader videoDownloader = null;
    @org.jetbrains.annotations.NotNull()
    private static final java.lang.String TAG = "FirebaseVideoRepository";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.aiagent.data.repository.FirebaseVideoRepository.Companion Companion = null;
    
    @javax.inject.Inject()
    public FirebaseVideoRepository(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.firebase.FirebaseVideoService firebaseVideoService, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.VideoDownloader videoDownloader) {
        super();
    }
    
    /**
     * الحصول على معرف المستخدم
     */
    private final java.lang.String getUserId() {
        return null;
    }
    
    /**
     * تنظيف الملفات المؤقتة
     */
    public final void cleanupTempFiles() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0012\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0005"}, d2 = {"Lcom/example/aiagent/data/repository/FirebaseVideoRepository$Companion;", "", "()V", "TAG", "", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}