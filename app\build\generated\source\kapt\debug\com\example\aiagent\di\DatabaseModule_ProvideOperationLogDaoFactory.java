package com.example.aiagent.di;

import com.example.aiagent.data.database.AiAgentDatabase;
import com.example.aiagent.data.database.OperationLogDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvideOperationLogDaoFactory implements Factory<OperationLogDao> {
  private final Provider<AiAgentDatabase> databaseProvider;

  public DatabaseModule_ProvideOperationLogDaoFactory(Provider<AiAgentDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public OperationLogDao get() {
    return provideOperationLogDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideOperationLogDaoFactory create(
      Provider<AiAgentDatabase> databaseProvider) {
    return new DatabaseModule_ProvideOperationLogDaoFactory(databaseProvider);
  }

  public static OperationLogDao provideOperationLogDao(AiAgentDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideOperationLogDao(database));
  }
}
