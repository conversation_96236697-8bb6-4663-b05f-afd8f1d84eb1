package com.example.aiagent.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\n\n\u0000\n\u0002\u0018\u0002\n\u0002\bH\"\u0013\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0002\u0010\u0003\"\u0013\u0010\u0005\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0006\u0010\u0003\"\u0013\u0010\u0007\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\b\u0010\u0003\"\u0013\u0010\t\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\n\u0010\u0003\"\u0013\u0010\u000b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\f\u0010\u0003\"\u0013\u0010\r\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u000e\u0010\u0003\"\u0013\u0010\u000f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0010\u0010\u0003\"\u0013\u0010\u0011\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0012\u0010\u0003\"\u0013\u0010\u0013\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0014\u0010\u0003\"\u0013\u0010\u0015\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0016\u0010\u0003\"\u0013\u0010\u0017\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u0018\u0010\u0003\"\u0013\u0010\u0019\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001a\u0010\u0003\"\u0013\u0010\u001b\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001c\u0010\u0003\"\u0013\u0010\u001d\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\u001e\u0010\u0003\"\u0013\u0010\u001f\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b \u0010\u0003\"\u0013\u0010!\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b\"\u0010\u0003\"\u0013\u0010#\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b$\u0010\u0003\"\u0013\u0010%\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b&\u0010\u0003\"\u0013\u0010'\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b(\u0010\u0003\"\u0013\u0010)\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b*\u0010\u0003\"\u0013\u0010+\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b,\u0010\u0003\"\u0013\u0010-\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b.\u0010\u0003\"\u0013\u0010/\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b0\u0010\u0003\"\u0013\u00101\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b2\u0010\u0003\"\u0013\u00103\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b4\u0010\u0003\"\u0013\u00105\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b6\u0010\u0003\"\u0013\u00107\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b8\u0010\u0003\"\u0013\u00109\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b:\u0010\u0003\"\u0013\u0010;\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b<\u0010\u0003\"\u0013\u0010=\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b>\u0010\u0003\"\u0013\u0010?\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\b@\u0010\u0003\"\u0013\u0010A\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bB\u0010\u0003\"\u0013\u0010C\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bD\u0010\u0003\"\u0013\u0010E\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bF\u0010\u0003\"\u0013\u0010G\u001a\u00020\u0001\u00a2\u0006\n\n\u0002\u0010\u0004\u001a\u0004\bH\u0010\u0003\u00a8\u0006I"}, d2 = {"AccentBlue", "Landroidx/compose/ui/graphics/Color;", "getAccentBlue", "()J", "J", "AccentGreen", "getAccentGreen", "AccentOrange", "getAccentOrange", "AccentPurple", "getAccentPurple", "BackgroundDark", "getBackgroundDark", "BackgroundLight", "getBackgroundLight", "ButtonDisabled", "getButtonDisabled", "ButtonPrimary", "getButtonPrimary", "ButtonSecondary", "getButtonSecondary", "CardBackground", "getCardBackground", "CardBackgroundDark", "getCardBackgroundDark", "CardElevation", "getCardElevation", "DarkGray", "getDarkGray", "ErrorRed", "getErrorRed", "GradientEnd", "getGradientEnd", "GradientStart", "getGradientStart", "InfoBlue", "getInfoBlue", "LightGray", "getLightGray", "ModernBlue", "getModernBlue", "ModernBlueDark", "getModernBlueDark", "ModernBlueLight", "getModernBlueLight", "NeutralGray", "getNeutralGray", "ProgressBackground", "getProgressBackground", "ProgressForeground", "getProgressForeground", "SuccessGreen", "getSuccessGreen", "SurfaceDark", "getSurfaceDark", "SurfaceLight", "getSurfaceLight", "TextOnDark", "getTextOnDark", "TextOnLight", "getTextOnLight", "TextPrimary", "getTextPrimary", "TextSecondary", "getTextSecondary", "WarningOrange", "getWarningOrange", "YouTubeRed", "getYouTubeRed", "YouTubeRedDark", "getYouTubeRedDark", "YouTubeRedLight", "getYouTubeRedLight", "app_debug"})
public final class ColorKt {
    private static final long YouTubeRed = 0L;
    private static final long YouTubeRedDark = 0L;
    private static final long YouTubeRedLight = 0L;
    private static final long ModernBlue = 0L;
    private static final long ModernBlueDark = 0L;
    private static final long ModernBlueLight = 0L;
    private static final long AccentGreen = 0L;
    private static final long AccentOrange = 0L;
    private static final long AccentPurple = 0L;
    private static final long AccentBlue = 0L;
    private static final long NeutralGray = 0L;
    private static final long LightGray = 0L;
    private static final long DarkGray = 0L;
    private static final long BackgroundLight = 0L;
    private static final long BackgroundDark = 0L;
    private static final long SurfaceLight = 0L;
    private static final long SurfaceDark = 0L;
    private static final long TextPrimary = 0L;
    private static final long TextSecondary = 0L;
    private static final long TextOnDark = 0L;
    private static final long TextOnLight = 0L;
    private static final long SuccessGreen = 0L;
    private static final long WarningOrange = 0L;
    private static final long ErrorRed = 0L;
    private static final long InfoBlue = 0L;
    private static final long GradientStart = 0L;
    private static final long GradientEnd = 0L;
    private static final long CardBackground = 0L;
    private static final long CardBackgroundDark = 0L;
    private static final long CardElevation = 0L;
    private static final long ButtonPrimary = 0L;
    private static final long ButtonSecondary = 0L;
    private static final long ButtonDisabled = 0L;
    private static final long ProgressBackground = 0L;
    private static final long ProgressForeground = 0L;
    
    public static final long getYouTubeRed() {
        return 0L;
    }
    
    public static final long getYouTubeRedDark() {
        return 0L;
    }
    
    public static final long getYouTubeRedLight() {
        return 0L;
    }
    
    public static final long getModernBlue() {
        return 0L;
    }
    
    public static final long getModernBlueDark() {
        return 0L;
    }
    
    public static final long getModernBlueLight() {
        return 0L;
    }
    
    public static final long getAccentGreen() {
        return 0L;
    }
    
    public static final long getAccentOrange() {
        return 0L;
    }
    
    public static final long getAccentPurple() {
        return 0L;
    }
    
    public static final long getAccentBlue() {
        return 0L;
    }
    
    public static final long getNeutralGray() {
        return 0L;
    }
    
    public static final long getLightGray() {
        return 0L;
    }
    
    public static final long getDarkGray() {
        return 0L;
    }
    
    public static final long getBackgroundLight() {
        return 0L;
    }
    
    public static final long getBackgroundDark() {
        return 0L;
    }
    
    public static final long getSurfaceLight() {
        return 0L;
    }
    
    public static final long getSurfaceDark() {
        return 0L;
    }
    
    public static final long getTextPrimary() {
        return 0L;
    }
    
    public static final long getTextSecondary() {
        return 0L;
    }
    
    public static final long getTextOnDark() {
        return 0L;
    }
    
    public static final long getTextOnLight() {
        return 0L;
    }
    
    public static final long getSuccessGreen() {
        return 0L;
    }
    
    public static final long getWarningOrange() {
        return 0L;
    }
    
    public static final long getErrorRed() {
        return 0L;
    }
    
    public static final long getInfoBlue() {
        return 0L;
    }
    
    public static final long getGradientStart() {
        return 0L;
    }
    
    public static final long getGradientEnd() {
        return 0L;
    }
    
    public static final long getCardBackground() {
        return 0L;
    }
    
    public static final long getCardBackgroundDark() {
        return 0L;
    }
    
    public static final long getCardElevation() {
        return 0L;
    }
    
    public static final long getButtonPrimary() {
        return 0L;
    }
    
    public static final long getButtonSecondary() {
        return 0L;
    }
    
    public static final long getButtonDisabled() {
        return 0L;
    }
    
    public static final long getProgressBackground() {
        return 0L;
    }
    
    public static final long getProgressForeground() {
        return 0L;
    }
}