package com.example.aiagent.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.example.aiagent.worker.ScheduledUploadWorker
import dagger.hilt.android.AndroidEntryPoint

/**
 * مستقبل المنبهات المجدولة
 * يتعامل مع تنفيذ المهام في الأوقات المحددة
 */
@AndroidEntryPoint
class AlarmReceiver : BroadcastReceiver() {

    companion object {
        const val ACTION_SCHEDULED_UPLOAD = "com.example.aiagent.SCHEDULED_UPLOAD"
        const val EXTRA_PROJECT_ID = "project_id"
    }

    override fun onReceive(context: Context, intent: Intent) {
        when (intent.action) {
            ACTION_SCHEDULED_UPLOAD -> {
                val projectId = intent.getStringExtra(EXTRA_PROJECT_ID)
                Log.d("AlarmReceiver", "Scheduled upload alarm received for project: $projectId")
                
                if (projectId != null) {
                    triggerScheduledUpload(context, projectId)
                } else {
                    // إذا لم يكن هناك مشروع محدد، تحقق من جميع المشاريع المجدولة
                    triggerScheduledCheck(context)
                }
            }
        }
    }

    /**
     * تشغيل رفع مجدول لمشروع محدد
     */
    private fun triggerScheduledUpload(context: Context, projectId: String) {
        val workManager = WorkManager.getInstance(context)
        
        val uploadWork = OneTimeWorkRequestBuilder<ScheduledUploadWorker>()
            .setInputData(
                androidx.work.workDataOf(
                    ScheduledUploadWorker.KEY_PROJECT_ID to projectId
                )
            )
            .addTag("scheduled_upload")
            .addTag("project_$projectId")
            .build()
        
        workManager.enqueueUniqueWork(
            "scheduled_upload_$projectId",
            ExistingWorkPolicy.REPLACE,
            uploadWork
        )
        
        Log.d("AlarmReceiver", "Scheduled upload work enqueued for project: $projectId")
    }

    /**
     * تشغيل فحص عام للمشاريع المجدولة
     */
    private fun triggerScheduledCheck(context: Context) {
        val workManager = WorkManager.getInstance(context)
        
        val checkWork = OneTimeWorkRequestBuilder<ScheduledUploadWorker>()
            .addTag("scheduled_check")
            .build()
        
        workManager.enqueueUniqueWork(
            "scheduled_check",
            ExistingWorkPolicy.REPLACE,
            checkWork
        )
        
        Log.d("AlarmReceiver", "Scheduled check work enqueued")
    }
}
