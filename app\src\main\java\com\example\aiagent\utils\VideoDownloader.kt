package com.example.aiagent.utils

import android.content.Context
import android.util.Log
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير تحميل الفيديوهات
 * يتعامل مع تحميل الفيديوهات من مصادر مختلفة
 */
@Singleton
class VideoDownloader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val okHttpClient: OkHttpClient
) {
    
    companion object {
        private const val TAG = "VideoDownloader"
        private const val BUFFER_SIZE = 8192
        private const val MAX_FILE_SIZE = 100 * 1024 * 1024 // 100 MB
    }

    /**
     * تحميل فيديو من رابط
     */
    suspend fun downloadVideo(
        url: String,
        outputDir: File,
        fileName: String = "video.mp4",
        onProgress: (Int) -> Unit = {}
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting download from: $url")
            
            // التحقق من صحة الرابط
            if (!isValidVideoUrl(url)) {
                return@withContext Result.failure(Exception("رابط الفيديو غير صحيح"))
            }
            
            // إنشاء مجلد الإخراج
            if (!outputDir.exists()) {
                outputDir.mkdirs()
            }
            
            val outputFile = File(outputDir, fileName)
            
            // بناء الطلب
            val request = Request.Builder()
                .url(url)
                .addHeader("User-Agent", "Mozilla/5.0 (Android)")
                .build()
            
            val response: Response = okHttpClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                return@withContext Result.failure(Exception("فشل في تحميل الفيديو: ${response.code}"))
            }
            
            val responseBody = response.body
                ?: return@withContext Result.failure(Exception("لا يوجد محتوى في الاستجابة"))
            
            val contentLength = responseBody.contentLength()
            
            // التحقق من حجم الملف
            if (contentLength > MAX_FILE_SIZE) {
                return@withContext Result.failure(Exception("حجم الفيديو كبير جداً (أكثر من 100 ميجابايت)"))
            }
            
            // تحميل الملف
            val inputStream: InputStream = responseBody.byteStream()
            val outputStream = FileOutputStream(outputFile)
            
            val buffer = ByteArray(BUFFER_SIZE)
            var totalBytesRead = 0L
            var bytesRead: Int
            
            while (inputStream.read(buffer).also { bytesRead = it } != -1) {
                outputStream.write(buffer, 0, bytesRead)
                totalBytesRead += bytesRead
                
                // تحديث التقدم
                if (contentLength > 0) {
                    val progress = ((totalBytesRead * 100) / contentLength).toInt()
                    onProgress(progress)
                }
            }
            
            outputStream.close()
            inputStream.close()
            response.close()
            
            Log.d(TAG, "Download completed: ${outputFile.absolutePath}")
            Result.success(outputFile.absolutePath)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading video", e)
            Result.failure(e)
        }
    }

    /**
     * تحميل فيديو من يوتيوب (يتطلب مكتبة خارجية)
     */
    suspend fun downloadFromYouTube(
        videoId: String,
        outputDir: File,
        quality: VideoQuality = VideoQuality.MEDIUM
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            // هنا يمكن استخدام مكتبة مثل youtube-dl أو yt-dlp
            // للبساطة، سنعيد خطأ يطلب تنفيذ هذه الوظيفة
            Result.failure(Exception("تحميل من يوتيوب غير مدعوم حالياً"))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تحميل فيديو من Instagram
     */
    suspend fun downloadFromInstagram(
        postUrl: String,
        outputDir: File
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            // تنفيذ تحميل من Instagram
            // يتطلب API خاص أو web scraping
            Result.failure(Exception("تحميل من Instagram غير مدعوم حالياً"))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تحميل فيديو من TikTok
     */
    suspend fun downloadFromTikTok(
        videoUrl: String,
        outputDir: File
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            // تنفيذ تحميل من TikTok
            // يتطلب API خاص أو web scraping
            Result.failure(Exception("تحميل من TikTok غير مدعوم حالياً"))
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * التحقق من صحة رابط الفيديو
     */
    private fun isValidVideoUrl(url: String): Boolean {
        return try {
            val validExtensions = listOf(".mp4", ".avi", ".mov", ".mkv", ".webm", ".3gp")
            val validDomains = listOf("youtube.com", "youtu.be", "instagram.com", "tiktok.com")
            
            // التحقق من الامتداد
            val hasValidExtension = validExtensions.any { url.lowercase().contains(it) }
            
            // التحقق من النطاق
            val hasValidDomain = validDomains.any { url.lowercase().contains(it) }
            
            // التحقق من بروتوكول HTTP/HTTPS
            val hasValidProtocol = url.startsWith("http://") || url.startsWith("https://")
            
            hasValidProtocol && (hasValidExtension || hasValidDomain)
        } catch (e: Exception) {
            false
        }
    }

    /**
     * الحصول على معلومات الفيديو من الرابط
     */
    suspend fun getVideoInfo(url: String): Result<RemoteVideoInfo> = withContext(Dispatchers.IO) {
        try {
            val request = Request.Builder()
                .url(url)
                .head() // طلب HEAD للحصول على المعلومات فقط
                .build()
            
            val response = okHttpClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                return@withContext Result.failure(Exception("فشل في الحصول على معلومات الفيديو"))
            }
            
            val contentLength = response.header("Content-Length")?.toLongOrNull() ?: 0L
            val contentType = response.header("Content-Type") ?: ""
            
            response.close()
            
            val info = RemoteVideoInfo(
                url = url,
                size = contentLength,
                mimeType = contentType,
                isVideo = contentType.startsWith("video/")
            )
            
            Result.success(info)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * إلغاء جميع التحميلات الجارية
     */
    fun cancelAllDownloads() {
        // إلغاء جميع الطلبات في OkHttpClient
        okHttpClient.dispatcher.cancelAll()
    }

    /**
     * تنظيف الملفات المؤقتة
     */
    fun cleanupTempFiles() {
        try {
            val tempDir = File(context.cacheDir, "video_downloads")
            if (tempDir.exists()) {
                tempDir.deleteRecursively()
            }
        } catch (e: Exception) {
            Log.w(TAG, "Error cleaning up temp files", e)
        }
    }
}

/**
 * جودة الفيديو للتحميل
 */
enum class VideoQuality {
    LOW,    // 360p
    MEDIUM, // 720p
    HIGH    // 1080p
}

/**
 * معلومات الفيديو البعيد
 */
data class RemoteVideoInfo(
    val url: String,
    val size: Long,
    val mimeType: String,
    val isVideo: Boolean
)
