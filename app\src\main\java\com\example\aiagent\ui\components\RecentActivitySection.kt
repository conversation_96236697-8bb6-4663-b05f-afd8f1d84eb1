package com.example.aiagent.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.data.model.VideoStatus
import com.example.aiagent.ui.theme.*
import java.text.SimpleDateFormat
import java.util.*

/**
 * قسم النشاط الحديث
 */
@Composable
fun RecentActivitySection(
    recentProjects: List<VideoProject>,
    onProjectClick: (VideoProject) -> Unit
) {
    Column {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "النشاط الحديث",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )
            
            if (recentProjects.isNotEmpty()) {
                TextButton(onClick = { /* Navigate to all projects */ }) {
                    Text("عرض الكل")
                }
            }
        }
        
        Spacer(modifier = Modifier.height(12.dp))
        
        if (recentProjects.isEmpty()) {
            EmptyStateCard()
        } else {
            LazyColumn(
                modifier = Modifier.height(300.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(recentProjects) { project ->
                    ProjectActivityCard(
                        project = project,
                        onClick = { onProjectClick(project) }
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun ProjectActivityCard(
    project: VideoProject,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Status Icon
            StatusIcon(status = project.status)
            
            Spacer(modifier = Modifier.width(12.dp))
            
            // Project Info
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = project.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
                
                Text(
                    text = getStatusText(project.status),
                    style = MaterialTheme.typography.bodySmall,
                    color = getStatusColor(project.status)
                )
                
                Text(
                    text = formatDate(project.createdAt),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
            
            // Progress or Status Indicator
            when (project.status) {
                VideoStatus.UPLOADING -> {
                    CircularProgressIndicator(
                        progress = project.uploadProgress / 100f,
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                }
                VideoStatus.UPLOADED -> {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = SuccessGreen,
                        modifier = Modifier.size(24.dp)
                    )
                }
                VideoStatus.FAILED -> {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = null,
                        tint = ErrorRed,
                        modifier = Modifier.size(24.dp)
                    )
                }
                else -> {
                    Icon(
                        imageVector = Icons.Default.MoreVert,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(24.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun StatusIcon(status: VideoStatus) {
    val (icon, color) = when (status) {
        VideoStatus.PENDING -> Icons.Default.Schedule to WarningOrange
        VideoStatus.DOWNLOADING -> Icons.Default.Download to InfoBlue
        VideoStatus.PROCESSING -> Icons.Default.VideoSettings to InfoBlue
        VideoStatus.GENERATING_CONTENT -> Icons.Default.AutoAwesome to AccentPurple
        VideoStatus.READY_TO_UPLOAD -> Icons.Default.CloudUpload to AccentGreen
        VideoStatus.UPLOADING -> Icons.Default.Upload to InfoBlue
        VideoStatus.UPLOADED -> Icons.Default.CheckCircle to SuccessGreen
        VideoStatus.FAILED -> Icons.Default.Error to ErrorRed
        VideoStatus.CANCELLED -> Icons.Default.Cancel to NeutralGray
    }
    
    Surface(
        shape = RoundedCornerShape(8.dp),
        color = color.copy(alpha = 0.1f),
        modifier = Modifier.size(40.dp)
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxSize()
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

@Composable
private fun EmptyStateCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.VideoLibrary,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.size(48.dp)
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "لا توجد مشاريع حديثة",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Text(
                text = "ابدأ بإنشاء مشروع فيديو جديد",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}

private fun getStatusText(status: VideoStatus): String {
    return when (status) {
        VideoStatus.PENDING -> "في الانتظار"
        VideoStatus.DOWNLOADING -> "جاري التحميل"
        VideoStatus.PROCESSING -> "جاري المعالجة"
        VideoStatus.GENERATING_CONTENT -> "توليد المحتوى"
        VideoStatus.READY_TO_UPLOAD -> "جاهز للرفع"
        VideoStatus.UPLOADING -> "جاري الرفع"
        VideoStatus.UPLOADED -> "تم الرفع"
        VideoStatus.FAILED -> "فشل"
        VideoStatus.CANCELLED -> "ملغي"
    }
}

private fun getStatusColor(status: VideoStatus): Color {
    return when (status) {
        VideoStatus.PENDING -> WarningOrange
        VideoStatus.DOWNLOADING, VideoStatus.PROCESSING, VideoStatus.UPLOADING -> InfoBlue
        VideoStatus.GENERATING_CONTENT -> AccentPurple
        VideoStatus.READY_TO_UPLOAD -> AccentGreen
        VideoStatus.UPLOADED -> SuccessGreen
        VideoStatus.FAILED -> ErrorRed
        VideoStatus.CANCELLED -> NeutralGray
    }
}

private fun formatDate(timestamp: Long): String {
    val formatter = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault())
    return formatter.format(Date(timestamp))
}
