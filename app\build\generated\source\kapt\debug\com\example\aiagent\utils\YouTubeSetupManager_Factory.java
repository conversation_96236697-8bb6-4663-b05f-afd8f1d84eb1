package com.example.aiagent.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class YouTubeSetupManager_Factory implements Factory<YouTubeSetupManager> {
  private final Provider<Context> contextProvider;

  private final Provider<YouTubeAuthManager> youTubeAuthManagerProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  public YouTubeSetupManager_Factory(Provider<Context> contextProvider,
      Provider<YouTubeAuthManager> youTubeAuthManagerProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    this.contextProvider = contextProvider;
    this.youTubeAuthManagerProvider = youTubeAuthManagerProvider;
    this.notificationHelperProvider = notificationHelperProvider;
  }

  @Override
  public YouTubeSetupManager get() {
    return newInstance(contextProvider.get(), youTubeAuthManagerProvider.get(), notificationHelperProvider.get());
  }

  public static YouTubeSetupManager_Factory create(Provider<Context> contextProvider,
      Provider<YouTubeAuthManager> youTubeAuthManagerProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    return new YouTubeSetupManager_Factory(contextProvider, youTubeAuthManagerProvider, notificationHelperProvider);
  }

  public static YouTubeSetupManager newInstance(Context context,
      YouTubeAuthManager youTubeAuthManager, NotificationHelper notificationHelper) {
    return new YouTubeSetupManager(context, youTubeAuthManager, notificationHelper);
  }
}
