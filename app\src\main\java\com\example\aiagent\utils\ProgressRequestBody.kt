package com.example.aiagent.utils

import okhttp3.MediaType
import okhttp3.RequestBody
import okio.Buffer
import okio.BufferedSink
import okio.ForwardingSink
import okio.Sink
import okio.buffer

/**
 * RequestBody مع مراقبة التقدم
 * يتيح مراقبة تقدم رفع الملفات
 */
class ProgressRequestBody(
    private val requestBody: RequestBody,
    private val onProgress: (Int) -> Unit
) : RequestBody() {

    override fun contentType(): MediaType? = requestBody.contentType()

    override fun contentLength(): Long = requestBody.contentLength()

    override fun writeTo(sink: BufferedSink) {
        val progressSink = ProgressSink(sink, contentLength(), onProgress)
        val bufferedSink = progressSink.buffer()
        
        requestBody.writeTo(bufferedSink)
        bufferedSink.flush()
    }

    /**
     * Sink مع مراقبة التقدم
     */
    private class ProgressSink(
        delegate: Sink,
        private val contentLength: Long,
        private val onProgress: (Int) -> Unit
    ) : ForwardingSink(delegate) {
        
        private var bytesWritten = 0L

        override fun write(source: <PERSON>uffer, byteCount: Long) {
            super.write(source, byteCount)
            
            bytesWritten += byteCount
            
            if (contentLength > 0) {
                val progress = ((bytesWritten * 100) / contentLength).toInt()
                onProgress(progress.coerceIn(0, 100))
            }
        }
    }
}
