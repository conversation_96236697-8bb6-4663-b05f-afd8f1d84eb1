package com.example.aiagent.data.api

import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.*

/**
 * خدمة YouTube API
 */
interface YouTubeApiService {
    
    /**
     * رفع فيديو جديد
     */
    @Multipart
    @POST("upload/youtube/v3/videos")
    suspend fun uploadVideo(
        @Header("Authorization") authorization: String,
        @Part("snippet") snippet: RequestBody,
        @Part("status") status: RequestBody,
        @Part video: MultipartBody.Part,
        @Query("part") part: String = "snippet,status",
        @Query("uploadType") uploadType: String = "multipart"
    ): Response<YouTubeVideoResponse>
    
    /**
     * تحديث معلومات الفيديو
     */
    @PUT("youtube/v3/videos")
    suspend fun updateVideo(
        @Header("Authorization") authorization: String,
        @Query("part") part: String,
        @Body videoUpdate: YouTubeVideoUpdateRequest
    ): Response<YouTubeVideoResponse>
    
    /**
     * الحصول على معلومات الفيديو
     */
    @GET("youtube/v3/videos")
    suspend fun getVideo(
        @Header("Authorization") authorization: String,
        @Query("part") part: String = "snippet,status,statistics",
        @Query("id") videoId: String
    ): Response<YouTubeVideoListResponse>
    
    /**
     * الحصول على معلومات القناة
     */
    @GET("youtube/v3/channels")
    suspend fun getChannel(
        @Header("Authorization") authorization: String,
        @Query("part") part: String = "snippet,statistics",
        @Query("mine") mine: Boolean = true
    ): Response<YouTubeChannelListResponse>
    
    /**
     * الحصول على قائمة الفيديوهات للقناة
     */
    @GET("youtube/v3/search")
    suspend fun getChannelVideos(
        @Header("Authorization") authorization: String,
        @Query("part") part: String = "snippet",
        @Query("channelId") channelId: String,
        @Query("type") type: String = "video",
        @Query("order") order: String = "date",
        @Query("maxResults") maxResults: Int = 50
    ): Response<YouTubeSearchResponse>
}

/**
 * نماذج البيانات لـ YouTube API
 */
data class YouTubeVideoResponse(
    val id: String,
    val snippet: YouTubeVideoSnippet,
    val status: YouTubeVideoStatus
)

data class YouTubeVideoSnippet(
    val title: String,
    val description: String,
    val tags: List<String>?,
    val categoryId: String,
    val defaultLanguage: String = "ar",
    val defaultAudioLanguage: String = "ar"
)

data class YouTubeVideoStatus(
    val privacyStatus: String = "public", // public, private, unlisted
    val embeddable: Boolean = true,
    val license: String = "youtube",
    val publicStatsViewable: Boolean = true,
    val madeForKids: Boolean = false
)

data class YouTubeVideoUpdateRequest(
    val id: String,
    val snippet: YouTubeVideoSnippet?,
    val status: YouTubeVideoStatus?
)

data class YouTubeVideoListResponse(
    val items: List<YouTubeVideoResponse>
)

data class YouTubeChannelListResponse(
    val items: List<YouTubeChannelResponse>
)

data class YouTubeChannelResponse(
    val id: String,
    val snippet: YouTubeChannelSnippet,
    val statistics: YouTubeChannelStatistics?
)

data class YouTubeChannelSnippet(
    val title: String,
    val description: String,
    val thumbnails: YouTubeThumbnails?
)

data class YouTubeChannelStatistics(
    val viewCount: String,
    val subscriberCount: String,
    val videoCount: String
)

data class YouTubeSearchResponse(
    val items: List<YouTubeSearchItem>
)

data class YouTubeSearchItem(
    val id: YouTubeVideoId,
    val snippet: YouTubeVideoSnippet
)

data class YouTubeVideoId(
    val videoId: String
)

data class YouTubeThumbnails(
    val default: YouTubeThumbnail?,
    val medium: YouTubeThumbnail?,
    val high: YouTubeThumbnail?
)

data class YouTubeThumbnail(
    val url: String,
    val width: Int,
    val height: Int
)
