package com.example.aiagent.utils

import android.content.Context
import android.util.Log
import com.google.firebase.FirebaseApp
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.FirebaseFirestoreSettings
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مهيئ Firebase
 * يتعامل مع إعداد وتهيئة Firebase
 */
@Singleton
class FirebaseInitializer @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "FirebaseInitializer"
    }

    /**
     * تهيئة Firebase
     */
    fun initialize(): Boolean {
        return try {
            // تهيئة Firebase App
            if (FirebaseApp.getApps(context).isEmpty()) {
                FirebaseApp.initializeApp(context)
                Log.d(TAG, "Firebase initialized successfully")
            } else {
                Log.d(TAG, "Firebase already initialized")
            }
            
            // إعداد Firestore
            setupFirestore()
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing Firebase", e)
            false
        }
    }

    /**
     * إعداد Firestore
     */
    private fun setupFirestore() {
        try {
            val firestore = FirebaseFirestore.getInstance()
            
            // إعدادات Firestore للأداء الأمثل
            val settings = FirebaseFirestoreSettings.Builder()
                .setPersistenceEnabled(true) // تمكين التخزين المحلي
                .setCacheSizeBytes(FirebaseFirestoreSettings.CACHE_SIZE_UNLIMITED)
                .build()
            
            firestore.firestoreSettings = settings
            
            Log.d(TAG, "Firestore configured successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error configuring Firestore", e)
        }
    }

    /**
     * التحقق من حالة الاتصال بـ Firebase
     */
    suspend fun checkConnection(): Boolean {
        return try {
            val firestore = FirebaseFirestore.getInstance()
            
            // محاولة قراءة بسيطة للتحقق من الاتصال
            firestore.collection("videos")
                .limit(1)
                .get()
                .addOnSuccessListener {
                    Log.d(TAG, "Firebase connection successful")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Firebase connection failed", e)
                }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error checking Firebase connection", e)
            false
        }
    }

    /**
     * إنشاء بنية قاعدة البيانات الأساسية (للاختبار)
     */
    suspend fun createSampleData(): Boolean {
        return try {
            val firestore = FirebaseFirestore.getInstance()
            
            // إنشاء فيديو تجريبي
            val sampleVideo = mapOf(
                "title" to "فيديو تجريبي مضحك",
                "description" to "هذا فيديو تجريبي للاختبار",
                "download_url" to "https://example.com/sample_video.mp4",
                "thumbnail_url" to "https://example.com/sample_thumbnail.jpg",
                "duration" to 30,
                "file_size" to 5000000L,
                "resolution" to "720x1280",
                "category" to "FUNNY",
                "tags" to listOf("مضحك", "ترفيه", "كوميديا"),
                "language" to "ar",
                "upload_date" to System.currentTimeMillis(),
                "view_count" to 0,
                "download_count" to 0,
                "rating" to 4.5f,
                "is_active" to true,
                "source" to "sample",
                "keywords" to listOf("مضحك", "فيديو", "ترفيه"),
                "mood" to "FUNNY",
                "age_rating" to "ALL_AGES"
            )
            
            firestore.collection("videos")
                .document("sample_video_1")
                .set(sampleVideo)
                .addOnSuccessListener {
                    Log.d(TAG, "Sample video created successfully")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Error creating sample video", e)
                }
            
            // إنشاء إحصائيات أولية
            val initialStats = mapOf(
                "totalVideos" to 1,
                "videosByCategory" to mapOf("FUNNY" to 1),
                "videosByMood" to mapOf("FUNNY" to 1),
                "averageRating" to 4.5f,
                "totalDownloads" to 0,
                "mostPopularTags" to listOf("مضحك", "ترفيه", "كوميديا")
            )
            
            firestore.collection("video_stats")
                .document("global")
                .set(initialStats)
                .addOnSuccessListener {
                    Log.d(TAG, "Initial stats created successfully")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Error creating initial stats", e)
                }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error creating sample data", e)
            false
        }
    }
}
