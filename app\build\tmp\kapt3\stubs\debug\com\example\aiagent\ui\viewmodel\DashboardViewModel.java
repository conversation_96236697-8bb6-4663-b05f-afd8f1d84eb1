package com.example.aiagent.ui.viewmodel;

/**
 * ViewModel للشاشة الرئيسية
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0006\u0010\u0010\u001a\u00020\u0011J\b\u0010\u0012\u001a\u00020\u0011H\u0002J\u0006\u0010\u0013\u001a\u00020\u0011J\u0010\u0010\u0014\u001a\u00020\u00112\b\u0010\u0015\u001a\u0004\u0018\u00010\u0016J\u0015\u0010\u0017\u001a\u00020\u00112\b\u0010\u0018\u001a\u0004\u0018\u00010\u0019\u00a2\u0006\u0002\u0010\u001aR\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001b"}, d2 = {"Lcom/example/aiagent/ui/viewmodel/DashboardViewModel;", "Landroidx/lifecycle/ViewModel;", "aiAgentManager", "Lcom/example/aiagent/core/AiAgentManager;", "videoProjectRepository", "Lcom/example/aiagent/data/repository/VideoProjectRepository;", "userSettingsRepository", "Lcom/example/aiagent/data/repository/UserSettingsRepository;", "(Lcom/example/aiagent/core/AiAgentManager;Lcom/example/aiagent/data/repository/VideoProjectRepository;Lcom/example/aiagent/data/repository/UserSettingsRepository;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/aiagent/ui/viewmodel/DashboardUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "createImmediateProject", "", "loadDashboardData", "toggleAgent", "updateCurrentTask", "task", "", "updateNextScheduledUpload", "timestamp", "", "(Ljava/lang/Long;)V", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class DashboardViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.core.AiAgentManager aiAgentManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.data.repository.VideoProjectRepository videoProjectRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.data.repository.UserSettingsRepository userSettingsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.aiagent.ui.viewmodel.DashboardUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.aiagent.ui.viewmodel.DashboardUiState> uiState = null;
    
    @javax.inject.Inject()
    public DashboardViewModel(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.core.AiAgentManager aiAgentManager, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.repository.VideoProjectRepository videoProjectRepository, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.repository.UserSettingsRepository userSettingsRepository) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.aiagent.ui.viewmodel.DashboardUiState> getUiState() {
        return null;
    }
    
    /**
     * تحميل بيانات الشاشة الرئيسية
     */
    private final void loadDashboardData() {
    }
    
    /**
     * تبديل حالة الوكيل
     */
    public final void toggleAgent() {
    }
    
    /**
     * إنشاء مشروع فوري
     */
    public final void createImmediateProject() {
    }
    
    /**
     * تحديث المهمة الحالية
     */
    public final void updateCurrentTask(@org.jetbrains.annotations.Nullable()
    java.lang.String task) {
    }
    
    /**
     * تحديث موعد الرفع التالي
     */
    public final void updateNextScheduledUpload(@org.jetbrains.annotations.Nullable()
    java.lang.Long timestamp) {
    }
}