package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.utils.NotificationHelper;
import com.example.aiagent.utils.YouTubeAuthManager;
import com.example.aiagent.utils.YouTubeSetupManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideYouTubeSetupManagerFactory implements Factory<YouTubeSetupManager> {
  private final Provider<Context> contextProvider;

  private final Provider<YouTubeAuthManager> youTubeAuthManagerProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  public NetworkModule_ProvideYouTubeSetupManagerFactory(Provider<Context> contextProvider,
      Provider<YouTubeAuthManager> youTubeAuthManagerProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    this.contextProvider = contextProvider;
    this.youTubeAuthManagerProvider = youTubeAuthManagerProvider;
    this.notificationHelperProvider = notificationHelperProvider;
  }

  @Override
  public YouTubeSetupManager get() {
    return provideYouTubeSetupManager(contextProvider.get(), youTubeAuthManagerProvider.get(), notificationHelperProvider.get());
  }

  public static NetworkModule_ProvideYouTubeSetupManagerFactory create(
      Provider<Context> contextProvider, Provider<YouTubeAuthManager> youTubeAuthManagerProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    return new NetworkModule_ProvideYouTubeSetupManagerFactory(contextProvider, youTubeAuthManagerProvider, notificationHelperProvider);
  }

  public static YouTubeSetupManager provideYouTubeSetupManager(Context context,
      YouTubeAuthManager youTubeAuthManager, NotificationHelper notificationHelper) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideYouTubeSetupManager(context, youTubeAuthManager, notificationHelper));
  }
}
