package com.example.aiagent.data.api;

/**
 * خدمة YouTube API
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000Z\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\bf\u0018\u00002\u00020\u0001J2\u0010\u0002\u001a\b\u0012\u0004\u0012\u00020\u00040\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0003\u0010\u0007\u001a\u00020\u00062\b\b\u0003\u0010\b\u001a\u00020\tH\u00a7@\u00a2\u0006\u0002\u0010\nJP\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\f0\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0003\u0010\u0007\u001a\u00020\u00062\b\b\u0001\u0010\r\u001a\u00020\u00062\b\b\u0003\u0010\u000e\u001a\u00020\u00062\b\b\u0003\u0010\u000f\u001a\u00020\u00062\b\b\u0003\u0010\u0010\u001a\u00020\u0011H\u00a7@\u00a2\u0006\u0002\u0010\u0012J2\u0010\u0013\u001a\b\u0012\u0004\u0012\u00020\u00140\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0003\u0010\u0007\u001a\u00020\u00062\b\b\u0001\u0010\u0015\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010\u0016J2\u0010\u0017\u001a\b\u0012\u0004\u0012\u00020\u00180\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\u0007\u001a\u00020\u00062\b\b\u0001\u0010\u0019\u001a\u00020\u001aH\u00a7@\u00a2\u0006\u0002\u0010\u001bJP\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00180\u00032\b\b\u0001\u0010\u0005\u001a\u00020\u00062\b\b\u0001\u0010\u001d\u001a\u00020\u001e2\b\b\u0001\u0010\u001f\u001a\u00020\u001e2\b\b\u0001\u0010 \u001a\u00020!2\b\b\u0003\u0010\u0007\u001a\u00020\u00062\b\b\u0003\u0010\"\u001a\u00020\u0006H\u00a7@\u00a2\u0006\u0002\u0010#\u00a8\u0006$"}, d2 = {"Lcom/example/aiagent/data/api/YouTubeApiService;", "", "getChannel", "Lretrofit2/Response;", "Lcom/example/aiagent/data/api/YouTubeChannelListResponse;", "authorization", "", "part", "mine", "", "(Ljava/lang/String;Ljava/lang/String;ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getChannelVideos", "Lcom/example/aiagent/data/api/YouTubeSearchResponse;", "channelId", "type", "order", "maxResults", "", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ILkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getVideo", "Lcom/example/aiagent/data/api/YouTubeVideoListResponse;", "videoId", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateVideo", "Lcom/example/aiagent/data/api/YouTubeVideoResponse;", "videoUpdate", "Lcom/example/aiagent/data/api/YouTubeVideoUpdateRequest;", "(Ljava/lang/String;Ljava/lang/String;Lcom/example/aiagent/data/api/YouTubeVideoUpdateRequest;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "uploadVideo", "snippet", "Lokhttp3/RequestBody;", "status", "video", "Lokhttp3/MultipartBody$Part;", "uploadType", "(Ljava/lang/String;Lokhttp3/RequestBody;Lokhttp3/RequestBody;Lokhttp3/MultipartBody$Part;Ljava/lang/String;Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public abstract interface YouTubeApiService {
    
    /**
     * رفع فيديو جديد
     */
    @retrofit2.http.Multipart()
    @retrofit2.http.POST(value = "upload/youtube/v3/videos")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object uploadVideo(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Part(value = "snippet")
    @org.jetbrains.annotations.NotNull()
    okhttp3.RequestBody snippet, @retrofit2.http.Part(value = "status")
    @org.jetbrains.annotations.NotNull()
    okhttp3.RequestBody status, @retrofit2.http.Part()
    @org.jetbrains.annotations.NotNull()
    okhttp3.MultipartBody.Part video, @retrofit2.http.Query(value = "part")
    @org.jetbrains.annotations.NotNull()
    java.lang.String part, @retrofit2.http.Query(value = "uploadType")
    @org.jetbrains.annotations.NotNull()
    java.lang.String uploadType, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.aiagent.data.api.YouTubeVideoResponse>> $completion);
    
    /**
     * تحديث معلومات الفيديو
     */
    @retrofit2.http.PUT(value = "youtube/v3/videos")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object updateVideo(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Query(value = "part")
    @org.jetbrains.annotations.NotNull()
    java.lang.String part, @retrofit2.http.Body()
    @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.api.YouTubeVideoUpdateRequest videoUpdate, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.aiagent.data.api.YouTubeVideoResponse>> $completion);
    
    /**
     * الحصول على معلومات الفيديو
     */
    @retrofit2.http.GET(value = "youtube/v3/videos")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getVideo(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Query(value = "part")
    @org.jetbrains.annotations.NotNull()
    java.lang.String part, @retrofit2.http.Query(value = "id")
    @org.jetbrains.annotations.NotNull()
    java.lang.String videoId, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.aiagent.data.api.YouTubeVideoListResponse>> $completion);
    
    /**
     * الحصول على معلومات القناة
     */
    @retrofit2.http.GET(value = "youtube/v3/channels")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getChannel(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Query(value = "part")
    @org.jetbrains.annotations.NotNull()
    java.lang.String part, @retrofit2.http.Query(value = "mine")
    boolean mine, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.aiagent.data.api.YouTubeChannelListResponse>> $completion);
    
    /**
     * الحصول على قائمة الفيديوهات للقناة
     */
    @retrofit2.http.GET(value = "youtube/v3/search")
    @org.jetbrains.annotations.Nullable()
    public abstract java.lang.Object getChannelVideos(@retrofit2.http.Header(value = "Authorization")
    @org.jetbrains.annotations.NotNull()
    java.lang.String authorization, @retrofit2.http.Query(value = "part")
    @org.jetbrains.annotations.NotNull()
    java.lang.String part, @retrofit2.http.Query(value = "channelId")
    @org.jetbrains.annotations.NotNull()
    java.lang.String channelId, @retrofit2.http.Query(value = "type")
    @org.jetbrains.annotations.NotNull()
    java.lang.String type, @retrofit2.http.Query(value = "order")
    @org.jetbrains.annotations.NotNull()
    java.lang.String order, @retrofit2.http.Query(value = "maxResults")
    int maxResults, @org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super retrofit2.Response<com.example.aiagent.data.api.YouTubeSearchResponse>> $completion);
    
    /**
     * خدمة YouTube API
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 3, xi = 48)
    public static final class DefaultImpls {
    }
}