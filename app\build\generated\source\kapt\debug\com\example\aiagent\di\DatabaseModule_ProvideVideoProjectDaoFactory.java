package com.example.aiagent.di;

import com.example.aiagent.data.database.AiAgentDatabase;
import com.example.aiagent.data.database.VideoProjectDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvideVideoProjectDaoFactory implements Factory<VideoProjectDao> {
  private final Provider<AiAgentDatabase> databaseProvider;

  public DatabaseModule_ProvideVideoProjectDaoFactory(Provider<AiAgentDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public VideoProjectDao get() {
    return provideVideoProjectDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideVideoProjectDaoFactory create(
      Provider<AiAgentDatabase> databaseProvider) {
    return new DatabaseModule_ProvideVideoProjectDaoFactory(databaseProvider);
  }

  public static VideoProjectDao provideVideoProjectDao(AiAgentDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideVideoProjectDao(database));
  }
}
