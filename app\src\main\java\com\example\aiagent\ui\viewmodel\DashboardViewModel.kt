package com.example.aiagent.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aiagent.core.AiAgentManager
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.data.model.UserSettings
import com.example.aiagent.data.repository.VideoProjectRepository
import com.example.aiagent.data.repository.UserSettingsRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel للشاشة الرئيسية
 */
@HiltViewModel
class DashboardViewModel @Inject constructor(
    private val aiAgentManager: AiAgentManager,
    private val videoProjectRepository: VideoProjectRepository,
    private val userSettingsRepository: UserSettingsRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(DashboardUiState())
    val uiState: StateFlow<DashboardUiState> = _uiState.asStateFlow()

    init {
        loadDashboardData()
    }

    /**
     * تحميل بيانات الشاشة الرئيسية
     */
    private fun loadDashboardData() {
        viewModelScope.launch {
            // تحميل الإعدادات
            userSettingsRepository.getSettings().collect { settings ->
                if (settings != null) {
                    _uiState.value = _uiState.value.copy(
                        channelName = settings.channelName,
                        isAgentActive = settings.autoUpload
                    )
                }
            }
        }

        viewModelScope.launch {
            // تحميل إحصائيات المشاريع
            val stats = videoProjectRepository.getProjectStatistics()
            _uiState.value = _uiState.value.copy(
                totalVideos = stats.total,
                uploadedToday = stats.today,
                pendingVideos = stats.pending,
                successRate = if (stats.total > 0) stats.uploaded.toFloat() / stats.total else 0f
            )
        }

        viewModelScope.launch {
            // تحميل المشاريع الحديثة
            videoProjectRepository.getAllProjects().collect { projects ->
                _uiState.value = _uiState.value.copy(
                    recentProjects = projects.take(5)
                )
            }
        }
    }

    /**
     * تبديل حالة الوكيل
     */
    fun toggleAgent() {
        viewModelScope.launch {
            val currentSettings = userSettingsRepository.getSettingsOnce()
            if (currentSettings != null) {
                val newAutoUpload = !currentSettings.autoUpload
                userSettingsRepository.updateAutoUpload(newAutoUpload)
                
                if (newAutoUpload) {
                    aiAgentManager.startAgent(currentSettings.copy(autoUpload = true))
                } else {
                    aiAgentManager.stopAgent()
                }
                
                _uiState.value = _uiState.value.copy(isAgentActive = newAutoUpload)
            }
        }
    }

    /**
     * إنشاء مشروع فوري
     */
    fun createImmediateProject() {
        viewModelScope.launch {
            val settings = userSettingsRepository.getSettingsOnce()
            if (settings != null) {
                aiAgentManager.createImmediateProject(settings)
            }
        }
    }

    /**
     * تحديث المهمة الحالية
     */
    fun updateCurrentTask(task: String?) {
        _uiState.value = _uiState.value.copy(currentTask = task)
    }

    /**
     * تحديث موعد الرفع التالي
     */
    fun updateNextScheduledUpload(timestamp: Long?) {
        _uiState.value = _uiState.value.copy(nextScheduledUpload = timestamp)
    }
}

/**
 * حالة واجهة المستخدم للشاشة الرئيسية
 */
data class DashboardUiState(
    val channelName: String = "",
    val isAgentActive: Boolean = false,
    val totalVideos: Int = 0,
    val uploadedToday: Int = 0,
    val pendingVideos: Int = 0,
    val successRate: Float = 0f,
    val recentProjects: List<VideoProject> = emptyList(),
    val currentTask: String? = null,
    val nextScheduledUpload: Long? = null,
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)
