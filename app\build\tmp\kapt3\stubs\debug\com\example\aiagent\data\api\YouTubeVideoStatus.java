package com.example.aiagent.data.api;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0014\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B7\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0004\u001a\u00020\u0005\u0012\b\b\u0002\u0010\u0006\u001a\u00020\u0003\u0012\b\b\u0002\u0010\u0007\u001a\u00020\u0005\u0012\b\b\u0002\u0010\b\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\tJ\t\u0010\u0011\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0012\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0013\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0014\u001a\u00020\u0005H\u00c6\u0003J\t\u0010\u0015\u001a\u00020\u0005H\u00c6\u0003J;\u0010\u0016\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00032\b\b\u0002\u0010\u0007\u001a\u00020\u00052\b\b\u0002\u0010\b\u001a\u00020\u0005H\u00c6\u0001J\u0013\u0010\u0017\u001a\u00020\u00052\b\u0010\u0018\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0019\u001a\u00020\u001aH\u00d6\u0001J\t\u0010\u001b\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u000bR\u0011\u0010\u0006\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\rR\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000bR\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000f\u0010\rR\u0011\u0010\u0007\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u000b\u00a8\u0006\u001c"}, d2 = {"Lcom/example/aiagent/data/api/YouTubeVideoStatus;", "", "privacyStatus", "", "embeddable", "", "license", "publicStatsViewable", "madeForKids", "(Ljava/lang/String;ZLjava/lang/String;ZZ)V", "getEmbeddable", "()Z", "getLicense", "()Ljava/lang/String;", "getMadeForKids", "getPrivacyStatus", "getPublicStatsViewable", "component1", "component2", "component3", "component4", "component5", "copy", "equals", "other", "hashCode", "", "toString", "app_debug"})
public final class YouTubeVideoStatus {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String privacyStatus = null;
    private final boolean embeddable = false;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String license = null;
    private final boolean publicStatsViewable = false;
    private final boolean madeForKids = false;
    
    public YouTubeVideoStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String privacyStatus, boolean embeddable, @org.jetbrains.annotations.NotNull()
    java.lang.String license, boolean publicStatsViewable, boolean madeForKids) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getPrivacyStatus() {
        return null;
    }
    
    public final boolean getEmbeddable() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getLicense() {
        return null;
    }
    
    public final boolean getPublicStatsViewable() {
        return false;
    }
    
    public final boolean getMadeForKids() {
        return false;
    }
    
    public YouTubeVideoStatus() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component2() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component3() {
        return null;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component5() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.api.YouTubeVideoStatus copy(@org.jetbrains.annotations.NotNull()
    java.lang.String privacyStatus, boolean embeddable, @org.jetbrains.annotations.NotNull()
    java.lang.String license, boolean publicStatsViewable, boolean madeForKids) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}