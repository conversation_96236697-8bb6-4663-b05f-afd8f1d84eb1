package com.example.aiagent.data.database;

/**
 * قاعدة البيانات الرئيسية للتطبيق
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b'\u0018\u0000 \u000b2\u00020\u0001:\u0001\u000bB\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&J\b\u0010\u0007\u001a\u00020\bH&J\b\u0010\t\u001a\u00020\nH&\u00a8\u0006\f"}, d2 = {"Lcom/example/aiagent/data/database/AiAgentDatabase;", "Landroidx/room/RoomDatabase;", "()V", "appStatisticsDao", "Lcom/example/aiagent/data/database/AppStatisticsDao;", "operationLogDao", "Lcom/example/aiagent/data/database/OperationLogDao;", "userSettingsDao", "Lcom/example/aiagent/data/database/UserSettingsDao;", "videoProjectDao", "Lcom/example/aiagent/data/database/VideoProjectDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.example.aiagent.data.model.VideoProject.class, com.example.aiagent.data.model.UserSettings.class, com.example.aiagent.data.model.AppStatistics.class, com.example.aiagent.data.model.OperationLog.class}, version = 1, exportSchema = false)
@androidx.room.TypeConverters(value = {com.example.aiagent.data.database.Converters.class})
public abstract class AiAgentDatabase extends androidx.room.RoomDatabase {
    @kotlin.jvm.Volatile()
    @org.jetbrains.annotations.Nullable()
    private static volatile com.example.aiagent.data.database.AiAgentDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.aiagent.data.database.AiAgentDatabase.Companion Companion = null;
    
    public AiAgentDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.aiagent.data.database.VideoProjectDao videoProjectDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.aiagent.data.database.UserSettingsDao userSettingsDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.aiagent.data.database.AppStatisticsDao appStatisticsDao();
    
    @org.jetbrains.annotations.NotNull()
    public abstract com.example.aiagent.data.database.OperationLogDao operationLogDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u000e\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\b"}, d2 = {"Lcom/example/aiagent/data/database/AiAgentDatabase$Companion;", "", "()V", "INSTANCE", "Lcom/example/aiagent/data/database/AiAgentDatabase;", "getDatabase", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.aiagent.data.database.AiAgentDatabase getDatabase(@org.jetbrains.annotations.NotNull()
        android.content.Context context) {
            return null;
        }
    }
}