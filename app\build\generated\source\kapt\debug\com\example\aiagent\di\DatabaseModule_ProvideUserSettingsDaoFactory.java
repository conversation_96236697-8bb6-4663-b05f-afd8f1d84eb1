package com.example.aiagent.di;

import com.example.aiagent.data.database.AiAgentDatabase;
import com.example.aiagent.data.database.UserSettingsDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DatabaseModule_ProvideUserSettingsDaoFactory implements Factory<UserSettingsDao> {
  private final Provider<AiAgentDatabase> databaseProvider;

  public DatabaseModule_ProvideUserSettingsDaoFactory(Provider<AiAgentDatabase> databaseProvider) {
    this.databaseProvider = databaseProvider;
  }

  @Override
  public UserSettingsDao get() {
    return provideUserSettingsDao(databaseProvider.get());
  }

  public static DatabaseModule_ProvideUserSettingsDaoFactory create(
      Provider<AiAgentDatabase> databaseProvider) {
    return new DatabaseModule_ProvideUserSettingsDaoFactory(databaseProvider);
  }

  public static UserSettingsDao provideUserSettingsDao(AiAgentDatabase database) {
    return Preconditions.checkNotNullFromProvides(DatabaseModule.INSTANCE.provideUserSettingsDao(database));
  }
}
