package com.example.aiagent.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.example.aiagent.ui.components.*
import com.example.aiagent.ui.theme.*
import com.example.aiagent.ui.viewmodel.DashboardViewModel

/**
 * شاشة الرئيسية - Dashboard
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DashboardScreen(
    navController: NavController,
    viewModel: DashboardViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header with greeting
        item {
            DashboardHeader(
                channelName = uiState.channelName,
                isAgentActive = uiState.isAgentActive,
                onToggleAgent = viewModel::toggleAgent
            )
        }
        
        // Quick Stats Cards
        item {
            QuickStatsSection(
                totalVideos = uiState.totalVideos,
                uploadedToday = uiState.uploadedToday,
                pendingVideos = uiState.pendingVideos,
                successRate = uiState.successRate
            )
        }
        
        // Quick Actions
        item {
            QuickActionsSection(
                onCreateProject = { viewModel.createImmediateProject() },
                onScheduleProject = { /* Navigate to schedule */ },
                onViewAnalytics = { navController.navigate("analytics") },
                onOpenSettings = { navController.navigate("settings") }
            )
        }
        
        // Recent Activity
        item {
            RecentActivitySection(
                recentProjects = uiState.recentProjects,
                onProjectClick = { /* Navigate to project details */ }
            )
        }
        
        // Agent Status
        item {
            AgentStatusSection(
                isActive = uiState.isAgentActive,
                nextScheduledUpload = uiState.nextScheduledUpload,
                currentTask = uiState.currentTask
            )
        }
    }
}

@Composable
private fun DashboardHeader(
    channelName: String,
    isAgentActive: Boolean,
    onToggleAgent: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.primaryContainer
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            GradientStart,
                            GradientEnd
                        )
                    )
                )
                .padding(20.dp)
        ) {
            Column {
                Text(
                    text = "مرحباً بك في وكيل الفيديو الذكي",
                    style = MaterialTheme.typography.headlineSmall,
                    color = Color.White,
                    fontWeight = FontWeight.Bold
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "قناة: $channelName",
                    style = MaterialTheme.typography.bodyLarge,
                    color = Color.White.copy(alpha = 0.9f)
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Switch(
                        checked = isAgentActive,
                        onCheckedChange = { onToggleAgent() },
                        colors = SwitchDefaults.colors(
                            checkedThumbColor = Color.White,
                            checkedTrackColor = Color.White.copy(alpha = 0.3f)
                        )
                    )
                    
                    Spacer(modifier = Modifier.width(12.dp))
                    
                    Text(
                        text = if (isAgentActive) "الوكيل نشط" else "الوكيل متوقف",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@Composable
private fun QuickStatsSection(
    totalVideos: Int,
    uploadedToday: Int,
    pendingVideos: Int,
    successRate: Float
) {
    Column {
        Text(
            text = "إحصائيات سريعة",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(
                listOf(
                    StatCard("إجمالي الفيديوهات", totalVideos.toString(), Icons.Default.VideoLibrary, AccentBlue),
                    StatCard("رُفع اليوم", uploadedToday.toString(), Icons.Default.Upload, AccentGreen),
                    StatCard("في الانتظار", pendingVideos.toString(), Icons.Default.Schedule, AccentOrange),
                    StatCard("معدل النجاح", "${(successRate * 100).toInt()}%", Icons.Default.TrendingUp, AccentPurple)
                )
            ) { stat ->
                StatCardItem(stat = stat)
            }
        }
    }
}

@Composable
private fun StatCardItem(stat: StatCard) {
    Card(
        modifier = Modifier
            .width(140.dp)
            .height(100.dp),
        colors = CardDefaults.cardColors(
            containerColor = stat.color.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                imageVector = stat.icon,
                contentDescription = null,
                tint = stat.color,
                modifier = Modifier.size(24.dp)
            )
            
            Column {
                Text(
                    text = stat.value,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = stat.color
                )
                Text(
                    text = stat.title,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

data class StatCard(
    val title: String,
    val value: String,
    val icon: ImageVector,
    val color: Color
)
