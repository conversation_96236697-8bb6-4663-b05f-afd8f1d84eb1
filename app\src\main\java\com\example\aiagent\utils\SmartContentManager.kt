package com.example.aiagent.utils

import android.content.Context
import android.util.Log
import com.example.aiagent.data.api.VideoContentResponse
import com.example.aiagent.data.model.*
import com.example.aiagent.data.repository.GeminiRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مدير المحتوى الذكي
 * يستخدم Gemini AI لتوليد محتوى ذكي ومناسب للوقت والمناسبات
 */
@Singleton
class SmartContentManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val geminiRepository: GeminiRepository
) {
    
    companion object {
        private const val TAG = "SmartContentManager"
    }

    /**
     * توليد محتوى ذكي بناءً على الوقت والسياق
     */
    suspend fun generateSmartContent(
        firebaseVideo: FirebaseVideo,
        channelName: String,
        settings: UserSettings
    ): Result<VideoContentResponse> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Generating smart content for video: ${firebaseVideo.title}")
            
            // تحديد السياق الحالي
            val context = analyzeCurrentContext()
            
            // اختيار استراتيجية التوليد المناسبة
            val contentResult = when {
                context.isSpecialOccasion -> generateOccasionContent(firebaseVideo, channelName, context)
                context.isWeekend -> generateWeekendContent(firebaseVideo, channelName)
                context.isEvening -> generateEveningContent(firebaseVideo, channelName)
                context.isMorning -> generateMorningContent(firebaseVideo, channelName)
                else -> generateRegularContent(firebaseVideo, channelName)
            }
            
            if (contentResult.isSuccess) {
                val content = contentResult.getOrNull()!!
                
                // تحسين المحتوى بناءً على إعدادات المستخدم
                val optimizedContent = optimizeContentForUser(content, settings)
                
                Log.d(TAG, "Smart content generated successfully")
                Result.success(optimizedContent)
            } else {
                contentResult
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error generating smart content", e)
            Result.failure(e)
        }
    }

    /**
     * توليد محتوى للمناسبات الخاصة
     */
    private suspend fun generateOccasionContent(
        firebaseVideo: FirebaseVideo,
        channelName: String,
        context: ContentContext
    ): Result<VideoContentResponse> {
        return try {
            val occasionKeywords = getOccasionKeywords(context.occasion)
            
            geminiRepository.generateVideoContent(
                videoDescription = "${firebaseVideo.description} - مناسبة ${context.occasion}",
                channelName = channelName,
                keywords = firebaseVideo.tags + occasionKeywords
            )
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * توليد محتوى لعطلة نهاية الأسبوع
     */
    private suspend fun generateWeekendContent(
        firebaseVideo: FirebaseVideo,
        channelName: String
    ): Result<VideoContentResponse> {
        return geminiRepository.generateVideoContent(
            videoDescription = "${firebaseVideo.description} - محتوى عطلة نهاية الأسبوع",
            channelName = channelName,
            keywords = firebaseVideo.tags + listOf("عطلة", "استرخاء", "ترفيه", "weekend")
        )
    }

    /**
     * توليد محتوى مسائي
     */
    private suspend fun generateEveningContent(
        firebaseVideo: FirebaseVideo,
        channelName: String
    ): Result<VideoContentResponse> {
        return geminiRepository.generateVideoContent(
            videoDescription = "${firebaseVideo.description} - محتوى مسائي",
            channelName = channelName,
            keywords = firebaseVideo.tags + listOf("مساء", "استرخاء", "ترفيه_مسائي")
        )
    }

    /**
     * توليد محتوى صباحي
     */
    private suspend fun generateMorningContent(
        firebaseVideo: FirebaseVideo,
        channelName: String
    ): Result<VideoContentResponse> {
        return geminiRepository.generateVideoContent(
            videoDescription = "${firebaseVideo.description} - محتوى صباحي",
            channelName = channelName,
            keywords = firebaseVideo.tags + listOf("صباح", "نشاط", "بداية_اليوم", "morning")
        )
    }

    /**
     * توليد محتوى عادي
     */
    private suspend fun generateRegularContent(
        firebaseVideo: FirebaseVideo,
        channelName: String
    ): Result<VideoContentResponse> {
        return geminiRepository.generateVideoContent(
            videoDescription = firebaseVideo.description,
            channelName = channelName,
            keywords = firebaseVideo.tags
        )
    }

    /**
     * تحليل السياق الحالي
     */
    private fun analyzeCurrentContext(): ContentContext {
        val calendar = Calendar.getInstance()
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        val month = calendar.get(Calendar.MONTH)
        val dayOfMonth = calendar.get(Calendar.DAY_OF_MONTH)
        
        return ContentContext(
            hour = hour,
            dayOfWeek = dayOfWeek,
            month = month,
            dayOfMonth = dayOfMonth,
            isMorning = hour in 6..11,
            isAfternoon = hour in 12..17,
            isEvening = hour in 18..23,
            isNight = hour in 0..5,
            isWeekend = dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY,
            isSpecialOccasion = detectSpecialOccasion(month, dayOfMonth),
            occasion = getOccasionName(month, dayOfMonth)
        )
    }

    /**
     * اكتشاف المناسبات الخاصة
     */
    private fun detectSpecialOccasion(month: Int, dayOfMonth: Int): Boolean {
        return when {
            // رمضان (تقريبي - يحتاج تحديث سنوي)
            month == Calendar.MARCH && dayOfMonth >= 10 -> true
            month == Calendar.APRIL && dayOfMonth <= 10 -> true
            
            // عيد الفطر
            month == Calendar.APRIL && dayOfMonth in 10..13 -> true
            
            // عيد الأضحى
            month == Calendar.JUNE && dayOfMonth in 15..19 -> true
            
            // رأس السنة الهجرية
            month == Calendar.JULY && dayOfMonth in 28..31 -> true
            
            // رأس السنة الميلادية
            month == Calendar.JANUARY && dayOfMonth == 1 -> true
            month == Calendar.DECEMBER && dayOfMonth >= 25 -> true
            
            else -> false
        }
    }

    /**
     * الحصول على اسم المناسبة
     */
    private fun getOccasionName(month: Int, dayOfMonth: Int): String? {
        return when {
            month == Calendar.MARCH && dayOfMonth >= 10 -> "رمضان"
            month == Calendar.APRIL && dayOfMonth <= 10 -> "رمضان"
            month == Calendar.APRIL && dayOfMonth in 10..13 -> "عيد الفطر"
            month == Calendar.JUNE && dayOfMonth in 15..19 -> "عيد الأضحى"
            month == Calendar.JULY && dayOfMonth in 28..31 -> "رأس السنة الهجرية"
            month == Calendar.JANUARY && dayOfMonth == 1 -> "رأس السنة الميلادية"
            month == Calendar.DECEMBER && dayOfMonth >= 25 -> "رأس السنة الميلادية"
            else -> null
        }
    }

    /**
     * الحصول على كلمات مفتاحية للمناسبة
     */
    private fun getOccasionKeywords(occasion: String?): List<String> {
        return when (occasion) {
            "رمضان" -> listOf("رمضان", "صيام", "إفطار", "سحور", "ramadan")
            "عيد الفطر" -> listOf("عيد", "فطر", "تهنئة", "احتفال", "eid")
            "عيد الأضحى" -> listOf("عيد", "أضحى", "حج", "تهنئة", "eid")
            "رأس السنة الهجرية" -> listOf("هجرية", "سنة_جديدة", "تهنئة")
            "رأس السنة الميلادية" -> listOf("سنة_جديدة", "احتفال", "تهنئة", "newyear")
            else -> emptyList()
        }
    }

    /**
     * تحسين المحتوى بناءً على إعدادات المستخدم
     */
    private fun optimizeContentForUser(
        content: VideoContentResponse,
        settings: UserSettings
    ): VideoContentResponse {
        return content.copy(
            title = optimizeTitle(content.title, settings),
            description = optimizeDescription(content.description, settings),
            hashtags = optimizeHashtags(content.hashtags, settings)
        )
    }

    /**
     * تحسين العنوان
     */
    private fun optimizeTitle(title: String, settings: UserSettings): String {
        return when {
            title.length > 60 -> title.take(57) + "..."
            !title.contains(settings.channelName) && title.length < 50 -> 
                "$title | ${settings.channelName}"
            else -> title
        }
    }

    /**
     * تحسين الوصف
     */
    private fun optimizeDescription(description: String, settings: UserSettings): String {
        return buildString {
            append(description)
            
            if (!description.contains(settings.channelName)) {
                appendLine()
                appendLine("📺 قناة: ${settings.channelName}")
            }
            
            appendLine()
            appendLine("🔔 اشترك في القناة وفعل الجرس للمزيد!")
        }
    }

    /**
     * تحسين الهاشتاغات
     */
    private fun optimizeHashtags(hashtags: List<String>, settings: UserSettings): List<String> {
        val optimizedHashtags = hashtags.toMutableList()
        
        // إضافة هاشتاغ القناة إذا لم يكن موجوداً
        val channelHashtag = "#${settings.channelName.replace(" ", "_")}"
        if (!optimizedHashtags.contains(channelHashtag)) {
            optimizedHashtags.add(channelHashtag)
        }
        
        // إضافة هاشتاغات أساسية
        val basicHashtags = listOf("#shorts", "#viral", "#trending", "#مضحك")
        basicHashtags.forEach { hashtag ->
            if (!optimizedHashtags.contains(hashtag)) {
                optimizedHashtags.add(hashtag)
            }
        }
        
        return optimizedHashtags.take(15) // حد أقصى 15 هاشتاغ
    }
}

/**
 * سياق المحتوى
 */
data class ContentContext(
    val hour: Int,
    val dayOfWeek: Int,
    val month: Int,
    val dayOfMonth: Int,
    val isMorning: Boolean,
    val isAfternoon: Boolean,
    val isEvening: Boolean,
    val isNight: Boolean,
    val isWeekend: Boolean,
    val isSpecialOccasion: Boolean,
    val occasion: String?
)
