package com.example.aiagent.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SchedulerManager_Factory implements Factory<SchedulerManager> {
  private final Provider<Context> contextProvider;

  public SchedulerManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public SchedulerManager get() {
    return newInstance(contextProvider.get());
  }

  public static SchedulerManager_Factory create(Provider<Context> contextProvider) {
    return new SchedulerManager_Factory(contextProvider);
  }

  public static SchedulerManager newInstance(Context context) {
    return new SchedulerManager(context);
  }
}
