package com.example.aiagent.utils

import android.content.Context
import android.util.Log
import com.google.auth.oauth2.GoogleCredentials
import com.google.auth.oauth2.ServiceAccountCredentials
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.util.*

/**
 * مدير المصادقة لـ YouTube
 * يتعامل مع Service Account للحصول على رموز الوصول
 */
class YouTubeAuthManager(private val context: Context) {
    
    private var serviceAccountCredentials: ServiceAccountCredentials? = null
    private var cachedAccessToken: String? = null
    private var tokenExpiryTime: Long = 0
    
    companion object {
        private const val TAG = "YouTubeAuthManager"
        private val SCOPES = listOf(
            "https://www.googleapis.com/auth/youtube.upload",
            "https://www.googleapis.com/auth/youtube",
            "https://www.googleapis.com/auth/youtubepartner"
        )
        private const val TOKEN_BUFFER_TIME = 5 * 60 * 1000L // 5 دقائق قبل انتهاء الصلاحية
    }

    /**
     * تهيئة المصادقة باستخدام Service Account
     */
    fun initialize(serviceAccountJsonPath: String): Boolean {
        return try {
            Log.d(TAG, "Initializing YouTube authentication")
            
            val serviceAccountFile = File(serviceAccountJsonPath)
            if (!serviceAccountFile.exists()) {
                Log.e(TAG, "Service account file not found: $serviceAccountJsonPath")
                return false
            }
            
            val inputStream = FileInputStream(serviceAccountFile)
            val credentials = ServiceAccountCredentials.fromStream(inputStream)
                .createScoped(SCOPES) as ServiceAccountCredentials

            serviceAccountCredentials = credentials
            inputStream.close()
            
            Log.d(TAG, "YouTube authentication initialized successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing YouTube authentication", e)
            false
        }
    }

    /**
     * تهيئة المصادقة من محتوى JSON
     */
    fun initializeFromJson(serviceAccountJson: String): Boolean {
        return try {
            Log.d(TAG, "Initializing YouTube authentication from JSON")
            
            val credentials = ServiceAccountCredentials.fromStream(
                serviceAccountJson.byteInputStream()
            ).createScoped(SCOPES) as ServiceAccountCredentials

            serviceAccountCredentials = credentials
            
            Log.d(TAG, "YouTube authentication initialized from JSON successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing YouTube authentication from JSON", e)
            false
        }
    }

    /**
     * الحصول على رمز الوصول
     */
    suspend fun getAccessToken(): String? = withContext(Dispatchers.IO) {
        try {
            val credentials = serviceAccountCredentials
            if (credentials == null) {
                Log.e(TAG, "Service account credentials not initialized")
                return@withContext null
            }
            
            // التحقق من صحة الرمز المخزن مؤقتاً
            if (isTokenValid()) {
                Log.d(TAG, "Using cached access token")
                return@withContext cachedAccessToken
            }
            
            // تحديث الرمز
            Log.d(TAG, "Refreshing access token")
            credentials.refresh()
            
            val accessToken = credentials.accessToken?.tokenValue
            if (accessToken != null) {
                cachedAccessToken = accessToken
                tokenExpiryTime = credentials.accessToken?.expirationTime?.time ?: 0
                
                Log.d(TAG, "Access token refreshed successfully")
                accessToken
            } else {
                Log.e(TAG, "Failed to get access token")
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting access token", e)
            null
        }
    }

    /**
     * التحقق من صحة الرمز المخزن
     */
    private fun isTokenValid(): Boolean {
        return cachedAccessToken != null && 
               tokenExpiryTime > 0 && 
               System.currentTimeMillis() < (tokenExpiryTime - TOKEN_BUFFER_TIME)
    }

    /**
     * إلغاء صحة الرمز المخزن
     */
    fun invalidateToken() {
        cachedAccessToken = null
        tokenExpiryTime = 0
        Log.d(TAG, "Access token invalidated")
    }

    /**
     * التحقق من تهيئة المصادقة
     */
    fun isInitialized(): Boolean {
        return serviceAccountCredentials != null
    }

    /**
     * الحصول على معلومات Service Account
     */
    fun getServiceAccountInfo(): ServiceAccountInfo? {
        return try {
            val credentials = serviceAccountCredentials
            if (credentials != null) {
                ServiceAccountInfo(
                    clientEmail = credentials.clientEmail,
                    projectId = credentials.projectId,
                    privateKeyId = credentials.privateKeyId
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting service account info", e)
            null
        }
    }

    /**
     * اختبار الاتصال بـ YouTube API
     */
    suspend fun testConnection(): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            val accessToken = getAccessToken()
            if (accessToken != null) {
                // يمكن إضافة طلب بسيط لاختبار الاتصال هنا
                Log.d(TAG, "YouTube API connection test successful")
                true
            } else {
                Log.e(TAG, "YouTube API connection test failed - no access token")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "YouTube API connection test failed", e)
            false
        }
    }

    /**
     * حفظ إعدادات Service Account في التفضيلات
     */
    fun saveServiceAccountPath(path: String) {
        try {
            val prefs = context.getSharedPreferences("youtube_auth", Context.MODE_PRIVATE)
            prefs.edit()
                .putString("service_account_path", path)
                .apply()
            Log.d(TAG, "Service account path saved")
        } catch (e: Exception) {
            Log.e(TAG, "Error saving service account path", e)
        }
    }

    /**
     * تحميل إعدادات Service Account من التفضيلات
     */
    fun loadServiceAccountPath(): String? {
        return try {
            val prefs = context.getSharedPreferences("youtube_auth", Context.MODE_PRIVATE)
            prefs.getString("service_account_path", null)
        } catch (e: Exception) {
            Log.e(TAG, "Error loading service account path", e)
            null
        }
    }

    /**
     * التحقق من صلاحيات Service Account
     */
    fun validateServiceAccountPermissions(): List<String> {
        val issues = mutableListOf<String>()
        
        try {
            val credentials = serviceAccountCredentials
            if (credentials == null) {
                issues.add("Service Account غير مهيأ")
                return issues
            }
            
            // التحقق من الصلاحيات المطلوبة
            val requiredScopes = SCOPES
            val grantedScopes = credentials.scopes ?: emptySet()
            
            for (scope in requiredScopes) {
                if (!grantedScopes.contains(scope)) {
                    issues.add("صلاحية مفقودة: $scope")
                }
            }
            
            // التحقق من معلومات أساسية
            if (credentials.clientEmail.isNullOrEmpty()) {
                issues.add("Client Email مفقود")
            }
            
            if (credentials.projectId.isNullOrEmpty()) {
                issues.add("Project ID مفقود")
            }
            
        } catch (e: Exception) {
            issues.add("خطأ في التحقق من الصلاحيات: ${e.message}")
        }
        
        return issues
    }

    /**
     * تنظيف الموارد
     */
    fun cleanup() {
        serviceAccountCredentials = null
        cachedAccessToken = null
        tokenExpiryTime = 0
        Log.d(TAG, "YouTube auth manager cleaned up")
    }
}

/**
 * معلومات Service Account
 */
data class ServiceAccountInfo(
    val clientEmail: String,
    val projectId: String,
    val privateKeyId: String
)
