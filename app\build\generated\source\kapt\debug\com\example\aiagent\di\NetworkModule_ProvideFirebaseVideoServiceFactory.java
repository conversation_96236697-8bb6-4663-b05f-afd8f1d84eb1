package com.example.aiagent.di;

import com.example.aiagent.data.firebase.FirebaseVideoService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideFirebaseVideoServiceFactory implements Factory<FirebaseVideoService> {
  @Override
  public FirebaseVideoService get() {
    return provideFirebaseVideoService();
  }

  public static NetworkModule_ProvideFirebaseVideoServiceFactory create() {
    return InstanceHolder.INSTANCE;
  }

  public static FirebaseVideoService provideFirebaseVideoService() {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideFirebaseVideoService());
  }

  private static final class InstanceHolder {
    private static final NetworkModule_ProvideFirebaseVideoServiceFactory INSTANCE = new NetworkModule_ProvideFirebaseVideoServiceFactory();
  }
}
