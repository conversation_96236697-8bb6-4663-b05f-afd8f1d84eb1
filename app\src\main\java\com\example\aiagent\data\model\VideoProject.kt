package com.example.aiagent.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.serialization.Serializable
import java.util.Date

/**
 * نموذج مشروع الفيديو
 */
@Entity(tableName = "video_projects")
@Serializable
data class VideoProject(
    @PrimaryKey
    val id: String,
    val title: String,
    val description: String,
    val hashtags: List<String>,
    val sourceVideoUrl: String,
    val processedVideoPath: String? = null,
    val thumbnailPath: String? = null,
    val watermarkText: String,
    val status: VideoStatus,
    val createdAt: Long = System.currentTimeMillis(),
    val scheduledUploadTime: Long? = null,
    val youtubeVideoId: String? = null,
    val uploadProgress: Int = 0,
    val errorMessage: String? = null,
    val retryCount: Int = 0,
    val maxRetries: Int = 3
)

/**
 * حالات الفيديو
 */
enum class VideoStatus {
    PENDING,           // في الانتظار
    DOWNLOADING,       // جاري التحميل
    PROCESSING,        // جاري المعالجة
    GENERATING_CONTENT, // جاري توليد المحتوى
    READY_TO_UPLOAD,   // جاهز للرفع
    UPLOADING,         // جاري الرفع
    UPLOADED,          // تم الرفع
    FAILED,            // فشل
    CANCELLED          // ملغي
}

/**
 * إعدادات المستخدم
 */
@Entity(tableName = "user_settings")
@Serializable
data class UserSettings(
    @PrimaryKey
    val id: Int = 1,
    val channelName: String,
    val watermarkStyle: WatermarkStyle,
    val uploadSchedule: UploadSchedule,
    val videoQuality: VideoQuality,
    val autoUpload: Boolean = true,
    val batteryOptimizationRequested: Boolean = false,
    val geminiApiKey: String? = null,
    val youtubeServiceAccountPath: String? = null
)

/**
 * أنماط العلامة المائية
 */
enum class WatermarkStyle {
    BOTTOM_RIGHT,
    BOTTOM_LEFT,
    TOP_RIGHT,
    TOP_LEFT,
    CENTER
}

/**
 * جدولة الرفع
 */
@Serializable
data class UploadSchedule(
    val intervalHours: Int = 24,
    val startTime: String = "09:00", // HH:mm
    val enabled: Boolean = true
)

/**
 * جودة الفيديو
 */
enum class VideoQuality {
    LOW,      // 480p
    MEDIUM,   // 720p
    HIGH      // 1080p
}

/**
 * إحصائيات التطبيق
 */
@Entity(tableName = "app_statistics")
@Serializable
data class AppStatistics(
    @PrimaryKey
    val id: Int = 1,
    val totalVideosProcessed: Int = 0,
    val totalVideosUploaded: Int = 0,
    val totalVideosFailed: Int = 0,
    val lastUploadTime: Long? = null,
    val totalProcessingTime: Long = 0, // بالميلي ثانية
    val averageProcessingTime: Long = 0
)

/**
 * سجل العمليات
 */
@Entity(tableName = "operation_logs")
@Serializable
data class OperationLog(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val videoProjectId: String,
    val operation: String,
    val status: String,
    val message: String,
    val timestamp: Long = System.currentTimeMillis(),
    val duration: Long? = null
)
