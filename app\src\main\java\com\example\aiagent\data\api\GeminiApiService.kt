package com.example.aiagent.data.api

import android.content.Context
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.GenerateContentResponse
import com.google.ai.client.generativeai.type.generationConfig
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import javax.inject.Inject
import javax.inject.Singleton

/**
 * خدمة Gemini AI لتوليد المحتوى
 */
@Singleton
class GeminiApiService @Inject constructor(
    private val context: Context
) {
    
    private var generativeModel: GenerativeModel? = null
    private val json = Json { ignoreUnknownKeys = true }
    
    /**
     * تهيئة النموذج مع مفتاح API
     */
    fun initializeModel(apiKey: String) {
        generativeModel = GenerativeModel(
            modelName = "gemini-1.5-flash",
            apiKey = apiKey,
            generationConfig = generationConfig {
                temperature = 0.7f
                topK = 40
                topP = 0.95f
                maxOutputTokens = 1024
            }
        )
    }
    
    /**
     * توليد عنوان ووصف وهاشتاغات للفيديو
     */
    suspend fun generateVideoContent(
        videoDescription: String,
        channelName: String,
        keywords: List<String> = emptyList()
    ): Result<VideoContentResponse> = withContext(Dispatchers.IO) {
        try {
            val model = generativeModel ?: return@withContext Result.failure(
                Exception("Gemini API غير مهيأ. يرجى إضافة مفتاح API أولاً.")
            )
            
            val prompt = buildPrompt(videoDescription, channelName, keywords)
            val response = model.generateContent(prompt)
            
            val content = response.text ?: return@withContext Result.failure(
                Exception("لم يتم الحصول على رد من Gemini AI")
            )
            
            parseVideoContent(content)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * توليد عناوين متعددة للاختيار من بينها
     */
    suspend fun generateMultipleTitles(
        videoDescription: String,
        count: Int = 5
    ): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            val model = generativeModel ?: return@withContext Result.failure(
                Exception("Gemini API غير مهيأ")
            )

            val prompt = """
                اكتب $count عناوين جذابة ومختلفة لفيديو يوتيوب شورتس بناءً على هذا الوصف:
                "$videoDescription"

                المتطلبات:
                - كل عنوان في سطر منفصل مرقم
                - العناوين باللغة العربية
                - جذابة ومثيرة للاهتمام وتحفز على النقر
                - مناسبة لمحتوى الشورتس
                - لا تزيد عن 60 حرف
                - استخدم رموز تعبيرية مناسبة
                - تنوع في الأسلوب (سؤال، تعجب، إثارة فضول)

                العناوين:
                1.
                2.
                3.
                4.
                5.
            """.trimIndent()

            val response = model.generateContent(prompt)
            val content = response.text ?: return@withContext Result.failure(
                Exception("لم يتم الحصول على رد من Gemini AI")
            )

            val titles = content.split("\n")
                .map { it.trim() }
                .filter { it.isNotBlank() }
                .map { line ->
                    // إزالة الترقيم من بداية السطر
                    line.replace(Regex("^\\d+\\.\\s*"), "")
                        .replace(Regex("^-\\s*"), "")
                        .trim()
                }
                .filter { it.isNotEmpty() }
                .take(count)

            Result.success(titles)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * توليد محتوى مخصص حسب الوقت والمناسبة
     */
    suspend fun generateTimedContent(
        videoDescription: String,
        channelName: String,
        timeOfDay: String, // "صباح", "مساء", "ليل"
        occasion: String? = null // "رمضان", "عيد", "جمعة"
    ): Result<VideoContentResponse> = withContext(Dispatchers.IO) {
        try {
            val model = generativeModel ?: return@withContext Result.failure(
                Exception("Gemini API غير مهيأ")
            )

            val occasionText = if (occasion != null) {
                "المناسبة: $occasion"
            } else ""

            val prompt = buildTimedPrompt(videoDescription, channelName, timeOfDay, occasionText)
            val response = model.generateContent(prompt)

            val content = response.text ?: return@withContext Result.failure(
                Exception("لم يتم الحصول على رد من Gemini AI")
            )

            parseVideoContent(content)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تحليل الترندات وتوليد محتوى مناسب
     */
    suspend fun generateTrendingContent(
        videoDescription: String,
        channelName: String,
        trendingTopics: List<String>
    ): Result<VideoContentResponse> = withContext(Dispatchers.IO) {
        try {
            val model = generativeModel ?: return@withContext Result.failure(
                Exception("Gemini API غير مهيأ")
            )

            val trendsText = "المواضيع الرائجة: ${trendingTopics.joinToString(", ")}"

            val prompt = """
                أنت خبير في إنشاء محتوى يوتيوب شورتس يواكب الترندات.

                بناءً على:
                - وصف الفيديو: "$videoDescription"
                - اسم القناة: "$channelName"
                - $trendsText

                اكتب محتوى يدمج الترندات الحالية بطريقة طبيعية ومناسبة:

                العنوان: [عنوان يتضمن عناصر من الترندات]

                الوصف: [وصف يربط المحتوى بالترندات الحالية]

                الهاشتاغات: [هاشتاغات تتضمن الترندات والمواضيع الرائجة]
            """.trimIndent()

            val response = model.generateContent(prompt)
            val content = response.text ?: return@withContext Result.failure(
                Exception("لم يتم الحصول على رد من Gemini AI")
            )

            parseVideoContent(content)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun buildPrompt(
        videoDescription: String,
        channelName: String,
        keywords: List<String>
    ): String {
        val keywordsText = if (keywords.isNotEmpty()) {
            "الكلمات المفتاحية: ${keywords.joinToString(", ")}"
        } else ""

        return """
            أنت خبير في إنشاء محتوى يوتيوب شورتس باللغة العربية متخصص في المحتوى المضحك والترفيهي.

            بناءً على المعلومات التالية:
            - وصف الفيديو: "$videoDescription"
            - اسم القناة: "$channelName"
            $keywordsText

            اكتب محتوى للفيديو بالتنسيق التالي بالضبط:

            العنوان: [عنوان جذاب ومضحك لا يزيد عن 60 حرف مع رموز تعبيرية مناسبة]

            الوصف: [وصف تفصيلي وجذاب يتضمن:
            - مقدمة مضحكة أو مثيرة للاهتمام
            - وصف المحتوى بطريقة شيقة
            - دعوة للتفاعل (لايك، اشتراك، تعليق)
            - سؤال للمتابعين لتحفيز التعليقات
            - ذكر اسم القناة
            - رموز تعبيرية مناسبة]

            الهاشتاغات: [12-15 هاشتاغ متنوع باللغة العربية والإنجليزية يتضمن:
            - هاشتاغات عامة للمحتوى المضحك
            - هاشتاغات خاصة بموضوع الفيديو
            - هاشتاغات رائجة على يوتيوب شورتس
            - هاشتاغات للتفاعل]

            المتطلبات:
            - المحتوى باللغة العربية
            - العنوان جذاب ومثير للفضول
            - الوصف يحفز على المشاهدة والتفاعل
            - الهاشتاغات متنوعة ومناسبة للمحتوى المضحك
            - استخدام رموز تعبيرية بذكاء
        """.trimIndent()
    }

    private fun buildTimedPrompt(
        videoDescription: String,
        channelName: String,
        timeOfDay: String,
        occasionText: String
    ): String {
        return """
            أنت خبير في إنشاء محتوى يوتيوب شورتس مخصص للأوقات والمناسبات.

            بناءً على:
            - وصف الفيديو: "$videoDescription"
            - اسم القناة: "$channelName"
            - وقت النشر: $timeOfDay
            $occasionText

            اكتب محتوى مناسب للوقت والمناسبة:

            العنوان: [عنوان يناسب الوقت/المناسبة]

            الوصف: [وصف يتضمن تحية مناسبة للوقت/المناسبة]

            الهاشتاغات: [هاشتاغات تتضمن الوقت/المناسبة]
        """.trimIndent()
    }
    
    private fun parseVideoContent(content: String): Result<VideoContentResponse> {
        return try {
            val lines = content.split("\n").map { it.trim() }
            
            var title = ""
            var description = ""
            var hashtags = ""
            
            var currentSection = ""
            val descriptionLines = mutableListOf<String>()
            
            for (line in lines) {
                when {
                    line.startsWith("العنوان:") -> {
                        title = line.substringAfter("العنوان:").trim()
                        currentSection = "title"
                    }
                    line.startsWith("الوصف:") -> {
                        description = line.substringAfter("الوصف:").trim()
                        currentSection = "description"
                        if (description.isNotEmpty()) {
                            descriptionLines.add(description)
                        }
                    }
                    line.startsWith("الهاشتاغات:") -> {
                        hashtags = line.substringAfter("الهاشتاغات:").trim()
                        currentSection = "hashtags"
                    }
                    line.isNotEmpty() && currentSection == "description" -> {
                        descriptionLines.add(line)
                    }
                }
            }
            
            if (descriptionLines.isNotEmpty()) {
                description = descriptionLines.joinToString("\n")
            }
            
            val hashtagList = hashtags.split(" ")
                .map { it.trim() }
                .filter { it.startsWith("#") && it.length > 1 }
            
            Result.success(
                VideoContentResponse(
                    title = title,
                    description = description,
                    hashtags = hashtagList
                )
            )
        } catch (e: Exception) {
            Result.failure(Exception("خطأ في تحليل المحتوى المولد: ${e.message}"))
        }
    }
}

/**
 * استجابة محتوى الفيديو من Gemini
 */
@Serializable
data class VideoContentResponse(
    val title: String,
    val description: String,
    val hashtags: List<String>
)
