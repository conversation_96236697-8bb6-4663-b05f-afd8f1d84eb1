1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.aiagent"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="35" />
10
11    <!-- صلاحيات الشبكة -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- صلاحيات التخزين -->
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:11:5-80
17-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:11:22-77
18    <uses-permission
18-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:12:5-13:38
19        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
19-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:12:22-78
20        android:maxSdkVersion="28" />
20-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:13:9-35
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:14:5-75
21-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:14:22-72
22    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
22-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:15:5-76
22-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:15:22-73
23
24    <!-- صلاحيات الخدمات في الخلفية -->
25    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
25-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:18:5-77
25-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:18:22-74
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROCESSING" />
26-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:19:5-94
26-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:19:22-91
27    <uses-permission android:name="android.permission.WAKE_LOCK" />
27-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:20:5-68
27-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:20:22-65
28    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
28-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:21:5-95
28-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:21:22-92
29
30    <!-- صلاحيات الإشعارات -->
31    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
31-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:24:5-77
31-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:24:22-74
32
33    <!-- صلاحيات الجدولة -->
34    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
34-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:27:5-79
34-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:27:22-76
35    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
35-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:28:5-74
35-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:28:22-71
36
37    <!-- صلاحيات بدء التطبيق عند الإقلاع -->
38    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
38-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:31:5-81
38-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:31:22-78
39    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
39-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
39-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
40    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
40-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
40-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
41    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
41-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
41-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
42    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
42-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
42-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
43
44    <permission
44-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
45        android:name="com.example.aiagent.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.example.aiagent.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
49
50    <application
50-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:33:5-115:19
51        android:name="com.example.aiagent.AiAgentApplication"
51-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:34:9-43
52        android:allowBackup="true"
52-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:35:9-35
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
54        android:dataExtractionRules="@xml/data_extraction_rules"
54-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:36:9-65
55        android:debuggable="true"
56        android:extractNativeLibs="false"
57        android:fullBackupContent="@xml/backup_rules"
57-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:37:9-54
58        android:icon="@mipmap/ic_launcher"
58-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:38:9-43
59        android:label="@string/app_name"
59-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:39:9-41
60        android:largeHeap="true"
60-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:44:9-33
61        android:requestLegacyExternalStorage="true"
61-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:43:9-52
62        android:roundIcon="@mipmap/ic_launcher_round"
62-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:40:9-54
63        android:supportsRtl="true"
63-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:41:9-35
64        android:theme="@style/Theme.AiAgent" >
64-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:42:9-45
65
66        <!-- النشاط الرئيسي -->
67        <activity
67-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:48:9-57:20
68            android:name="com.example.aiagent.MainActivity"
68-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:49:13-41
69            android:exported="true"
69-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:50:13-36
70            android:screenOrientation="portrait"
70-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:52:13-49
71            android:theme="@style/Theme.AiAgent" >
71-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:51:13-49
72            <intent-filter>
72-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:53:13-56:29
73                <action android:name="android.intent.action.MAIN" />
73-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:54:17-69
73-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:54:25-66
74
75                <category android:name="android.intent.category.LAUNCHER" />
75-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:55:17-77
75-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:55:27-74
76            </intent-filter>
77        </activity>
78
79        <!-- خدمة معالجة الفيديو في الخلفية -->
80        <service
80-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:60:9-64:63
81            android:name="com.example.aiagent.service.VideoProcessingService"
81-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:61:13-59
82            android:enabled="true"
82-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:62:13-35
83            android:exported="false"
83-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:63:13-37
84            android:foregroundServiceType="mediaProcessing" />
84-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:64:13-60
85
86        <!-- خدمة رفع الفيديوهات -->
87        <service
87-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:67:9-71:56
88            android:name="com.example.aiagent.service.YouTubeUploadService"
88-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:68:13-57
89            android:enabled="true"
89-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:69:13-35
90            android:exported="false"
90-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:70:13-37
91            android:foregroundServiceType="dataSync" />
91-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:71:13-53
92
93        <!-- مستقبل إعادة التشغيل -->
94        <receiver
94-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:74:9-84:20
95            android:name="com.example.aiagent.receiver.BootReceiver"
95-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:75:13-50
96            android:enabled="true"
96-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:76:13-35
97            android:exported="true" >
97-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:77:13-36
98            <intent-filter android:priority="1000" >
98-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:78:13-83:29
98-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:78:28-51
99                <action android:name="android.intent.action.BOOT_COMPLETED" />
99-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:79:17-79
99-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:79:25-76
100                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
100-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:80:17-84
100-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:80:25-81
101                <action android:name="android.intent.action.PACKAGE_REPLACED" />
101-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:81:17-81
101-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:81:25-78
102
103                <data android:scheme="package" />
103-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:82:17-50
103-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:82:23-47
104            </intent-filter>
105        </receiver>
106
107        <!-- مستقبل المنبهات -->
108        <receiver
108-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:87:9-90:40
109            android:name="com.example.aiagent.receiver.AlarmReceiver"
109-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:88:13-51
110            android:enabled="true"
110-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:89:13-35
111            android:exported="false" />
111-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:90:13-37
112
113        <!-- مزود الملفات -->
114        <provider
115            android:name="androidx.core.content.FileProvider"
115-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:94:13-62
116            android:authorities="com.example.aiagent.fileprovider"
116-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:95:13-64
117            android:exported="false"
117-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:96:13-37
118            android:grantUriPermissions="true" >
118-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:97:13-47
119            <meta-data
119-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:98:13-100:54
120                android:name="android.support.FILE_PROVIDER_PATHS"
120-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:99:17-67
121                android:resource="@xml/file_paths" />
121-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:100:17-51
122        </provider>
123
124        <!-- إعدادات Work Manager -->
125        <provider
126            android:name="androidx.startup.InitializationProvider"
126-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:105:13-67
127            android:authorities="com.example.aiagent.androidx-startup"
127-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:106:13-68
128            android:exported="false" >
128-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:107:13-37
129            <meta-data
129-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
130                android:name="androidx.emoji2.text.EmojiCompatInitializer"
130-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
131                android:value="androidx.startup" />
131-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
132            <meta-data
132-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
133-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
134                android:value="androidx.startup" />
134-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
136-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
137                android:value="androidx.startup" />
137-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
138        </provider>
139
140        <activity
140-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
141            android:name="androidx.compose.ui.tooling.PreviewActivity"
141-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
142            android:exported="true" />
142-->[androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
143        <activity
143-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
144            android:name="androidx.activity.ComponentActivity"
144-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
145            android:exported="true" />
145-->[androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
146
147        <service
147-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:39:9-45:35
148            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
148-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:40:13-88
149            android:directBootAware="false"
149-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:41:13-44
150            android:enabled="@bool/enable_system_alarm_service_default"
150-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:42:13-72
151            android:exported="false" />
151-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:43:13-37
152        <service
152-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:46:9-52:35
153            android:name="androidx.work.impl.background.systemjob.SystemJobService"
153-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:47:13-84
154            android:directBootAware="false"
154-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:48:13-44
155            android:enabled="@bool/enable_system_job_service_default"
155-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:49:13-70
156            android:exported="true"
156-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:50:13-36
157            android:permission="android.permission.BIND_JOB_SERVICE" />
157-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:51:13-69
158        <service
158-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:53:9-59:35
159            android:name="androidx.work.impl.foreground.SystemForegroundService"
159-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:54:13-81
160            android:directBootAware="false"
160-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:55:13-44
161            android:enabled="@bool/enable_system_foreground_service_default"
161-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:56:13-77
162            android:exported="false" />
162-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:57:13-37
163
164        <receiver
164-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:61:9-66:35
165            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
165-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:62:13-88
166            android:directBootAware="false"
166-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:63:13-44
167            android:enabled="true"
167-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:64:13-35
168            android:exported="false" />
168-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:65:13-37
169        <receiver
169-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:67:9-77:20
170            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
170-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:68:13-106
171            android:directBootAware="false"
171-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:69:13-44
172            android:enabled="false"
172-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:70:13-36
173            android:exported="false" >
173-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:71:13-37
174            <intent-filter>
174-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:73:13-76:29
175                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
175-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:17-87
175-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:25-84
176                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
176-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:17-90
176-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:25-87
177            </intent-filter>
178        </receiver>
179        <receiver
179-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:78:9-88:20
180            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
180-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:79:13-104
181            android:directBootAware="false"
181-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:80:13-44
182            android:enabled="false"
182-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:81:13-36
183            android:exported="false" >
183-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:82:13-37
184            <intent-filter>
184-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:84:13-87:29
185                <action android:name="android.intent.action.BATTERY_OKAY" />
185-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:17-77
185-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:25-74
186                <action android:name="android.intent.action.BATTERY_LOW" />
186-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:17-76
186-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:25-73
187            </intent-filter>
188        </receiver>
189        <receiver
189-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:89:9-99:20
190            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
190-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:90:13-104
191            android:directBootAware="false"
191-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:91:13-44
192            android:enabled="false"
192-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:92:13-36
193            android:exported="false" >
193-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:93:13-37
194            <intent-filter>
194-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:95:13-98:29
195                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
195-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:17-83
195-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:25-80
196                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
196-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:17-82
196-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:25-79
197            </intent-filter>
198        </receiver>
199        <receiver
199-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:100:9-109:20
200            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
200-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:101:13-103
201            android:directBootAware="false"
201-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:102:13-44
202            android:enabled="false"
202-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:103:13-36
203            android:exported="false" >
203-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:104:13-37
204            <intent-filter>
204-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:106:13-108:29
205                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
205-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:17-79
205-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:25-76
206            </intent-filter>
207        </receiver>
208        <receiver
208-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:110:9-121:20
209            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
209-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:111:13-88
210            android:directBootAware="false"
210-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:112:13-44
211            android:enabled="false"
211-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:113:13-36
212            android:exported="false" >
212-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:114:13-37
213            <intent-filter>
213-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:116:13-120:29
214                <action android:name="android.intent.action.BOOT_COMPLETED" />
214-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:79:17-79
214-->C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:79:25-76
215                <action android:name="android.intent.action.TIME_SET" />
215-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:17-73
215-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:25-70
216                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
216-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:17-81
216-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:25-78
217            </intent-filter>
218        </receiver>
219        <receiver
219-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:122:9-131:20
220            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
220-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:123:13-99
221            android:directBootAware="false"
221-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:124:13-44
222            android:enabled="@bool/enable_system_alarm_service_default"
222-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:125:13-72
223            android:exported="false" >
223-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:126:13-37
224            <intent-filter>
224-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:128:13-130:29
225                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
225-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:17-98
225-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:25-95
226            </intent-filter>
227        </receiver>
228        <receiver
228-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:132:9-142:20
229            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
229-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:133:13-78
230            android:directBootAware="false"
230-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:134:13-44
231            android:enabled="true"
231-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:135:13-35
232            android:exported="true"
232-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:136:13-36
233            android:permission="android.permission.DUMP" >
233-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:137:13-57
234            <intent-filter>
234-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:139:13-141:29
235                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
235-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:17-88
235-->[androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:25-85
236            </intent-filter>
237        </receiver>
238
239        <service
239-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:9:9-15:19
240            android:name="com.google.firebase.components.ComponentDiscoveryService"
240-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:10:13-84
241            android:directBootAware="true"
241-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
242            android:exported="false" >
242-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:11:13-37
243            <meta-data
243-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:12:13-14:85
244                android:name="com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar"
244-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:13:17-125
245                android:value="com.google.firebase.components.ComponentRegistrar" />
245-->[com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:14:17-82
246            <meta-data
246-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
247                android:name="com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar"
247-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
248                android:value="com.google.firebase.components.ComponentRegistrar" />
248-->[com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
249            <meta-data
249-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
250                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
250-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
251                android:value="com.google.firebase.components.ComponentRegistrar" />
251-->[com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
252            <meta-data
252-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
253                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
253-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
254                android:value="com.google.firebase.components.ComponentRegistrar" />
254-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
255            <meta-data
255-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
256                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
256-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
257                android:value="com.google.firebase.components.ComponentRegistrar" />
257-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
258            <meta-data
258-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
259                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
259-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
260                android:value="com.google.firebase.components.ComponentRegistrar" />
260-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
261            <meta-data
261-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
262                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
262-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
263                android:value="com.google.firebase.components.ComponentRegistrar" />
263-->[com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
264            <meta-data
264-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
265                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
265-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
266                android:value="com.google.firebase.components.ComponentRegistrar" />
266-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
267            <meta-data
267-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
268                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
268-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
269                android:value="com.google.firebase.components.ComponentRegistrar" />
269-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
270            <meta-data
270-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
271                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
271-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
272                android:value="com.google.firebase.components.ComponentRegistrar" />
272-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
273            <meta-data
273-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
274                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
274-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
275                android:value="com.google.firebase.components.ComponentRegistrar" />
275-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
276            <meta-data
276-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
277                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
277-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
278                android:value="com.google.firebase.components.ComponentRegistrar" />
278-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
279            <meta-data
279-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
280                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
280-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
281                android:value="com.google.firebase.components.ComponentRegistrar" />
281-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
282            <meta-data
282-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
283                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
283-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
284                android:value="com.google.firebase.components.ComponentRegistrar" />
284-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
285        </service>
286
287        <property
287-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
288            android:name="android.adservices.AD_SERVICES_CONFIG"
288-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
289            android:resource="@xml/ga_ad_services_config" />
289-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
290
291        <provider
291-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
292            android:name="com.google.firebase.provider.FirebaseInitProvider"
292-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
293            android:authorities="com.example.aiagent.firebaseinitprovider"
293-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
294            android:directBootAware="true"
294-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
295            android:exported="false"
295-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
296            android:initOrder="100" />
296-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
297
298        <receiver
298-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
299            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
299-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
300            android:enabled="true"
300-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
301            android:exported="false" >
301-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
302        </receiver>
303
304        <service
304-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
305            android:name="com.google.android.gms.measurement.AppMeasurementService"
305-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
306            android:enabled="true"
306-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
307            android:exported="false" />
307-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
308        <service
308-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
309            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
309-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
310            android:enabled="true"
310-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
311            android:exported="false"
311-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
312            android:permission="android.permission.BIND_JOB_SERVICE" />
312-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
313        <service
313-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
314            android:name="androidx.room.MultiInstanceInvalidationService"
314-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
315            android:directBootAware="true"
315-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
316            android:exported="false" />
316-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
317
318        <activity
318-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
319            android:name="com.google.android.gms.common.api.GoogleApiActivity"
319-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
320            android:exported="false"
320-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
321            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
321-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
322
323        <receiver
323-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
324            android:name="androidx.profileinstaller.ProfileInstallReceiver"
324-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
325            android:directBootAware="false"
325-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
326            android:enabled="true"
326-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
327            android:exported="true"
327-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
328            android:permission="android.permission.DUMP" >
328-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
329            <intent-filter>
329-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
330                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
330-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
330-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
331            </intent-filter>
332            <intent-filter>
332-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
333                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
333-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
333-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
334            </intent-filter>
335            <intent-filter>
335-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
336                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
336-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
336-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
337            </intent-filter>
338            <intent-filter>
338-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
339                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
339-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
339-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
340            </intent-filter>
341        </receiver>
342
343        <uses-library
343-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
344            android:name="android.ext.adservices"
344-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
345            android:required="false" />
345-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
346
347        <meta-data
347-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
348            android:name="com.google.android.gms.version"
348-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
349            android:value="@integer/google_play_services_version" />
349-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
350    </application>
351
352</manifest>
