package com.example.aiagent.service

import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.example.aiagent.AiAgentApplication
import com.example.aiagent.R
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.data.model.VideoStatus
import com.example.aiagent.data.repository.VideoProjectRepository
import com.example.aiagent.data.repository.YouTubeRepository
import com.example.aiagent.MainActivity
import com.example.aiagent.utils.NotificationHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * خدمة رفع الفيديوهات على يوتيوب
 * متوافقة مع Android 14+ وقيود Foreground Services
 */
@AndroidEntryPoint
class YouTubeUploadService : LifecycleService() {

    @Inject
    lateinit var videoProjectRepository: VideoProjectRepository
    
    @Inject
    lateinit var youTubeRepository: YouTubeRepository
    
    @Inject
    lateinit var notificationHelper: NotificationHelper

    private var currentProjectId: String? = null
    private var isUploading = false

    companion object {
        const val ACTION_START_UPLOAD = "START_UPLOAD"
        const val ACTION_STOP_UPLOAD = "STOP_UPLOAD"
        const val ACTION_UPLOAD_VIDEO = "UPLOAD_VIDEO"
        const val EXTRA_PROJECT_ID = "project_id"
        
        /**
         * بدء خدمة رفع الفيديو
         */
        fun startUpload(context: Context, projectId: String) {
            val intent = Intent(context, YouTubeUploadService::class.java).apply {
                action = ACTION_UPLOAD_VIDEO
                putExtra(EXTRA_PROJECT_ID, projectId)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        
        /**
         * إيقاف خدمة رفع الفيديو
         */
        fun stopUpload(context: Context) {
            val intent = Intent(context, YouTubeUploadService::class.java).apply {
                action = ACTION_STOP_UPLOAD
            }
            context.startService(intent)
        }
    }

    override fun onCreate() {
        super.onCreate()
        startForegroundService()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        
        when (intent?.action) {
            ACTION_UPLOAD_VIDEO -> {
                val projectId = intent.getStringExtra(EXTRA_PROJECT_ID)
                if (projectId != null) {
                    uploadVideo(projectId)
                }
            }
            ACTION_STOP_UPLOAD -> {
                stopUpload()
            }
        }
        
        return START_STICKY
    }

    override fun onBind(intent: Intent): IBinder? {
        super.onBind(intent)
        return null
    }

    /**
     * بدء الخدمة في المقدمة
     */
    private fun startForegroundService() {
        val notification = createNotification(
            title = "خدمة رفع يوتيوب نشطة",
            content = "جاهز لرفع الفيديوهات",
            progress = null
        )

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                AiAgentApplication.NOTIFICATION_ID_UPLOAD,
                notification,
                ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
            )
        } else {
            startForeground(AiAgentApplication.NOTIFICATION_ID_UPLOAD, notification)
        }
    }

    /**
     * رفع الفيديو على يوتيوب
     */
    private fun uploadVideo(projectId: String) {
        if (isUploading) {
            // إذا كان هناك رفع جاري، أضف المشروع للقائمة
            return
        }

        currentProjectId = projectId
        isUploading = true

        lifecycleScope.launch {
            try {
                val project = videoProjectRepository.getProjectById(projectId)
                if (project == null) {
                    notificationHelper.showError("مشروع غير موجود", "لم يتم العثور على المشروع المطلوب")
                    stopUpload()
                    return@launch
                }

                // التحقق من وجود الفيديو المعالج
                if (project.processedVideoPath.isNullOrEmpty()) {
                    notificationHelper.showError("فيديو غير جاهز", "لم يتم معالجة الفيديو بعد")
                    stopUpload()
                    return@launch
                }

                // تحديث حالة المشروع
                videoProjectRepository.updateProjectStatus(projectId, VideoStatus.UPLOADING)
                
                // تحديث الإشعار
                updateNotification("جاري رفع الفيديو", project.title, 0)

                // بدء رفع الفيديو
                val result = youTubeRepository.uploadVideo(
                    project = project,
                    onProgress = { progress ->
                        updateNotification("جاري رفع الفيديو", project.title, progress)
                        lifecycleScope.launch {
                            videoProjectRepository.updateUploadProgress(projectId, progress)
                        }
                    }
                )

                result.fold(
                    onSuccess = { youtubeVideoId ->
                        // تحديث المشروع بمعرف يوتيوب
                        videoProjectRepository.updateYouTubeVideoId(projectId, youtubeVideoId)
                        
                        // إشعار النجاح
                        notificationHelper.showSuccess(
                            "تم الرفع بنجاح",
                            "تم رفع فيديو: ${project.title}"
                        )
                        
                        // إشعار بالرابط
                        notificationHelper.showVideoUploaded(
                            project.title,
                            "https://youtube.com/watch?v=$youtubeVideoId"
                        )
                    },
                    onFailure = { error ->
                        // زيادة عداد المحاولات
                        videoProjectRepository.incrementRetryCount(projectId)
                        
                        val updatedProject = videoProjectRepository.getProjectById(projectId)
                        if (updatedProject != null && updatedProject.retryCount < updatedProject.maxRetries) {
                            // إعادة المحاولة
                            videoProjectRepository.updateProjectStatus(
                                projectId,
                                VideoStatus.READY_TO_UPLOAD,
                                "فشل الرفع - سيتم إعادة المحاولة: ${error.message}"
                            )
                            
                            notificationHelper.showWarning(
                                "سيتم إعادة المحاولة",
                                "فشل رفع ${project.title} - المحاولة ${updatedProject.retryCount + 1}/${updatedProject.maxRetries}"
                            )
                        } else {
                            // فشل نهائي
                            videoProjectRepository.updateProjectStatus(
                                projectId,
                                VideoStatus.FAILED,
                                "فشل الرفع نهائياً: ${error.message}"
                            )
                            
                            notificationHelper.showError(
                                "فشل رفع الفيديو",
                                error.message ?: "خطأ غير معروف"
                            )
                        }
                    }
                )

            } catch (e: Exception) {
                // معالجة الأخطاء العامة
                videoProjectRepository.updateProjectStatus(
                    projectId,
                    VideoStatus.FAILED,
                    "خطأ في خدمة الرفع: ${e.message}"
                )
                
                notificationHelper.showError(
                    "خطأ في خدمة الرفع",
                    e.message ?: "خطأ غير معروف"
                )
            } finally {
                stopUpload()
            }
        }
    }

    /**
     * إيقاف الرفع
     */
    private fun stopUpload() {
        isUploading = false
        currentProjectId = null
        
        // تحديث الإشعار
        updateNotification(
            "خدمة رفع يوتيوب نشطة",
            "جاهز لرفع الفيديوهات",
            null
        )
        
        // إيقاف الخدمة إذا لم تعد هناك مهام
        stopSelf()
    }

    /**
     * تحديث الإشعار
     */
    private fun updateNotification(title: String, content: String, progress: Int?) {
        val notification = createNotification(title, content, progress)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(AiAgentApplication.NOTIFICATION_ID_UPLOAD, notification)
    }

    /**
     * إنشاء الإشعار
     */
    private fun createNotification(title: String, content: String, progress: Int?): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val builder = NotificationCompat.Builder(this, AiAgentApplication.CHANNEL_UPLOAD)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_upload)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)

        // إضافة شريط التقدم إذا كان متاحاً
        if (progress != null) {
            builder.setProgress(100, progress, false)
                .setSubText("$progress%")
        }

        // إضافة زر الإيقاف
        val stopIntent = Intent(this, YouTubeUploadService::class.java).apply {
            action = ACTION_STOP_UPLOAD
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 0, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        builder.addAction(R.drawable.ic_stop, "إيقاف", stopPendingIntent)

        return builder.build()
    }

    override fun onDestroy() {
        super.onDestroy()
        isUploading = false
        currentProjectId = null
    }
}
