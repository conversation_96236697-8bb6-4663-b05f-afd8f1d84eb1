package com.example.aiagent.di;

import com.example.aiagent.data.api.GeminiApiService;
import com.example.aiagent.data.repository.GeminiRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideGeminiRepositoryFactory implements Factory<GeminiRepository> {
  private final Provider<GeminiApiService> apiServiceProvider;

  public NetworkModule_ProvideGeminiRepositoryFactory(
      Provider<GeminiApiService> apiServiceProvider) {
    this.apiServiceProvider = apiServiceProvider;
  }

  @Override
  public GeminiRepository get() {
    return provideGeminiRepository(apiServiceProvider.get());
  }

  public static NetworkModule_ProvideGeminiRepositoryFactory create(
      Provider<GeminiApiService> apiServiceProvider) {
    return new NetworkModule_ProvideGeminiRepositoryFactory(apiServiceProvider);
  }

  public static GeminiRepository provideGeminiRepository(GeminiApiService apiService) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideGeminiRepository(apiService));
  }
}
