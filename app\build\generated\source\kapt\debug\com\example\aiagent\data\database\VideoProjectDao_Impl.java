package com.example.aiagent.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.room.util.StringUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.aiagent.data.model.VideoProject;
import com.example.aiagent.data.model.VideoStatus;
import java.lang.Class;
import java.lang.Double;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class VideoProjectDao_Impl implements VideoProjectDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<VideoProject> __insertionAdapterOfVideoProject;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<VideoProject> __deletionAdapterOfVideoProject;

  private final EntityDeletionOrUpdateAdapter<VideoProject> __updateAdapterOfVideoProject;

  private final SharedSQLiteStatement __preparedStmtOfUpdateProjectStatus;

  private final SharedSQLiteStatement __preparedStmtOfUpdateProjectStatusWithError;

  private final SharedSQLiteStatement __preparedStmtOfUpdateUploadProgress;

  private final SharedSQLiteStatement __preparedStmtOfUpdateYouTubeVideoId;

  private final SharedSQLiteStatement __preparedStmtOfIncrementRetryCount;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProjectById;

  private final SharedSQLiteStatement __preparedStmtOfDeleteProjectsByStatus;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldProjects;

  public VideoProjectDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfVideoProject = new EntityInsertionAdapter<VideoProject>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `video_projects` (`id`,`title`,`description`,`hashtags`,`sourceVideoUrl`,`processedVideoPath`,`thumbnailPath`,`watermarkText`,`status`,`createdAt`,`scheduledUploadTime`,`youtubeVideoId`,`uploadProgress`,`errorMessage`,`retryCount`,`maxRetries`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final VideoProject entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getTitle() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getTitle());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        final String _tmp = __converters.fromStringList(entity.getHashtags());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp);
        }
        if (entity.getSourceVideoUrl() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSourceVideoUrl());
        }
        if (entity.getProcessedVideoPath() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getProcessedVideoPath());
        }
        if (entity.getThumbnailPath() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getThumbnailPath());
        }
        if (entity.getWatermarkText() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getWatermarkText());
        }
        final String _tmp_1 = __converters.fromVideoStatus(entity.getStatus());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        statement.bindLong(10, entity.getCreatedAt());
        if (entity.getScheduledUploadTime() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getScheduledUploadTime());
        }
        if (entity.getYoutubeVideoId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getYoutubeVideoId());
        }
        statement.bindLong(13, entity.getUploadProgress());
        if (entity.getErrorMessage() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getErrorMessage());
        }
        statement.bindLong(15, entity.getRetryCount());
        statement.bindLong(16, entity.getMaxRetries());
      }
    };
    this.__deletionAdapterOfVideoProject = new EntityDeletionOrUpdateAdapter<VideoProject>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `video_projects` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final VideoProject entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfVideoProject = new EntityDeletionOrUpdateAdapter<VideoProject>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `video_projects` SET `id` = ?,`title` = ?,`description` = ?,`hashtags` = ?,`sourceVideoUrl` = ?,`processedVideoPath` = ?,`thumbnailPath` = ?,`watermarkText` = ?,`status` = ?,`createdAt` = ?,`scheduledUploadTime` = ?,`youtubeVideoId` = ?,`uploadProgress` = ?,`errorMessage` = ?,`retryCount` = ?,`maxRetries` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final VideoProject entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        if (entity.getTitle() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getTitle());
        }
        if (entity.getDescription() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getDescription());
        }
        final String _tmp = __converters.fromStringList(entity.getHashtags());
        if (_tmp == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp);
        }
        if (entity.getSourceVideoUrl() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getSourceVideoUrl());
        }
        if (entity.getProcessedVideoPath() == null) {
          statement.bindNull(6);
        } else {
          statement.bindString(6, entity.getProcessedVideoPath());
        }
        if (entity.getThumbnailPath() == null) {
          statement.bindNull(7);
        } else {
          statement.bindString(7, entity.getThumbnailPath());
        }
        if (entity.getWatermarkText() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getWatermarkText());
        }
        final String _tmp_1 = __converters.fromVideoStatus(entity.getStatus());
        if (_tmp_1 == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, _tmp_1);
        }
        statement.bindLong(10, entity.getCreatedAt());
        if (entity.getScheduledUploadTime() == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, entity.getScheduledUploadTime());
        }
        if (entity.getYoutubeVideoId() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getYoutubeVideoId());
        }
        statement.bindLong(13, entity.getUploadProgress());
        if (entity.getErrorMessage() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getErrorMessage());
        }
        statement.bindLong(15, entity.getRetryCount());
        statement.bindLong(16, entity.getMaxRetries());
        if (entity.getId() == null) {
          statement.bindNull(17);
        } else {
          statement.bindString(17, entity.getId());
        }
      }
    };
    this.__preparedStmtOfUpdateProjectStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE video_projects SET status = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateProjectStatusWithError = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE video_projects SET status = ?, errorMessage = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateUploadProgress = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE video_projects SET uploadProgress = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateYouTubeVideoId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE video_projects SET youtubeVideoId = ?, status = ? WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfIncrementRetryCount = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE video_projects SET retryCount = retryCount + 1 WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteProjectById = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM video_projects WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteProjectsByStatus = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM video_projects WHERE status = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOldProjects = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM video_projects WHERE createdAt < ?";
        return _query;
      }
    };
  }

  @Override
  public Object insertProject(final VideoProject project,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfVideoProject.insert(project);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object insertProjects(final List<VideoProject> projects,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfVideoProject.insert(projects);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteProject(final VideoProject project,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfVideoProject.handle(project);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateProject(final VideoProject project,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfVideoProject.handle(project);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateProjectStatus(final String id, final VideoStatus status,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateProjectStatus.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromVideoStatus(status);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateProjectStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateProjectStatusWithError(final String id, final VideoStatus status,
      final String errorMessage, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateProjectStatusWithError.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromVideoStatus(status);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 2;
        if (errorMessage == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, errorMessage);
        }
        _argIndex = 3;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateProjectStatusWithError.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUploadProgress(final String id, final int progress,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateUploadProgress.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, progress);
        _argIndex = 2;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateUploadProgress.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateYouTubeVideoId(final String id, final String videoId,
      final VideoStatus status, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateYouTubeVideoId.acquire();
        int _argIndex = 1;
        if (videoId == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, videoId);
        }
        _argIndex = 2;
        final String _tmp = __converters.fromVideoStatus(status);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        _argIndex = 3;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateYouTubeVideoId.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object incrementRetryCount(final String id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfIncrementRetryCount.acquire();
        int _argIndex = 1;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfIncrementRetryCount.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteProjectById(final String id, final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProjectById.acquire();
        int _argIndex = 1;
        if (id == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, id);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteProjectById.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteProjectsByStatus(final VideoStatus status,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteProjectsByStatus.acquire();
        int _argIndex = 1;
        final String _tmp = __converters.fromVideoStatus(status);
        if (_tmp == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, _tmp);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteProjectsByStatus.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldProjects(final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldProjects.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldProjects.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<List<VideoProject>> getAllProjects() {
    final String _sql = "SELECT * FROM video_projects ORDER BY createdAt DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"video_projects"}, new Callable<List<VideoProject>>() {
      @Override
      @NonNull
      public List<VideoProject> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfHashtags = CursorUtil.getColumnIndexOrThrow(_cursor, "hashtags");
          final int _cursorIndexOfSourceVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "sourceVideoUrl");
          final int _cursorIndexOfProcessedVideoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "processedVideoPath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfWatermarkText = CursorUtil.getColumnIndexOrThrow(_cursor, "watermarkText");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfScheduledUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledUploadTime");
          final int _cursorIndexOfYoutubeVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "youtubeVideoId");
          final int _cursorIndexOfUploadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadProgress");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfMaxRetries = CursorUtil.getColumnIndexOrThrow(_cursor, "maxRetries");
          final List<VideoProject> _result = new ArrayList<VideoProject>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final VideoProject _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpHashtags;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfHashtags)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfHashtags);
            }
            _tmpHashtags = __converters.toStringList(_tmp);
            final String _tmpSourceVideoUrl;
            if (_cursor.isNull(_cursorIndexOfSourceVideoUrl)) {
              _tmpSourceVideoUrl = null;
            } else {
              _tmpSourceVideoUrl = _cursor.getString(_cursorIndexOfSourceVideoUrl);
            }
            final String _tmpProcessedVideoPath;
            if (_cursor.isNull(_cursorIndexOfProcessedVideoPath)) {
              _tmpProcessedVideoPath = null;
            } else {
              _tmpProcessedVideoPath = _cursor.getString(_cursorIndexOfProcessedVideoPath);
            }
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final String _tmpWatermarkText;
            if (_cursor.isNull(_cursorIndexOfWatermarkText)) {
              _tmpWatermarkText = null;
            } else {
              _tmpWatermarkText = _cursor.getString(_cursorIndexOfWatermarkText);
            }
            final VideoStatus _tmpStatus;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toVideoStatus(_tmp_1);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpScheduledUploadTime;
            if (_cursor.isNull(_cursorIndexOfScheduledUploadTime)) {
              _tmpScheduledUploadTime = null;
            } else {
              _tmpScheduledUploadTime = _cursor.getLong(_cursorIndexOfScheduledUploadTime);
            }
            final String _tmpYoutubeVideoId;
            if (_cursor.isNull(_cursorIndexOfYoutubeVideoId)) {
              _tmpYoutubeVideoId = null;
            } else {
              _tmpYoutubeVideoId = _cursor.getString(_cursorIndexOfYoutubeVideoId);
            }
            final int _tmpUploadProgress;
            _tmpUploadProgress = _cursor.getInt(_cursorIndexOfUploadProgress);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final int _tmpMaxRetries;
            _tmpMaxRetries = _cursor.getInt(_cursorIndexOfMaxRetries);
            _item = new VideoProject(_tmpId,_tmpTitle,_tmpDescription,_tmpHashtags,_tmpSourceVideoUrl,_tmpProcessedVideoPath,_tmpThumbnailPath,_tmpWatermarkText,_tmpStatus,_tmpCreatedAt,_tmpScheduledUploadTime,_tmpYoutubeVideoId,_tmpUploadProgress,_tmpErrorMessage,_tmpRetryCount,_tmpMaxRetries);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getProjectById(final String id,
      final Continuation<? super VideoProject> $completion) {
    final String _sql = "SELECT * FROM video_projects WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (id == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, id);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<VideoProject>() {
      @Override
      @Nullable
      public VideoProject call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfHashtags = CursorUtil.getColumnIndexOrThrow(_cursor, "hashtags");
          final int _cursorIndexOfSourceVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "sourceVideoUrl");
          final int _cursorIndexOfProcessedVideoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "processedVideoPath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfWatermarkText = CursorUtil.getColumnIndexOrThrow(_cursor, "watermarkText");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfScheduledUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledUploadTime");
          final int _cursorIndexOfYoutubeVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "youtubeVideoId");
          final int _cursorIndexOfUploadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadProgress");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfMaxRetries = CursorUtil.getColumnIndexOrThrow(_cursor, "maxRetries");
          final VideoProject _result;
          if (_cursor.moveToFirst()) {
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpHashtags;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfHashtags)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfHashtags);
            }
            _tmpHashtags = __converters.toStringList(_tmp);
            final String _tmpSourceVideoUrl;
            if (_cursor.isNull(_cursorIndexOfSourceVideoUrl)) {
              _tmpSourceVideoUrl = null;
            } else {
              _tmpSourceVideoUrl = _cursor.getString(_cursorIndexOfSourceVideoUrl);
            }
            final String _tmpProcessedVideoPath;
            if (_cursor.isNull(_cursorIndexOfProcessedVideoPath)) {
              _tmpProcessedVideoPath = null;
            } else {
              _tmpProcessedVideoPath = _cursor.getString(_cursorIndexOfProcessedVideoPath);
            }
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final String _tmpWatermarkText;
            if (_cursor.isNull(_cursorIndexOfWatermarkText)) {
              _tmpWatermarkText = null;
            } else {
              _tmpWatermarkText = _cursor.getString(_cursorIndexOfWatermarkText);
            }
            final VideoStatus _tmpStatus;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toVideoStatus(_tmp_1);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpScheduledUploadTime;
            if (_cursor.isNull(_cursorIndexOfScheduledUploadTime)) {
              _tmpScheduledUploadTime = null;
            } else {
              _tmpScheduledUploadTime = _cursor.getLong(_cursorIndexOfScheduledUploadTime);
            }
            final String _tmpYoutubeVideoId;
            if (_cursor.isNull(_cursorIndexOfYoutubeVideoId)) {
              _tmpYoutubeVideoId = null;
            } else {
              _tmpYoutubeVideoId = _cursor.getString(_cursorIndexOfYoutubeVideoId);
            }
            final int _tmpUploadProgress;
            _tmpUploadProgress = _cursor.getInt(_cursorIndexOfUploadProgress);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final int _tmpMaxRetries;
            _tmpMaxRetries = _cursor.getInt(_cursorIndexOfMaxRetries);
            _result = new VideoProject(_tmpId,_tmpTitle,_tmpDescription,_tmpHashtags,_tmpSourceVideoUrl,_tmpProcessedVideoPath,_tmpThumbnailPath,_tmpWatermarkText,_tmpStatus,_tmpCreatedAt,_tmpScheduledUploadTime,_tmpYoutubeVideoId,_tmpUploadProgress,_tmpErrorMessage,_tmpRetryCount,_tmpMaxRetries);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getProjectsByStatus(final VideoStatus status,
      final Continuation<? super List<VideoProject>> $completion) {
    final String _sql = "SELECT * FROM video_projects WHERE status = ? ORDER BY createdAt ASC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromVideoStatus(status);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<VideoProject>>() {
      @Override
      @NonNull
      public List<VideoProject> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfHashtags = CursorUtil.getColumnIndexOrThrow(_cursor, "hashtags");
          final int _cursorIndexOfSourceVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "sourceVideoUrl");
          final int _cursorIndexOfProcessedVideoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "processedVideoPath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfWatermarkText = CursorUtil.getColumnIndexOrThrow(_cursor, "watermarkText");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfScheduledUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledUploadTime");
          final int _cursorIndexOfYoutubeVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "youtubeVideoId");
          final int _cursorIndexOfUploadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadProgress");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfMaxRetries = CursorUtil.getColumnIndexOrThrow(_cursor, "maxRetries");
          final List<VideoProject> _result = new ArrayList<VideoProject>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final VideoProject _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpHashtags;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfHashtags)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfHashtags);
            }
            _tmpHashtags = __converters.toStringList(_tmp_1);
            final String _tmpSourceVideoUrl;
            if (_cursor.isNull(_cursorIndexOfSourceVideoUrl)) {
              _tmpSourceVideoUrl = null;
            } else {
              _tmpSourceVideoUrl = _cursor.getString(_cursorIndexOfSourceVideoUrl);
            }
            final String _tmpProcessedVideoPath;
            if (_cursor.isNull(_cursorIndexOfProcessedVideoPath)) {
              _tmpProcessedVideoPath = null;
            } else {
              _tmpProcessedVideoPath = _cursor.getString(_cursorIndexOfProcessedVideoPath);
            }
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final String _tmpWatermarkText;
            if (_cursor.isNull(_cursorIndexOfWatermarkText)) {
              _tmpWatermarkText = null;
            } else {
              _tmpWatermarkText = _cursor.getString(_cursorIndexOfWatermarkText);
            }
            final VideoStatus _tmpStatus;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toVideoStatus(_tmp_2);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpScheduledUploadTime;
            if (_cursor.isNull(_cursorIndexOfScheduledUploadTime)) {
              _tmpScheduledUploadTime = null;
            } else {
              _tmpScheduledUploadTime = _cursor.getLong(_cursorIndexOfScheduledUploadTime);
            }
            final String _tmpYoutubeVideoId;
            if (_cursor.isNull(_cursorIndexOfYoutubeVideoId)) {
              _tmpYoutubeVideoId = null;
            } else {
              _tmpYoutubeVideoId = _cursor.getString(_cursorIndexOfYoutubeVideoId);
            }
            final int _tmpUploadProgress;
            _tmpUploadProgress = _cursor.getInt(_cursorIndexOfUploadProgress);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final int _tmpMaxRetries;
            _tmpMaxRetries = _cursor.getInt(_cursorIndexOfMaxRetries);
            _item = new VideoProject(_tmpId,_tmpTitle,_tmpDescription,_tmpHashtags,_tmpSourceVideoUrl,_tmpProcessedVideoPath,_tmpThumbnailPath,_tmpWatermarkText,_tmpStatus,_tmpCreatedAt,_tmpScheduledUploadTime,_tmpYoutubeVideoId,_tmpUploadProgress,_tmpErrorMessage,_tmpRetryCount,_tmpMaxRetries);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getProjectsByStatuses(final List<? extends VideoStatus> statuses,
      final Continuation<? super List<VideoProject>> $completion) {
    final StringBuilder _stringBuilder = StringUtil.newStringBuilder();
    _stringBuilder.append("SELECT * FROM video_projects WHERE status IN (");
    final int _inputSize = statuses.size();
    StringUtil.appendPlaceholders(_stringBuilder, _inputSize);
    _stringBuilder.append(") ORDER BY createdAt ASC");
    final String _sql = _stringBuilder.toString();
    final int _argCount = 0 + _inputSize;
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, _argCount);
    int _argIndex = 1;
    for (VideoStatus _item : statuses) {
      final String _tmp = __converters.fromVideoStatus(_item);
      if (_tmp == null) {
        _statement.bindNull(_argIndex);
      } else {
        _statement.bindString(_argIndex, _tmp);
      }
      _argIndex++;
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<VideoProject>>() {
      @Override
      @NonNull
      public List<VideoProject> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfHashtags = CursorUtil.getColumnIndexOrThrow(_cursor, "hashtags");
          final int _cursorIndexOfSourceVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "sourceVideoUrl");
          final int _cursorIndexOfProcessedVideoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "processedVideoPath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfWatermarkText = CursorUtil.getColumnIndexOrThrow(_cursor, "watermarkText");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfScheduledUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledUploadTime");
          final int _cursorIndexOfYoutubeVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "youtubeVideoId");
          final int _cursorIndexOfUploadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadProgress");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfMaxRetries = CursorUtil.getColumnIndexOrThrow(_cursor, "maxRetries");
          final List<VideoProject> _result = new ArrayList<VideoProject>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final VideoProject _item_1;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpHashtags;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfHashtags)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfHashtags);
            }
            _tmpHashtags = __converters.toStringList(_tmp_1);
            final String _tmpSourceVideoUrl;
            if (_cursor.isNull(_cursorIndexOfSourceVideoUrl)) {
              _tmpSourceVideoUrl = null;
            } else {
              _tmpSourceVideoUrl = _cursor.getString(_cursorIndexOfSourceVideoUrl);
            }
            final String _tmpProcessedVideoPath;
            if (_cursor.isNull(_cursorIndexOfProcessedVideoPath)) {
              _tmpProcessedVideoPath = null;
            } else {
              _tmpProcessedVideoPath = _cursor.getString(_cursorIndexOfProcessedVideoPath);
            }
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final String _tmpWatermarkText;
            if (_cursor.isNull(_cursorIndexOfWatermarkText)) {
              _tmpWatermarkText = null;
            } else {
              _tmpWatermarkText = _cursor.getString(_cursorIndexOfWatermarkText);
            }
            final VideoStatus _tmpStatus;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toVideoStatus(_tmp_2);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpScheduledUploadTime;
            if (_cursor.isNull(_cursorIndexOfScheduledUploadTime)) {
              _tmpScheduledUploadTime = null;
            } else {
              _tmpScheduledUploadTime = _cursor.getLong(_cursorIndexOfScheduledUploadTime);
            }
            final String _tmpYoutubeVideoId;
            if (_cursor.isNull(_cursorIndexOfYoutubeVideoId)) {
              _tmpYoutubeVideoId = null;
            } else {
              _tmpYoutubeVideoId = _cursor.getString(_cursorIndexOfYoutubeVideoId);
            }
            final int _tmpUploadProgress;
            _tmpUploadProgress = _cursor.getInt(_cursorIndexOfUploadProgress);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final int _tmpMaxRetries;
            _tmpMaxRetries = _cursor.getInt(_cursorIndexOfMaxRetries);
            _item_1 = new VideoProject(_tmpId,_tmpTitle,_tmpDescription,_tmpHashtags,_tmpSourceVideoUrl,_tmpProcessedVideoPath,_tmpThumbnailPath,_tmpWatermarkText,_tmpStatus,_tmpCreatedAt,_tmpScheduledUploadTime,_tmpYoutubeVideoId,_tmpUploadProgress,_tmpErrorMessage,_tmpRetryCount,_tmpMaxRetries);
            _result.add(_item_1);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getScheduledProjects(final long currentTime, final VideoStatus status,
      final Continuation<? super List<VideoProject>> $completion) {
    final String _sql = "SELECT * FROM video_projects WHERE scheduledUploadTime <= ? AND status = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 2);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, currentTime);
    _argIndex = 2;
    final String _tmp = __converters.fromVideoStatus(status);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<VideoProject>>() {
      @Override
      @NonNull
      public List<VideoProject> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTitle = CursorUtil.getColumnIndexOrThrow(_cursor, "title");
          final int _cursorIndexOfDescription = CursorUtil.getColumnIndexOrThrow(_cursor, "description");
          final int _cursorIndexOfHashtags = CursorUtil.getColumnIndexOrThrow(_cursor, "hashtags");
          final int _cursorIndexOfSourceVideoUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "sourceVideoUrl");
          final int _cursorIndexOfProcessedVideoPath = CursorUtil.getColumnIndexOrThrow(_cursor, "processedVideoPath");
          final int _cursorIndexOfThumbnailPath = CursorUtil.getColumnIndexOrThrow(_cursor, "thumbnailPath");
          final int _cursorIndexOfWatermarkText = CursorUtil.getColumnIndexOrThrow(_cursor, "watermarkText");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfScheduledUploadTime = CursorUtil.getColumnIndexOrThrow(_cursor, "scheduledUploadTime");
          final int _cursorIndexOfYoutubeVideoId = CursorUtil.getColumnIndexOrThrow(_cursor, "youtubeVideoId");
          final int _cursorIndexOfUploadProgress = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadProgress");
          final int _cursorIndexOfErrorMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "errorMessage");
          final int _cursorIndexOfRetryCount = CursorUtil.getColumnIndexOrThrow(_cursor, "retryCount");
          final int _cursorIndexOfMaxRetries = CursorUtil.getColumnIndexOrThrow(_cursor, "maxRetries");
          final List<VideoProject> _result = new ArrayList<VideoProject>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final VideoProject _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final String _tmpTitle;
            if (_cursor.isNull(_cursorIndexOfTitle)) {
              _tmpTitle = null;
            } else {
              _tmpTitle = _cursor.getString(_cursorIndexOfTitle);
            }
            final String _tmpDescription;
            if (_cursor.isNull(_cursorIndexOfDescription)) {
              _tmpDescription = null;
            } else {
              _tmpDescription = _cursor.getString(_cursorIndexOfDescription);
            }
            final List<String> _tmpHashtags;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfHashtags)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfHashtags);
            }
            _tmpHashtags = __converters.toStringList(_tmp_1);
            final String _tmpSourceVideoUrl;
            if (_cursor.isNull(_cursorIndexOfSourceVideoUrl)) {
              _tmpSourceVideoUrl = null;
            } else {
              _tmpSourceVideoUrl = _cursor.getString(_cursorIndexOfSourceVideoUrl);
            }
            final String _tmpProcessedVideoPath;
            if (_cursor.isNull(_cursorIndexOfProcessedVideoPath)) {
              _tmpProcessedVideoPath = null;
            } else {
              _tmpProcessedVideoPath = _cursor.getString(_cursorIndexOfProcessedVideoPath);
            }
            final String _tmpThumbnailPath;
            if (_cursor.isNull(_cursorIndexOfThumbnailPath)) {
              _tmpThumbnailPath = null;
            } else {
              _tmpThumbnailPath = _cursor.getString(_cursorIndexOfThumbnailPath);
            }
            final String _tmpWatermarkText;
            if (_cursor.isNull(_cursorIndexOfWatermarkText)) {
              _tmpWatermarkText = null;
            } else {
              _tmpWatermarkText = _cursor.getString(_cursorIndexOfWatermarkText);
            }
            final VideoStatus _tmpStatus;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfStatus);
            }
            _tmpStatus = __converters.toVideoStatus(_tmp_2);
            final long _tmpCreatedAt;
            _tmpCreatedAt = _cursor.getLong(_cursorIndexOfCreatedAt);
            final Long _tmpScheduledUploadTime;
            if (_cursor.isNull(_cursorIndexOfScheduledUploadTime)) {
              _tmpScheduledUploadTime = null;
            } else {
              _tmpScheduledUploadTime = _cursor.getLong(_cursorIndexOfScheduledUploadTime);
            }
            final String _tmpYoutubeVideoId;
            if (_cursor.isNull(_cursorIndexOfYoutubeVideoId)) {
              _tmpYoutubeVideoId = null;
            } else {
              _tmpYoutubeVideoId = _cursor.getString(_cursorIndexOfYoutubeVideoId);
            }
            final int _tmpUploadProgress;
            _tmpUploadProgress = _cursor.getInt(_cursorIndexOfUploadProgress);
            final String _tmpErrorMessage;
            if (_cursor.isNull(_cursorIndexOfErrorMessage)) {
              _tmpErrorMessage = null;
            } else {
              _tmpErrorMessage = _cursor.getString(_cursorIndexOfErrorMessage);
            }
            final int _tmpRetryCount;
            _tmpRetryCount = _cursor.getInt(_cursorIndexOfRetryCount);
            final int _tmpMaxRetries;
            _tmpMaxRetries = _cursor.getInt(_cursorIndexOfMaxRetries);
            _item = new VideoProject(_tmpId,_tmpTitle,_tmpDescription,_tmpHashtags,_tmpSourceVideoUrl,_tmpProcessedVideoPath,_tmpThumbnailPath,_tmpWatermarkText,_tmpStatus,_tmpCreatedAt,_tmpScheduledUploadTime,_tmpYoutubeVideoId,_tmpUploadProgress,_tmpErrorMessage,_tmpRetryCount,_tmpMaxRetries);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getProjectCountByStatus(final VideoStatus status,
      final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM video_projects WHERE status = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromVideoStatus(status);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp_1;
            if (_cursor.isNull(0)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getInt(0);
            }
            _result = _tmp_1;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTotalProjectCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM video_projects";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getAverageUploadProgress(final VideoStatus status,
      final Continuation<? super Double> $completion) {
    final String _sql = "SELECT AVG(uploadProgress) FROM video_projects WHERE status = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    final String _tmp = __converters.fromVideoStatus(status);
    if (_tmp == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, _tmp);
    }
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Double>() {
      @Override
      @Nullable
      public Double call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Double _result;
          if (_cursor.moveToFirst()) {
            final Double _tmp_1;
            if (_cursor.isNull(0)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getDouble(0);
            }
            _result = _tmp_1;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getTodayProjectCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM video_projects WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now')";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getYesterdayProjectCount(final Continuation<? super Integer> $completion) {
    final String _sql = "SELECT COUNT(*) FROM video_projects WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now', '-1 day')";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<Integer>() {
      @Override
      @NonNull
      public Integer call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final Integer _result;
          if (_cursor.moveToFirst()) {
            final Integer _tmp;
            if (_cursor.isNull(0)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getInt(0);
            }
            _result = _tmp;
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
