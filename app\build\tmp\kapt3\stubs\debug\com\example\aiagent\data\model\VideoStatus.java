package com.example.aiagent.data.model;

/**
 * حالات الفيديو
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\u000b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b\u00a8\u0006\f"}, d2 = {"Lcom/example/aiagent/data/model/VideoStatus;", "", "(Ljava/lang/String;I)V", "PENDING", "DOWNLOADING", "PROCESSING", "GENERATING_CONTENT", "READY_TO_UPLOAD", "UPLOADING", "UPLOADED", "FAILED", "CANCELLED", "app_debug"})
public enum VideoStatus {
    /*public static final*/ PENDING /* = new PENDING() */,
    /*public static final*/ DOWNLOADING /* = new DOWNLOADING() */,
    /*public static final*/ PROCESSING /* = new PROCESSING() */,
    /*public static final*/ GENERATING_CONTENT /* = new GENERATING_CONTENT() */,
    /*public static final*/ READY_TO_UPLOAD /* = new READY_TO_UPLOAD() */,
    /*public static final*/ UPLOADING /* = new UPLOADING() */,
    /*public static final*/ UPLOADED /* = new UPLOADED() */,
    /*public static final*/ FAILED /* = new FAILED() */,
    /*public static final*/ CANCELLED /* = new CANCELLED() */;
    
    VideoStatus() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.aiagent.data.model.VideoStatus> getEntries() {
        return null;
    }
}