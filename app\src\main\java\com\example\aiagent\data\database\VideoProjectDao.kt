package com.example.aiagent.data.database

import androidx.room.*
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.data.model.VideoStatus
import kotlinx.coroutines.flow.Flow

/**
 * DAO لإدارة مشاريع الفيديو
 */
@Dao
interface VideoProjectDao {
    
    @Query("SELECT * FROM video_projects ORDER BY createdAt DESC")
    fun getAllProjects(): Flow<List<VideoProject>>
    
    @Query("SELECT * FROM video_projects WHERE id = :id")
    suspend fun getProjectById(id: String): VideoProject?
    
    @Query("SELECT * FROM video_projects WHERE status = :status ORDER BY createdAt ASC")
    suspend fun getProjectsByStatus(status: VideoStatus): List<VideoProject>
    
    @Query("SELECT * FROM video_projects WHERE status IN (:statuses) ORDER BY createdAt ASC")
    suspend fun getProjectsByStatuses(statuses: List<VideoStatus>): List<VideoProject>
    
    @Query("SELECT * FROM video_projects WHERE scheduledUploadTime <= :currentTime AND status = :status")
    suspend fun getScheduledProjects(currentTime: Long, status: VideoStatus = VideoStatus.READY_TO_UPLOAD): List<VideoProject>
    
    @Query("SELECT COUNT(*) FROM video_projects WHERE status = :status")
    suspend fun getProjectCountByStatus(status: VideoStatus): Int
    
    @Query("SELECT COUNT(*) FROM video_projects")
    suspend fun getTotalProjectCount(): Int
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProject(project: VideoProject)
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProjects(projects: List<VideoProject>)
    
    @Update
    suspend fun updateProject(project: VideoProject)
    
    @Query("UPDATE video_projects SET status = :status WHERE id = :id")
    suspend fun updateProjectStatus(id: String, status: VideoStatus)
    
    @Query("UPDATE video_projects SET status = :status, errorMessage = :errorMessage WHERE id = :id")
    suspend fun updateProjectStatusWithError(id: String, status: VideoStatus, errorMessage: String)
    
    @Query("UPDATE video_projects SET uploadProgress = :progress WHERE id = :id")
    suspend fun updateUploadProgress(id: String, progress: Int)
    
    @Query("UPDATE video_projects SET youtubeVideoId = :videoId, status = :status WHERE id = :id")
    suspend fun updateYouTubeVideoId(id: String, videoId: String, status: VideoStatus)
    
    @Query("UPDATE video_projects SET retryCount = retryCount + 1 WHERE id = :id")
    suspend fun incrementRetryCount(id: String)
    
    @Delete
    suspend fun deleteProject(project: VideoProject)
    
    @Query("DELETE FROM video_projects WHERE id = :id")
    suspend fun deleteProjectById(id: String)
    
    @Query("DELETE FROM video_projects WHERE status = :status")
    suspend fun deleteProjectsByStatus(status: VideoStatus)
    
    @Query("DELETE FROM video_projects WHERE createdAt < :timestamp")
    suspend fun deleteOldProjects(timestamp: Long)
    
    // استعلامات إحصائية
    @Query("SELECT AVG(uploadProgress) FROM video_projects WHERE status = :status")
    suspend fun getAverageUploadProgress(status: VideoStatus = VideoStatus.UPLOADING): Double?
    
    @Query("SELECT COUNT(*) FROM video_projects WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now')")
    suspend fun getTodayProjectCount(): Int
    
    @Query("SELECT COUNT(*) FROM video_projects WHERE DATE(createdAt/1000, 'unixepoch') = DATE('now', '-1 day')")
    suspend fun getYesterdayProjectCount(): Int
}
