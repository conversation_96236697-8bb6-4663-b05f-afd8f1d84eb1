package com.example.aiagent.ui;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u0014\n\u0000\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0000\u001a\b\u0010\u0005\u001a\u00020\u0006H\u0007\"\u0017\u0010\u0000\u001a\b\u0012\u0004\u0012\u00020\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0003\u0010\u0004\u00a8\u0006\u0007"}, d2 = {"bottomNavItems", "", "Lcom/example/aiagent/ui/BottomNavItem;", "getBottomNavItems", "()Ljava/util/List;", "AiAgentApp", "", "app_debug"})
public final class AiAgentAppKt {
    @org.jetbrains.annotations.NotNull()
    private static final java.util.List<com.example.aiagent.ui.BottomNavItem> bottomNavItems = null;
    
    /**
     * التطبيق الرئيسي مع التنقل
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void AiAgentApp() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static final java.util.List<com.example.aiagent.ui.BottomNavItem> getBottomNavItems() {
        return null;
    }
}