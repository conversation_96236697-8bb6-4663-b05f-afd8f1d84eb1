package com.example.aiagent.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000@\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\u001a\b\u0010\u0000\u001a\u00020\u0001H\u0003\u001a\u001e\u0010\u0002\u001a\u00020\u00012\u0006\u0010\u0003\u001a\u00020\u00042\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00010\u0006H\u0003\u001a*\u0010\u0007\u001a\u00020\u00012\f\u0010\b\u001a\b\u0012\u0004\u0012\u00020\u00040\t2\u0012\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00010\u000bH\u0007\u001a\u0010\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000eH\u0003\u001a\u0010\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0002\u001a\u0015\u0010\u0013\u001a\u00020\u00142\u0006\u0010\r\u001a\u00020\u000eH\u0002\u00a2\u0006\u0002\u0010\u0015\u001a\u0010\u0010\u0016\u001a\u00020\u00102\u0006\u0010\r\u001a\u00020\u000eH\u0002\u00a8\u0006\u0017"}, d2 = {"EmptyStateCard", "", "ProjectActivityCard", "project", "Lcom/example/aiagent/data/model/VideoProject;", "onClick", "Lkotlin/Function0;", "RecentActivitySection", "recentProjects", "", "onProjectClick", "Lkotlin/Function1;", "StatusIcon", "status", "Lcom/example/aiagent/data/model/VideoStatus;", "formatDate", "", "timestamp", "", "getStatusColor", "Landroidx/compose/ui/graphics/Color;", "(Lcom/example/aiagent/data/model/VideoStatus;)J", "getStatusText", "app_debug"})
public final class RecentActivitySectionKt {
    
    /**
     * قسم النشاط الحديث
     */
    @androidx.compose.runtime.Composable()
    public static final void RecentActivitySection(@org.jetbrains.annotations.NotNull()
    java.util.List<com.example.aiagent.data.model.VideoProject> recentProjects, @org.jetbrains.annotations.NotNull()
    kotlin.jvm.functions.Function1<? super com.example.aiagent.data.model.VideoProject, kotlin.Unit> onProjectClick) {
    }
    
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    private static final void ProjectActivityCard(com.example.aiagent.data.model.VideoProject project, kotlin.jvm.functions.Function0<kotlin.Unit> onClick) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatusIcon(com.example.aiagent.data.model.VideoStatus status) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void EmptyStateCard() {
    }
    
    private static final java.lang.String getStatusText(com.example.aiagent.data.model.VideoStatus status) {
        return null;
    }
    
    private static final long getStatusColor(com.example.aiagent.data.model.VideoStatus status) {
        return 0L;
    }
    
    private static final java.lang.String formatDate(long timestamp) {
        return null;
    }
}