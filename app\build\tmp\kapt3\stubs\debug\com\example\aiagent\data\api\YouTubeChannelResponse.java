package com.example.aiagent.data.api;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\f\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0087\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\u0002\u0010\bJ\t\u0010\u000f\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u0010\u001a\u00020\u0005H\u00c6\u0003J\u000b\u0010\u0011\u001a\u0004\u0018\u00010\u0007H\u00c6\u0003J)\u0010\u0012\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u00c6\u0001J\u0013\u0010\u0013\u001a\u00020\u00142\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\t\u0010\u0016\u001a\u00020\u0017H\u00d6\u0001J\t\u0010\u0018\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\fR\u0013\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u00a2\u0006\b\n\u0000\u001a\u0004\b\r\u0010\u000e\u00a8\u0006\u0019"}, d2 = {"Lcom/example/aiagent/data/api/YouTubeChannelResponse;", "", "id", "", "snippet", "Lcom/example/aiagent/data/api/YouTubeChannelSnippet;", "statistics", "Lcom/example/aiagent/data/api/YouTubeChannelStatistics;", "(Ljava/lang/String;Lcom/example/aiagent/data/api/YouTubeChannelSnippet;Lcom/example/aiagent/data/api/YouTubeChannelStatistics;)V", "getId", "()Ljava/lang/String;", "getSnippet", "()Lcom/example/aiagent/data/api/YouTubeChannelSnippet;", "getStatistics", "()Lcom/example/aiagent/data/api/YouTubeChannelStatistics;", "component1", "component2", "component3", "copy", "equals", "", "other", "hashCode", "", "toString", "app_debug"})
public final class YouTubeChannelResponse {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String id = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.data.api.YouTubeChannelSnippet snippet = null;
    @org.jetbrains.annotations.Nullable()
    private final com.example.aiagent.data.api.YouTubeChannelStatistics statistics = null;
    
    public YouTubeChannelResponse(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.api.YouTubeChannelSnippet snippet, @org.jetbrains.annotations.Nullable()
    com.example.aiagent.data.api.YouTubeChannelStatistics statistics) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.api.YouTubeChannelSnippet getSnippet() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.aiagent.data.api.YouTubeChannelStatistics getStatistics() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.api.YouTubeChannelSnippet component2() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final com.example.aiagent.data.api.YouTubeChannelStatistics component3() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.api.YouTubeChannelResponse copy(@org.jetbrains.annotations.NotNull()
    java.lang.String id, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.api.YouTubeChannelSnippet snippet, @org.jetbrains.annotations.Nullable()
    com.example.aiagent.data.api.YouTubeChannelStatistics statistics) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
}