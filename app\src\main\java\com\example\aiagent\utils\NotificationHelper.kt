package com.example.aiagent.utils

import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.app.NotificationCompat
import com.example.aiagent.AiAgentApplication
import com.example.aiagent.R
import com.example.aiagent.ui.MainActivity
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مساعد الإشعارات
 * يدير جميع أنواع الإشعارات في التطبيق
 */
@Singleton
class NotificationHelper @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

    /**
     * إشعار نجاح العملية
     */
    fun showSuccess(title: String, message: String) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, AiAgentApplication.CHANNEL_SUCCESS)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_success)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        notificationManager.notify(
            generateNotificationId(),
            notification
        )
    }

    /**
     * إشعار خطأ
     */
    fun showError(title: String, message: String) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, AiAgentApplication.CHANNEL_ERROR)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_error)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .build()

        notificationManager.notify(
            generateNotificationId(),
            notification
        )
    }

    /**
     * إشعار تحذير
     */
    fun showWarning(title: String, message: String) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, AiAgentApplication.CHANNEL_ERROR)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_warning)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .build()

        notificationManager.notify(
            generateNotificationId(),
            notification
        )
    }

    /**
     * إشعار معلومات
     */
    fun showInfo(title: String, message: String) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, AiAgentApplication.CHANNEL_SUCCESS)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_info)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()

        notificationManager.notify(
            generateNotificationId(),
            notification
        )
    }

    /**
     * إشعار رفع فيديو بنجاح مع رابط
     */
    fun showVideoUploaded(videoTitle: String, youtubeUrl: String) {
        // إنشاء Intent لفتح الفيديو على يوتيوب
        val youtubeIntent = Intent(Intent.ACTION_VIEW, Uri.parse(youtubeUrl))
        val youtubePendingIntent = PendingIntent.getActivity(
            context, 0, youtubeIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // إنشاء Intent لفتح التطبيق
        val appIntent = Intent(context, MainActivity::class.java)
        val appPendingIntent = PendingIntent.getActivity(
            context, 1, appIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, AiAgentApplication.CHANNEL_SUCCESS)
            .setContentTitle("تم رفع الفيديو بنجاح! 🎉")
            .setContentText("$videoTitle - اضغط لمشاهدة على يوتيوب")
            .setSmallIcon(R.drawable.ic_youtube)
            .setContentIntent(youtubePendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .addAction(
                R.drawable.ic_youtube,
                "مشاهدة على يوتيوب",
                youtubePendingIntent
            )
            .addAction(
                R.drawable.ic_app,
                "فتح التطبيق",
                appPendingIntent
            )
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText("تم رفع الفيديو '$videoTitle' بنجاح على يوتيوب! اضغط لمشاهدته.")
            )
            .build()

        notificationManager.notify(
            generateNotificationId(),
            notification
        )
    }

    /**
     * إشعار طلب صلاحيات
     */
    fun showPermissionRequest(title: String, message: String, actionText: String, actionIntent: PendingIntent) {
        val notification = NotificationCompat.Builder(context, AiAgentApplication.CHANNEL_ERROR)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_permission)
            .setContentIntent(actionIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .addAction(R.drawable.ic_settings, actionText, actionIntent)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .build()

        notificationManager.notify(
            generateNotificationId(),
            notification
        )
    }

    /**
     * إشعار تقدم العملية
     */
    fun showProgress(title: String, message: String, progress: Int, max: Int = 100): Int {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notificationId = generateNotificationId()
        
        val notification = NotificationCompat.Builder(context, AiAgentApplication.CHANNEL_VIDEO_PROCESSING)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_processing)
            .setContentIntent(pendingIntent)
            .setProgress(max, progress, false)
            .setOngoing(true)
            .setAutoCancel(false)
            .setSubText("$progress%")
            .build()

        notificationManager.notify(notificationId, notification)
        return notificationId
    }

    /**
     * تحديث إشعار التقدم
     */
    fun updateProgress(notificationId: Int, title: String, message: String, progress: Int, max: Int = 100) {
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, AiAgentApplication.CHANNEL_VIDEO_PROCESSING)
            .setContentTitle(title)
            .setContentText(message)
            .setSmallIcon(R.drawable.ic_processing)
            .setContentIntent(pendingIntent)
            .setProgress(max, progress, false)
            .setOngoing(true)
            .setAutoCancel(false)
            .setSubText("$progress%")
            .build()

        notificationManager.notify(notificationId, notification)
    }

    /**
     * إلغاء إشعار
     */
    fun cancelNotification(notificationId: Int) {
        notificationManager.cancel(notificationId)
    }

    /**
     * إلغاء جميع الإشعارات
     */
    fun cancelAllNotifications() {
        notificationManager.cancelAll()
    }

    /**
     * توليد معرف إشعار فريد
     */
    private fun generateNotificationId(): Int {
        return (System.currentTimeMillis() % Int.MAX_VALUE).toInt()
    }
}
