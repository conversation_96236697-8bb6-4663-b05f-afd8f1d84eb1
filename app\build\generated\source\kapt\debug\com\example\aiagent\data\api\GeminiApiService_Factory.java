package com.example.aiagent.data.api;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class GeminiApiService_Factory implements Factory<GeminiApiService> {
  private final Provider<Context> contextProvider;

  public GeminiApiService_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public GeminiApiService get() {
    return newInstance(contextProvider.get());
  }

  public static GeminiApiService_Factory create(Provider<Context> contextProvider) {
    return new GeminiApiService_Factory(contextProvider);
  }

  public static GeminiApiService newInstance(Context context) {
    return new GeminiApiService(context);
  }
}
