package com.example.aiagent.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.util.Log
import com.arthenica.ffmpegkit.FFmpegKit
import com.arthenica.ffmpegkit.FFmpegSession
import com.arthenica.ffmpegkit.ReturnCode
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.data.model.WatermarkStyle
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * معالج الفيديو
 * يتعامل مع تحميل ومعالجة وإضافة العلامة المائية للفيديوهات
 */
@Singleton
class VideoProcessor @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "VideoProcessor"
        private const val MAX_VIDEO_DURATION = 60 // ثانية
        private const val TARGET_RESOLUTION = "720x1280" // 9:16 aspect ratio
        private const val TARGET_BITRATE = "1500k"
        private const val TARGET_FPS = "30"
    }

    /**
     * معالجة الفيديو الكاملة
     */
    suspend fun processVideo(
        project: VideoProject,
        onProgress: (Int) -> Unit
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting video processing for project: ${project.id}")
            
            // إنشاء مجلد العمل
            val workDir = createWorkDirectory(project.id)
            
            // تحميل الفيديو
            onProgress(10)
            val downloadedVideoPath = downloadVideo(project.sourceVideoUrl, workDir)
                ?: return@withContext Result.failure(Exception("فشل في تحميل الفيديو"))
            
            // تحليل الفيديو
            onProgress(20)
            val videoInfo = analyzeVideo(downloadedVideoPath)
                ?: return@withContext Result.failure(Exception("فشل في تحليل الفيديو"))
            
            // قص الفيديو إذا كان طويلاً
            onProgress(30)
            val trimmedVideoPath = if (videoInfo.duration > MAX_VIDEO_DURATION) {
                trimVideo(downloadedVideoPath, workDir, MAX_VIDEO_DURATION)
            } else {
                downloadedVideoPath
            }
            
            // تحسين جودة الفيديو
            onProgress(50)
            val optimizedVideoPath = optimizeVideo(trimmedVideoPath, workDir)
                ?: return@withContext Result.failure(Exception("فشل في تحسين الفيديو"))
            
            // إنشاء العلامة المائية
            onProgress(70)
            val watermarkPath = createWatermark(project.watermarkText, workDir)
                ?: return@withContext Result.failure(Exception("فشل في إنشاء العلامة المائية"))
            
            // إضافة العلامة المائية
            onProgress(85)
            val finalVideoPath = addWatermark(
                optimizedVideoPath, 
                watermarkPath, 
                workDir,
                WatermarkStyle.BOTTOM_RIGHT // يمكن تخصيصه لاحقاً
            ) ?: return@withContext Result.failure(Exception("فشل في إضافة العلامة المائية"))
            
            // نسخ الفيديو النهائي إلى مجلد التطبيق
            onProgress(95)
            val outputPath = copyToAppDirectory(finalVideoPath, project.id)
                ?: return@withContext Result.failure(Exception("فشل في حفظ الفيديو النهائي"))
            
            // تنظيف الملفات المؤقتة
            cleanupWorkDirectory(workDir)
            
            onProgress(100)
            Log.d(TAG, "Video processing completed successfully: $outputPath")
            Result.success(outputPath)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing video", e)
            Result.failure(e)
        }
    }

    /**
     * تحميل الفيديو من الرابط
     */
    private suspend fun downloadVideo(url: String, workDir: File): String? {
        return try {
            val outputFile = File(workDir, "downloaded_video.mp4")

            Log.d(TAG, "Downloading video from: $url")

            when {
                // ملف محلي
                url.startsWith("file://") || File(url).exists() -> {
                    val sourceFile = File(url.removePrefix("file://"))
                    sourceFile.copyTo(outputFile, overwrite = true)
                    outputFile.absolutePath
                }

                // رابط HTTP/HTTPS (Firebase Storage أو أي مصدر آخر)
                url.startsWith("http://") || url.startsWith("https://") -> {
                    downloadFromUrl(url, outputFile)
                }

                else -> {
                    Log.e(TAG, "Unsupported URL format: $url")
                    null
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading video", e)
            null
        }
    }

    /**
     * تحميل من رابط HTTP/HTTPS
     */
    private suspend fun downloadFromUrl(url: String, outputFile: File): String? {
        return try {
            // استخدام OkHttp للتحميل
            val request = okhttp3.Request.Builder()
                .url(url)
                .build()

            val client = okhttp3.OkHttpClient()
            val response = client.newCall(request).execute()

            if (!response.isSuccessful) {
                Log.e(TAG, "Failed to download: ${response.code}")
                return null
            }

            response.body?.let { responseBody ->
                val inputStream = responseBody.byteStream()
                val outputStream = outputFile.outputStream()

                inputStream.copyTo(outputStream)

                outputStream.close()
                inputStream.close()

                outputFile.absolutePath
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error downloading from URL", e)
            null
        }
    }

    /**
     * تحليل معلومات الفيديو
     */
    private suspend fun analyzeVideo(videoPath: String): VideoInfo? {
        return try {
            var duration = 0
            var width = 0
            var height = 0
            
            val session = FFmpegKit.execute("-i \"$videoPath\" -hide_banner")
            val output = session.allLogsAsString
            
            // استخراج المدة
            val durationRegex = "Duration: (\\d{2}):(\\d{2}):(\\d{2})".toRegex()
            val durationMatch = durationRegex.find(output)
            if (durationMatch != null) {
                val hours = durationMatch.groupValues[1].toInt()
                val minutes = durationMatch.groupValues[2].toInt()
                val seconds = durationMatch.groupValues[3].toInt()
                duration = hours * 3600 + minutes * 60 + seconds
            }
            
            // استخراج الأبعاد
            val resolutionRegex = "(\\d+)x(\\d+)".toRegex()
            val resolutionMatch = resolutionRegex.find(output)
            if (resolutionMatch != null) {
                width = resolutionMatch.groupValues[1].toInt()
                height = resolutionMatch.groupValues[2].toInt()
            }
            
            VideoInfo(duration, width, height)
        } catch (e: Exception) {
            Log.e(TAG, "Error analyzing video", e)
            null
        }
    }

    /**
     * قص الفيديو
     */
    private suspend fun trimVideo(inputPath: String, workDir: File, maxDuration: Int): String {
        val outputFile = File(workDir, "trimmed_video.mp4")
        
        val command = "-i \"$inputPath\" -t $maxDuration -c copy \"${outputFile.absolutePath}\""
        
        val session = FFmpegKit.execute(command)
        
        return if (ReturnCode.isSuccess(session.returnCode)) {
            outputFile.absolutePath
        } else {
            Log.e(TAG, "Error trimming video: ${session.failStackTrace}")
            inputPath // إرجاع الملف الأصلي في حالة الفشل
        }
    }

    /**
     * تحسين جودة الفيديو
     */
    private suspend fun optimizeVideo(inputPath: String, workDir: File): String? {
        val outputFile = File(workDir, "optimized_video.mp4")
        
        val command = buildString {
            append("-i \"$inputPath\" ")
            append("-vf \"scale=$TARGET_RESOLUTION:force_original_aspect_ratio=decrease,pad=$TARGET_RESOLUTION:(ow-iw)/2:(oh-ih)/2\" ")
            append("-c:v libx264 ")
            append("-b:v $TARGET_BITRATE ")
            append("-r $TARGET_FPS ")
            append("-c:a aac ")
            append("-b:a 128k ")
            append("-movflags +faststart ")
            append("\"${outputFile.absolutePath}\"")
        }
        
        val session = FFmpegKit.execute(command)
        
        return if (ReturnCode.isSuccess(session.returnCode)) {
            outputFile.absolutePath
        } else {
            Log.e(TAG, "Error optimizing video: ${session.failStackTrace}")
            null
        }
    }

    /**
     * إنشاء العلامة المائية
     */
    private suspend fun createWatermark(text: String, workDir: File): String? {
        return try {
            val watermarkFile = File(workDir, "watermark.png")
            
            // إنشاء bitmap للعلامة المائية
            val bitmap = Bitmap.createBitmap(400, 100, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            
            // إعداد النص
            val paint = Paint().apply {
                color = Color.WHITE
                textSize = 32f
                typeface = Typeface.DEFAULT_BOLD
                isAntiAlias = true
                setShadowLayer(4f, 2f, 2f, Color.BLACK)
            }
            
            // رسم خلفية شبه شفافة
            val backgroundPaint = Paint().apply {
                color = Color.argb(128, 0, 0, 0)
            }
            canvas.drawRoundRect(0f, 0f, 400f, 100f, 10f, 10f, backgroundPaint)
            
            // رسم النص
            val textBounds = android.graphics.Rect()
            paint.getTextBounds(text, 0, text.length, textBounds)
            val x = (400 - textBounds.width()) / 2f
            val y = (100 + textBounds.height()) / 2f
            canvas.drawText(text, x, y, paint)
            
            // حفظ الصورة
            val outputStream = FileOutputStream(watermarkFile)
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
            outputStream.close()
            bitmap.recycle()
            
            watermarkFile.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "Error creating watermark", e)
            null
        }
    }

    /**
     * إضافة العلامة المائية للفيديو
     */
    private suspend fun addWatermark(
        videoPath: String, 
        watermarkPath: String, 
        workDir: File,
        style: WatermarkStyle
    ): String? {
        val outputFile = File(workDir, "final_video.mp4")
        
        val position = when (style) {
            WatermarkStyle.BOTTOM_RIGHT -> "main_w-overlay_w-10:main_h-overlay_h-10"
            WatermarkStyle.BOTTOM_LEFT -> "10:main_h-overlay_h-10"
            WatermarkStyle.TOP_RIGHT -> "main_w-overlay_w-10:10"
            WatermarkStyle.TOP_LEFT -> "10:10"
            WatermarkStyle.CENTER -> "(main_w-overlay_w)/2:(main_h-overlay_h)/2"
        }
        
        val command = "-i \"$videoPath\" -i \"$watermarkPath\" " +
                "-filter_complex \"[0:v][1:v] overlay=$position\" " +
                "-codec:a copy \"${outputFile.absolutePath}\""
        
        val session = FFmpegKit.execute(command)
        
        return if (ReturnCode.isSuccess(session.returnCode)) {
            outputFile.absolutePath
        } else {
            Log.e(TAG, "Error adding watermark: ${session.failStackTrace}")
            null
        }
    }

    /**
     * إنشاء مجلد العمل
     */
    private fun createWorkDirectory(projectId: String): File {
        val workDir = File(context.cacheDir, "video_processing/$projectId")
        if (!workDir.exists()) {
            workDir.mkdirs()
        }
        return workDir
    }

    /**
     * نسخ الفيديو النهائي إلى مجلد التطبيق
     */
    private fun copyToAppDirectory(videoPath: String, projectId: String): String? {
        return try {
            val videosDir = File(context.filesDir, "videos")
            if (!videosDir.exists()) {
                videosDir.mkdirs()
            }
            
            val outputFile = File(videosDir, "${projectId}_final.mp4")
            File(videoPath).copyTo(outputFile, overwrite = true)
            
            outputFile.absolutePath
        } catch (e: Exception) {
            Log.e(TAG, "Error copying to app directory", e)
            null
        }
    }

    /**
     * تنظيف مجلد العمل
     */
    private fun cleanupWorkDirectory(workDir: File) {
        try {
            workDir.deleteRecursively()
        } catch (e: Exception) {
            Log.w(TAG, "Error cleaning up work directory", e)
        }
    }
}

/**
 * معلومات الفيديو
 */
data class VideoInfo(
    val duration: Int, // بالثواني
    val width: Int,
    val height: Int
)
