package com.example.aiagent.ui.screens;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000:\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0002\b\u0006\u001a&\u0010\u0000\u001a\u00020\u00012\b\u0010\u0002\u001a\u0004\u0018\u00010\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a2\u0010\u0006\u001a\u00020\u00012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u00010\b2\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0003\u001a$\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u00032\u0012\u0010\r\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\u001a\u0010\u000e\u001a\u00020\u00012\u0006\u0010\u000f\u001a\u00020\u00102\b\b\u0002\u0010\u0011\u001a\u00020\u0012H\u0007\u001a@\u0010\u0013\u001a\u00020\u00012\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00172\u0012\u0010\u0018\u001a\u000e\u0012\u0004\u0012\u00020\u0015\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0019\u001a\u000e\u0012\u0004\u0012\u00020\u0017\u0012\u0004\u0012\u00020\u00010\u0005H\u0003\u001a\u001e\u0010\u001a\u001a\u00020\u00012\u0006\u0010\u001b\u001a\u00020\u00152\f\u0010\u001c\u001a\b\u0012\u0004\u0012\u00020\u00010\bH\u0003\u00a8\u0006\u001d"}, d2 = {"AISettingsCard", "", "geminiApiKey", "", "onGeminiApiKeyChange", "Lkotlin/Function1;", "AppSettingsCard", "onResetSettings", "Lkotlin/Function0;", "onExportData", "onImportData", "ChannelSettingsCard", "channelName", "onChannelNameChange", "SettingsScreen", "navController", "Landroidx/navigation/NavController;", "viewModel", "Lcom/example/aiagent/ui/viewmodel/SettingsViewModel;", "UploadSettingsCard", "autoUpload", "", "uploadInterval", "", "onAutoUploadChange", "onUploadIntervalChange", "YouTubeSettingsCard", "isConfigured", "onSetupYouTube", "app_debug"})
public final class SettingsScreenKt {
    
    /**
     * شاشة الإعدادات
     */
    @kotlin.OptIn(markerClass = {androidx.compose.material3.ExperimentalMaterial3Api.class})
    @androidx.compose.runtime.Composable()
    public static final void SettingsScreen(@org.jetbrains.annotations.NotNull()
    androidx.navigation.NavController navController, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.ui.viewmodel.SettingsViewModel viewModel) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void ChannelSettingsCard(java.lang.String channelName, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onChannelNameChange) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AISettingsCard(java.lang.String geminiApiKey, kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> onGeminiApiKeyChange) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void YouTubeSettingsCard(boolean isConfigured, kotlin.jvm.functions.Function0<kotlin.Unit> onSetupYouTube) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void UploadSettingsCard(boolean autoUpload, int uploadInterval, kotlin.jvm.functions.Function1<? super java.lang.Boolean, kotlin.Unit> onAutoUploadChange, kotlin.jvm.functions.Function1<? super java.lang.Integer, kotlin.Unit> onUploadIntervalChange) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void AppSettingsCard(kotlin.jvm.functions.Function0<kotlin.Unit> onResetSettings, kotlin.jvm.functions.Function0<kotlin.Unit> onExportData, kotlin.jvm.functions.Function0<kotlin.Unit> onImportData) {
    }
}