package com.example.aiagent.receiver;

/**
 * مستقبل المنبهات المجدولة
 * يتعامل مع تنفيذ المهام في الأوقات المحددة
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0007\u0018\u0000 \r2\u00020\u0001:\u0001\rB\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\bH\u0016J\u0010\u0010\t\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u0006H\u0002J\u0018\u0010\n\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\fH\u0002\u00a8\u0006\u000e"}, d2 = {"Lcom/example/aiagent/receiver/AlarmReceiver;", "Landroid/content/BroadcastReceiver;", "()V", "onReceive", "", "context", "Landroid/content/Context;", "intent", "Landroid/content/Intent;", "triggerScheduledCheck", "triggerScheduledUpload", "projectId", "", "Companion", "app_debug"})
public final class AlarmReceiver extends android.content.BroadcastReceiver {
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String ACTION_SCHEDULED_UPLOAD = "com.example.aiagent.SCHEDULED_UPLOAD";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String EXTRA_PROJECT_ID = "project_id";
    @org.jetbrains.annotations.NotNull()
    public static final com.example.aiagent.receiver.AlarmReceiver.Companion Companion = null;
    
    public AlarmReceiver() {
        super();
    }
    
    @java.lang.Override()
    public void onReceive(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.content.Intent intent) {
    }
    
    /**
     * تشغيل رفع مجدول لمشروع محدد
     */
    private final void triggerScheduledUpload(android.content.Context context, java.lang.String projectId) {
    }
    
    /**
     * تشغيل فحص عام للمشاريع المجدولة
     */
    private final void triggerScheduledCheck(android.content.Context context) {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0006"}, d2 = {"Lcom/example/aiagent/receiver/AlarmReceiver$Companion;", "", "()V", "ACTION_SCHEDULED_UPLOAD", "", "EXTRA_PROJECT_ID", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}