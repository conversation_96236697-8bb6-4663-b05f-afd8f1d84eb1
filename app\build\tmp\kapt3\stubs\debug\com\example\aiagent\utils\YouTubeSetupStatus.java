package com.example.aiagent.utils;

/**
 * حالات إعداد YouTube
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\b\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\b\u00a8\u0006\t"}, d2 = {"Lcom/example/aiagent/utils/YouTubeSetupStatus;", "", "(<PERSON><PERSON><PERSON>/lang/String;I)V", "NOT_CONFIGURED", "FILE_MISSING", "AUTH_FAILED", "CONNECTION_FAILED", "CONFIGURED_AND_WORKING", "ERROR", "app_debug"})
public enum YouTubeSetupStatus {
    /*public static final*/ NOT_CONFIGURED /* = new NOT_CONFIGURED() */,
    /*public static final*/ FILE_MISSING /* = new FILE_MISSING() */,
    /*public static final*/ AUTH_FAILED /* = new AUTH_FAILED() */,
    /*public static final*/ CONNECTION_FAILED /* = new CONNECTION_FAILED() */,
    /*public static final*/ CONFIGURED_AND_WORKING /* = new CONFIGURED_AND_WORKING() */,
    /*public static final*/ ERROR /* = new ERROR() */;
    
    YouTubeSetupStatus() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.aiagent.utils.YouTubeSetupStatus> getEntries() {
        return null;
    }
}