package com.example.aiagent.data.repository;

/**
 * مستودع إعدادات المستخدم
 */
@javax.inject.Singleton()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000R\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0000\n\u0002\u0018\u0002\n\u0002\u0010\u0002\n\u0002\b\u000e\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u000f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\b\u0010\u0005\u001a\u00020\u0006H\u0002J\u000e\u0010\u0007\u001a\n\u0012\u0006\u0012\u0004\u0018\u00010\u00060\bJ\u0010\u0010\t\u001a\u0004\u0018\u00010\u0006H\u0086@\u00a2\u0006\u0002\u0010\nJ\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00020\u00060\bJ\u000e\u0010\f\u001a\u00020\rH\u0086@\u00a2\u0006\u0002\u0010\nJ\u001c\u0010\u000e\u001a\b\u0012\u0004\u0012\u00020\u00100\u000fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0011\u0010\nJ$\u0010\u0012\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010\u0013\u001a\u00020\u0006H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0014\u0010\u0015J$\u0010\u0016\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010\u0017\u001a\u00020\rH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u0018\u0010\u0019J$\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010\u001b\u001a\u00020\rH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\u001c\u0010\u0019J$\u0010\u001d\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010\u001e\u001a\u00020\u001fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b \u0010!J&\u0010\"\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\b\u0010#\u001a\u0004\u0018\u00010\u001fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b$\u0010!J$\u0010%\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010&\u001a\u00020'H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b(\u0010)J$\u0010*\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u0010+\u001a\u00020,H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b-\u0010.J$\u0010/\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\u0006\u00100\u001a\u000201H\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b2\u00103J&\u00104\u001a\b\u0012\u0004\u0012\u00020\u00100\u000f2\b\u00105\u001a\u0004\u0018\u00010\u001fH\u0086@\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b6\u0010!R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000b\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u00067"}, d2 = {"Lcom/example/aiagent/data/repository/UserSettingsRepository;", "", "userSettingsDao", "Lcom/example/aiagent/data/database/UserSettingsDao;", "(Lcom/example/aiagent/data/database/UserSettingsDao;)V", "getDefaultSettings", "Lcom/example/aiagent/data/model/UserSettings;", "getSettings", "Lkotlinx/coroutines/flow/Flow;", "getSettingsOnce", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getSettingsWithDefaults", "isInitialSetupComplete", "", "resetToDefaults", "Lkotlin/Result;", "", "resetToDefaults-IoAF18A", "saveSettings", "settings", "saveSettings-gIAlu-s", "(Lcom/example/aiagent/data/model/UserSettings;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateAutoUpload", "autoUpload", "updateAutoUpload-gIAlu-s", "(ZLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateBatteryOptimizationRequested", "requested", "updateBatteryOptimizationRequested-gIAlu-s", "updateChannelName", "channelName", "", "updateChannelName-gIAlu-s", "(Ljava/lang/String;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateGeminiApiKey", "apiKey", "updateGeminiApiKey-gIAlu-s", "updateUploadSchedule", "schedule", "Lcom/example/aiagent/data/model/UploadSchedule;", "updateUploadSchedule-gIAlu-s", "(Lcom/example/aiagent/data/model/UploadSchedule;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateVideoQuality", "quality", "Lcom/example/aiagent/data/model/VideoQuality;", "updateVideoQuality-gIAlu-s", "(Lcom/example/aiagent/data/model/VideoQuality;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateWatermarkStyle", "style", "Lcom/example/aiagent/data/model/WatermarkStyle;", "updateWatermarkStyle-gIAlu-s", "(Lcom/example/aiagent/data/model/WatermarkStyle;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateYouTubeServiceAccountPath", "path", "updateYouTubeServiceAccountPath-gIAlu-s", "app_debug"})
public final class UserSettingsRepository {
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.data.database.UserSettingsDao userSettingsDao = null;
    
    @javax.inject.Inject()
    public UserSettingsRepository(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.database.UserSettingsDao userSettingsDao) {
        super();
    }
    
    /**
     * الحصول على الإعدادات كـ Flow
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.example.aiagent.data.model.UserSettings> getSettings() {
        return null;
    }
    
    /**
     * الحصول على الإعدادات مرة واحدة
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object getSettingsOnce(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super com.example.aiagent.data.model.UserSettings> $completion) {
        return null;
    }
    
    /**
     * الحصول على الإعدادات مع القيم الافتراضية
     */
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.Flow<com.example.aiagent.data.model.UserSettings> getSettingsWithDefaults() {
        return null;
    }
    
    /**
     * التحقق من اكتمال الإعداد الأولي
     */
    @org.jetbrains.annotations.Nullable()
    public final java.lang.Object isInitialSetupComplete(@org.jetbrains.annotations.NotNull()
    kotlin.coroutines.Continuation<? super java.lang.Boolean> $completion) {
        return null;
    }
    
    /**
     * الحصول على الإعدادات الافتراضية
     */
    private final com.example.aiagent.data.model.UserSettings getDefaultSettings() {
        return null;
    }
}