package com.example.aiagent.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.example.aiagent.ui.components.SettingsSection
import com.example.aiagent.ui.theme.*
import com.example.aiagent.ui.viewmodel.SettingsViewModel

/**
 * شاشة الإعدادات
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    viewModel: SettingsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Header
        item {
            Text(
                text = "الإعدادات",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 8.dp)
            )
        }
        
        // Channel Settings
        item {
            SettingsSection(
                title = "إعدادات القناة",
                icon = Icons.Default.VideoLibrary
            ) {
                ChannelSettingsCard(
                    channelName = uiState.channelName,
                    onChannelNameChange = viewModel::updateChannelName
                )
            }
        }
        
        // AI Settings
        item {
            SettingsSection(
                title = "إعدادات الذكاء الاصطناعي",
                icon = Icons.Default.Psychology
            ) {
                AISettingsCard(
                    geminiApiKey = uiState.geminiApiKey,
                    onGeminiApiKeyChange = viewModel::updateGeminiApiKey
                )
            }
        }
        
        // YouTube Settings
        item {
            SettingsSection(
                title = "إعدادات YouTube",
                icon = Icons.Default.YouTube
            ) {
                YouTubeSettingsCard(
                    isConfigured = uiState.isYouTubeConfigured,
                    onSetupYouTube = viewModel::setupYouTube
                )
            }
        }
        
        // Upload Settings
        item {
            SettingsSection(
                title = "إعدادات الرفع",
                icon = Icons.Default.Upload
            ) {
                UploadSettingsCard(
                    autoUpload = uiState.autoUpload,
                    uploadInterval = uiState.uploadInterval,
                    onAutoUploadChange = viewModel::updateAutoUpload,
                    onUploadIntervalChange = viewModel::updateUploadInterval
                )
            }
        }
        
        // App Settings
        item {
            SettingsSection(
                title = "إعدادات التطبيق",
                icon = Icons.Default.Settings
            ) {
                AppSettingsCard(
                    onResetSettings = viewModel::resetSettings,
                    onExportData = viewModel::exportData,
                    onImportData = viewModel::importData
                )
            }
        }
    }
}

@Composable
private fun ChannelSettingsCard(
    channelName: String,
    onChannelNameChange: (String) -> Unit
) {
    var editingChannelName by remember { mutableStateOf(channelName) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            OutlinedTextField(
                value = editingChannelName,
                onValueChange = { 
                    editingChannelName = it
                    onChannelNameChange(it)
                },
                label = { Text("اسم القناة") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true
            )
        }
    }
}

@Composable
private fun AISettingsCard(
    geminiApiKey: String?,
    onGeminiApiKeyChange: (String) -> Unit
) {
    var editingApiKey by remember { mutableStateOf(geminiApiKey ?: "") }
    var showApiKey by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            OutlinedTextField(
                value = editingApiKey,
                onValueChange = { 
                    editingApiKey = it
                    onGeminiApiKeyChange(it)
                },
                label = { Text("Gemini API Key") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                visualTransformation = if (showApiKey) {
                    androidx.compose.ui.text.input.VisualTransformation.None
                } else {
                    androidx.compose.ui.text.input.PasswordVisualTransformation()
                },
                trailingIcon = {
                    IconButton(onClick = { showApiKey = !showApiKey }) {
                        Icon(
                            imageVector = if (showApiKey) Icons.Default.VisibilityOff else Icons.Default.Visibility,
                            contentDescription = if (showApiKey) "إخفاء" else "إظهار"
                        )
                    }
                }
            )
            
            if (geminiApiKey.isNullOrEmpty()) {
                Text(
                    text = "يرجى إضافة مفتاح Gemini API لتفعيل الذكاء الاصطناعي",
                    style = MaterialTheme.typography.bodySmall,
                    color = WarningOrange
                )
            } else {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = SuccessGreen,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "تم تكوين Gemini AI",
                        style = MaterialTheme.typography.bodySmall,
                        color = SuccessGreen
                    )
                }
            }
        }
    }
}

@Composable
private fun YouTubeSettingsCard(
    isConfigured: Boolean,
    onSetupYouTube: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "ربط YouTube",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = if (isConfigured) "مُعد ومتصل" else "غير مُعد",
                        style = MaterialTheme.typography.bodySmall,
                        color = if (isConfigured) SuccessGreen else WarningOrange
                    )
                }
                
                if (isConfigured) {
                    Icon(
                        imageVector = Icons.Default.CheckCircle,
                        contentDescription = null,
                        tint = SuccessGreen
                    )
                } else {
                    Button(
                        onClick = onSetupYouTube,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = YouTubeRed
                        )
                    ) {
                        Text("إعداد")
                    }
                }
            }
            
            if (!isConfigured) {
                Text(
                    text = "يرجى إعداد Service Account لرفع الفيديوهات على YouTube",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun UploadSettingsCard(
    autoUpload: Boolean,
    uploadInterval: Int,
    onAutoUploadChange: (Boolean) -> Unit,
    onUploadIntervalChange: (Int) -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Auto Upload Toggle
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "الرفع التلقائي",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = "رفع الفيديوهات تلقائياً عند الانتهاء من المعالجة",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
                
                Switch(
                    checked = autoUpload,
                    onCheckedChange = onAutoUploadChange
                )
            }
            
            // Upload Interval
            Column {
                Text(
                    text = "فترة الرفع (ساعات)",
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Medium
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Slider(
                    value = uploadInterval.toFloat(),
                    onValueChange = { onUploadIntervalChange(it.toInt()) },
                    valueRange = 1f..48f,
                    steps = 47,
                    modifier = Modifier.fillMaxWidth()
                )
                
                Text(
                    text = "كل $uploadInterval ساعة",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

@Composable
private fun AppSettingsCard(
    onResetSettings: () -> Unit,
    onExportData: () -> Unit,
    onImportData: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Export Data
            OutlinedButton(
                onClick = onExportData,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.FileDownload,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("تصدير البيانات")
            }
            
            // Import Data
            OutlinedButton(
                onClick = onImportData,
                modifier = Modifier.fillMaxWidth()
            ) {
                Icon(
                    imageVector = Icons.Default.FileUpload,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("استيراد البيانات")
            }
            
            // Reset Settings
            OutlinedButton(
                onClick = onResetSettings,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.outlinedButtonColors(
                    contentColor = ErrorRed
                )
            ) {
                Icon(
                    imageVector = Icons.Default.RestartAlt,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text("إعادة تعيين الإعدادات")
            }
        }
    }
}
