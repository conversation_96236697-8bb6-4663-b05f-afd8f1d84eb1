package com.example.aiagent.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.example.aiagent.data.model.UploadSchedule;
import com.example.aiagent.data.model.UserSettings;
import com.example.aiagent.data.model.VideoQuality;
import com.example.aiagent.data.model.WatermarkStyle;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;
import kotlinx.coroutines.flow.Flow;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserSettingsDao_Impl implements UserSettingsDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserSettings> __insertionAdapterOfUserSettings;

  private final Converters __converters = new Converters();

  private final EntityDeletionOrUpdateAdapter<UserSettings> __updateAdapterOfUserSettings;

  private final SharedSQLiteStatement __preparedStmtOfUpdateChannelName;

  private final SharedSQLiteStatement __preparedStmtOfUpdateAutoUpload;

  private final SharedSQLiteStatement __preparedStmtOfUpdateBatteryOptimizationRequested;

  private final SharedSQLiteStatement __preparedStmtOfUpdateGeminiApiKey;

  private final SharedSQLiteStatement __preparedStmtOfUpdateYouTubeServiceAccountPath;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllSettings;

  public UserSettingsDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserSettings = new EntityInsertionAdapter<UserSettings>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_settings` (`id`,`channelName`,`watermarkStyle`,`uploadSchedule`,`videoQuality`,`autoUpload`,`batteryOptimizationRequested`,`geminiApiKey`,`youtubeServiceAccountPath`) VALUES (?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserSettings entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getChannelName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getChannelName());
        }
        final String _tmp = __converters.fromWatermarkStyle(entity.getWatermarkStyle());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        final String _tmp_1 = __converters.fromUploadSchedule(entity.getUploadSchedule());
        if (_tmp_1 == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp_1);
        }
        final String _tmp_2 = __converters.fromVideoQuality(entity.getVideoQuality());
        if (_tmp_2 == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp_2);
        }
        final int _tmp_3 = entity.getAutoUpload() ? 1 : 0;
        statement.bindLong(6, _tmp_3);
        final int _tmp_4 = entity.getBatteryOptimizationRequested() ? 1 : 0;
        statement.bindLong(7, _tmp_4);
        if (entity.getGeminiApiKey() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getGeminiApiKey());
        }
        if (entity.getYoutubeServiceAccountPath() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getYoutubeServiceAccountPath());
        }
      }
    };
    this.__updateAdapterOfUserSettings = new EntityDeletionOrUpdateAdapter<UserSettings>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `user_settings` SET `id` = ?,`channelName` = ?,`watermarkStyle` = ?,`uploadSchedule` = ?,`videoQuality` = ?,`autoUpload` = ?,`batteryOptimizationRequested` = ?,`geminiApiKey` = ?,`youtubeServiceAccountPath` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserSettings entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getChannelName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getChannelName());
        }
        final String _tmp = __converters.fromWatermarkStyle(entity.getWatermarkStyle());
        if (_tmp == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, _tmp);
        }
        final String _tmp_1 = __converters.fromUploadSchedule(entity.getUploadSchedule());
        if (_tmp_1 == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, _tmp_1);
        }
        final String _tmp_2 = __converters.fromVideoQuality(entity.getVideoQuality());
        if (_tmp_2 == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, _tmp_2);
        }
        final int _tmp_3 = entity.getAutoUpload() ? 1 : 0;
        statement.bindLong(6, _tmp_3);
        final int _tmp_4 = entity.getBatteryOptimizationRequested() ? 1 : 0;
        statement.bindLong(7, _tmp_4);
        if (entity.getGeminiApiKey() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getGeminiApiKey());
        }
        if (entity.getYoutubeServiceAccountPath() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getYoutubeServiceAccountPath());
        }
        statement.bindLong(10, entity.getId());
      }
    };
    this.__preparedStmtOfUpdateChannelName = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_settings SET channelName = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateAutoUpload = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_settings SET autoUpload = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateBatteryOptimizationRequested = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_settings SET batteryOptimizationRequested = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateGeminiApiKey = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_settings SET geminiApiKey = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfUpdateYouTubeServiceAccountPath = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "UPDATE user_settings SET youtubeServiceAccountPath = ? WHERE id = 1";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllSettings = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM user_settings";
        return _query;
      }
    };
  }

  @Override
  public Object insertSettings(final UserSettings settings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfUserSettings.insert(settings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateSettings(final UserSettings settings,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserSettings.handle(settings);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateChannelName(final String channelName,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateChannelName.acquire();
        int _argIndex = 1;
        if (channelName == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, channelName);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateChannelName.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateAutoUpload(final boolean autoUpload,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateAutoUpload.acquire();
        int _argIndex = 1;
        final int _tmp = autoUpload ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateAutoUpload.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateBatteryOptimizationRequested(final boolean requested,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateBatteryOptimizationRequested.acquire();
        int _argIndex = 1;
        final int _tmp = requested ? 1 : 0;
        _stmt.bindLong(_argIndex, _tmp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateBatteryOptimizationRequested.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateGeminiApiKey(final String apiKey,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateGeminiApiKey.acquire();
        int _argIndex = 1;
        if (apiKey == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, apiKey);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateGeminiApiKey.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object updateYouTubeServiceAccountPath(final String path,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfUpdateYouTubeServiceAccountPath.acquire();
        int _argIndex = 1;
        if (path == null) {
          _stmt.bindNull(_argIndex);
        } else {
          _stmt.bindString(_argIndex, path);
        }
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfUpdateYouTubeServiceAccountPath.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteAllSettings(final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllSettings.acquire();
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteAllSettings.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Flow<UserSettings> getSettings() {
    final String _sql = "SELECT * FROM user_settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    return CoroutinesRoom.createFlow(__db, false, new String[] {"user_settings"}, new Callable<UserSettings>() {
      @Override
      @Nullable
      public UserSettings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfChannelName = CursorUtil.getColumnIndexOrThrow(_cursor, "channelName");
          final int _cursorIndexOfWatermarkStyle = CursorUtil.getColumnIndexOrThrow(_cursor, "watermarkStyle");
          final int _cursorIndexOfUploadSchedule = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadSchedule");
          final int _cursorIndexOfVideoQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "videoQuality");
          final int _cursorIndexOfAutoUpload = CursorUtil.getColumnIndexOrThrow(_cursor, "autoUpload");
          final int _cursorIndexOfBatteryOptimizationRequested = CursorUtil.getColumnIndexOrThrow(_cursor, "batteryOptimizationRequested");
          final int _cursorIndexOfGeminiApiKey = CursorUtil.getColumnIndexOrThrow(_cursor, "geminiApiKey");
          final int _cursorIndexOfYoutubeServiceAccountPath = CursorUtil.getColumnIndexOrThrow(_cursor, "youtubeServiceAccountPath");
          final UserSettings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpChannelName;
            if (_cursor.isNull(_cursorIndexOfChannelName)) {
              _tmpChannelName = null;
            } else {
              _tmpChannelName = _cursor.getString(_cursorIndexOfChannelName);
            }
            final WatermarkStyle _tmpWatermarkStyle;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfWatermarkStyle)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfWatermarkStyle);
            }
            _tmpWatermarkStyle = __converters.toWatermarkStyle(_tmp);
            final UploadSchedule _tmpUploadSchedule;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUploadSchedule)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfUploadSchedule);
            }
            _tmpUploadSchedule = __converters.toUploadSchedule(_tmp_1);
            final VideoQuality _tmpVideoQuality;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfVideoQuality)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfVideoQuality);
            }
            _tmpVideoQuality = __converters.toVideoQuality(_tmp_2);
            final boolean _tmpAutoUpload;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfAutoUpload);
            _tmpAutoUpload = _tmp_3 != 0;
            final boolean _tmpBatteryOptimizationRequested;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfBatteryOptimizationRequested);
            _tmpBatteryOptimizationRequested = _tmp_4 != 0;
            final String _tmpGeminiApiKey;
            if (_cursor.isNull(_cursorIndexOfGeminiApiKey)) {
              _tmpGeminiApiKey = null;
            } else {
              _tmpGeminiApiKey = _cursor.getString(_cursorIndexOfGeminiApiKey);
            }
            final String _tmpYoutubeServiceAccountPath;
            if (_cursor.isNull(_cursorIndexOfYoutubeServiceAccountPath)) {
              _tmpYoutubeServiceAccountPath = null;
            } else {
              _tmpYoutubeServiceAccountPath = _cursor.getString(_cursorIndexOfYoutubeServiceAccountPath);
            }
            _result = new UserSettings(_tmpId,_tmpChannelName,_tmpWatermarkStyle,_tmpUploadSchedule,_tmpVideoQuality,_tmpAutoUpload,_tmpBatteryOptimizationRequested,_tmpGeminiApiKey,_tmpYoutubeServiceAccountPath);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public Object getSettingsOnce(final Continuation<? super UserSettings> $completion) {
    final String _sql = "SELECT * FROM user_settings WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserSettings>() {
      @Override
      @Nullable
      public UserSettings call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfChannelName = CursorUtil.getColumnIndexOrThrow(_cursor, "channelName");
          final int _cursorIndexOfWatermarkStyle = CursorUtil.getColumnIndexOrThrow(_cursor, "watermarkStyle");
          final int _cursorIndexOfUploadSchedule = CursorUtil.getColumnIndexOrThrow(_cursor, "uploadSchedule");
          final int _cursorIndexOfVideoQuality = CursorUtil.getColumnIndexOrThrow(_cursor, "videoQuality");
          final int _cursorIndexOfAutoUpload = CursorUtil.getColumnIndexOrThrow(_cursor, "autoUpload");
          final int _cursorIndexOfBatteryOptimizationRequested = CursorUtil.getColumnIndexOrThrow(_cursor, "batteryOptimizationRequested");
          final int _cursorIndexOfGeminiApiKey = CursorUtil.getColumnIndexOrThrow(_cursor, "geminiApiKey");
          final int _cursorIndexOfYoutubeServiceAccountPath = CursorUtil.getColumnIndexOrThrow(_cursor, "youtubeServiceAccountPath");
          final UserSettings _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpChannelName;
            if (_cursor.isNull(_cursorIndexOfChannelName)) {
              _tmpChannelName = null;
            } else {
              _tmpChannelName = _cursor.getString(_cursorIndexOfChannelName);
            }
            final WatermarkStyle _tmpWatermarkStyle;
            final String _tmp;
            if (_cursor.isNull(_cursorIndexOfWatermarkStyle)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getString(_cursorIndexOfWatermarkStyle);
            }
            _tmpWatermarkStyle = __converters.toWatermarkStyle(_tmp);
            final UploadSchedule _tmpUploadSchedule;
            final String _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUploadSchedule)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getString(_cursorIndexOfUploadSchedule);
            }
            _tmpUploadSchedule = __converters.toUploadSchedule(_tmp_1);
            final VideoQuality _tmpVideoQuality;
            final String _tmp_2;
            if (_cursor.isNull(_cursorIndexOfVideoQuality)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getString(_cursorIndexOfVideoQuality);
            }
            _tmpVideoQuality = __converters.toVideoQuality(_tmp_2);
            final boolean _tmpAutoUpload;
            final int _tmp_3;
            _tmp_3 = _cursor.getInt(_cursorIndexOfAutoUpload);
            _tmpAutoUpload = _tmp_3 != 0;
            final boolean _tmpBatteryOptimizationRequested;
            final int _tmp_4;
            _tmp_4 = _cursor.getInt(_cursorIndexOfBatteryOptimizationRequested);
            _tmpBatteryOptimizationRequested = _tmp_4 != 0;
            final String _tmpGeminiApiKey;
            if (_cursor.isNull(_cursorIndexOfGeminiApiKey)) {
              _tmpGeminiApiKey = null;
            } else {
              _tmpGeminiApiKey = _cursor.getString(_cursorIndexOfGeminiApiKey);
            }
            final String _tmpYoutubeServiceAccountPath;
            if (_cursor.isNull(_cursorIndexOfYoutubeServiceAccountPath)) {
              _tmpYoutubeServiceAccountPath = null;
            } else {
              _tmpYoutubeServiceAccountPath = _cursor.getString(_cursorIndexOfYoutubeServiceAccountPath);
            }
            _result = new UserSettings(_tmpId,_tmpChannelName,_tmpWatermarkStyle,_tmpUploadSchedule,_tmpVideoQuality,_tmpAutoUpload,_tmpBatteryOptimizationRequested,_tmpGeminiApiKey,_tmpYoutubeServiceAccountPath);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
