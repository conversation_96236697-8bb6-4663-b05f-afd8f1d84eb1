package com.example.aiagent.service;

import com.example.aiagent.data.repository.VideoProjectRepository;
import com.example.aiagent.utils.NotificationHelper;
import com.example.aiagent.utils.VideoProcessor;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class VideoProcessingService_MembersInjector implements MembersInjector<VideoProcessingService> {
  private final Provider<VideoProjectRepository> videoProjectRepositoryProvider;

  private final Provider<VideoProcessor> videoProcessorProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  public VideoProcessingService_MembersInjector(
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<VideoProcessor> videoProcessorProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    this.videoProjectRepositoryProvider = videoProjectRepositoryProvider;
    this.videoProcessorProvider = videoProcessorProvider;
    this.notificationHelperProvider = notificationHelperProvider;
  }

  public static MembersInjector<VideoProcessingService> create(
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<VideoProcessor> videoProcessorProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    return new VideoProcessingService_MembersInjector(videoProjectRepositoryProvider, videoProcessorProvider, notificationHelperProvider);
  }

  @Override
  public void injectMembers(VideoProcessingService instance) {
    injectVideoProjectRepository(instance, videoProjectRepositoryProvider.get());
    injectVideoProcessor(instance, videoProcessorProvider.get());
    injectNotificationHelper(instance, notificationHelperProvider.get());
  }

  @InjectedFieldSignature("com.example.aiagent.service.VideoProcessingService.videoProjectRepository")
  public static void injectVideoProjectRepository(VideoProcessingService instance,
      VideoProjectRepository videoProjectRepository) {
    instance.videoProjectRepository = videoProjectRepository;
  }

  @InjectedFieldSignature("com.example.aiagent.service.VideoProcessingService.videoProcessor")
  public static void injectVideoProcessor(VideoProcessingService instance,
      VideoProcessor videoProcessor) {
    instance.videoProcessor = videoProcessor;
  }

  @InjectedFieldSignature("com.example.aiagent.service.VideoProcessingService.notificationHelper")
  public static void injectNotificationHelper(VideoProcessingService instance,
      NotificationHelper notificationHelper) {
    instance.notificationHelper = notificationHelper;
  }
}
