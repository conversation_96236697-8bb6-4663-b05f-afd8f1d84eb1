package com.example.aiagent.data.repository;

import com.example.aiagent.data.api.GeminiApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class GeminiRepository_Factory implements Factory<GeminiRepository> {
  private final Provider<GeminiApiService> geminiApiServiceProvider;

  public GeminiRepository_Factory(Provider<GeminiApiService> geminiApiServiceProvider) {
    this.geminiApiServiceProvider = geminiApiServiceProvider;
  }

  @Override
  public GeminiRepository get() {
    return newInstance(geminiApiServiceProvider.get());
  }

  public static GeminiRepository_Factory create(
      Provider<GeminiApiService> geminiApiServiceProvider) {
    return new GeminiRepository_Factory(geminiApiServiceProvider);
  }

  public static GeminiRepository newInstance(GeminiApiService geminiApiService) {
    return new GeminiRepository(geminiApiService);
  }
}
