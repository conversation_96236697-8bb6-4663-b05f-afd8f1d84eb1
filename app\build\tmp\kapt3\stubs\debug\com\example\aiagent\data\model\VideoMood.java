package com.example.aiagent.data.model;

/**
 * مزاج الفيديو
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\b\f\b\u0086\u0081\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002j\u0002\b\u0003j\u0002\b\u0004j\u0002\b\u0005j\u0002\b\u0006j\u0002\b\u0007j\u0002\b\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000bj\u0002\b\f\u00a8\u0006\r"}, d2 = {"Lcom/example/aiagent/data/model/VideoMood;", "", "(Ljava/lang/String;I)V", "HAPPY", "FUNNY", "EXCITING", "RELAXING", "INSPIRING", "EMOTIONAL", "ENERGETIC", "CALM", "MYSTERIOUS", "ROMANTIC", "app_debug"})
public enum VideoMood {
    /*public static final*/ HAPPY /* = new HAPPY() */,
    /*public static final*/ FUNNY /* = new FUNNY() */,
    /*public static final*/ EXCITING /* = new EXCITING() */,
    /*public static final*/ RELAXING /* = new RELAXING() */,
    /*public static final*/ INSPIRING /* = new INSPIRING() */,
    /*public static final*/ EMOTIONAL /* = new EMOTIONAL() */,
    /*public static final*/ ENERGETIC /* = new ENERGETIC() */,
    /*public static final*/ CALM /* = new CALM() */,
    /*public static final*/ MYSTERIOUS /* = new MYSTERIOUS() */,
    /*public static final*/ ROMANTIC /* = new ROMANTIC() */;
    
    VideoMood() {
    }
    
    @org.jetbrains.annotations.NotNull()
    public static kotlin.enums.EnumEntries<com.example.aiagent.data.model.VideoMood> getEntries() {
        return null;
    }
}