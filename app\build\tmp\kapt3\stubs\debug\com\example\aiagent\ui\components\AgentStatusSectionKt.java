package com.example.aiagent.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001c\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\u001a)\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\u0010\u0004\u001a\u0004\u0018\u00010\u00052\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007H\u0007\u00a2\u0006\u0002\u0010\b\u001a\u0010\u0010\t\u001a\u00020\u00012\u0006\u0010\n\u001a\u00020\u0007H\u0003\u001a\u0010\u0010\u000b\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u0005H\u0003\u001a\u0010\u0010\r\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u0003H\u0003\u00a8\u0006\u000e"}, d2 = {"AgentStatusSection", "", "isActive", "", "nextScheduledUpload", "", "currentTask", "", "(ZLjava/lang/Long;Ljava/lang/String;)V", "CurrentTaskCard", "task", "NextUploadCard", "timestamp", "StatusIndicator", "app_debug"})
public final class AgentStatusSectionKt {
    
    /**
     * قسم حالة الوكيل
     */
    @androidx.compose.runtime.Composable()
    public static final void AgentStatusSection(boolean isActive, @org.jetbrains.annotations.Nullable()
    java.lang.Long nextScheduledUpload, @org.jetbrains.annotations.Nullable()
    java.lang.String currentTask) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void StatusIndicator(boolean isActive) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void CurrentTaskCard(java.lang.String task) {
    }
    
    @androidx.compose.runtime.Composable()
    private static final void NextUploadCard(long timestamp) {
    }
}