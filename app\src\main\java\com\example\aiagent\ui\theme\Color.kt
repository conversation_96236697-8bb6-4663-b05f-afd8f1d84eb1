package com.example.aiagent.ui.theme

import androidx.compose.ui.graphics.Color

// Primary Colors - YouTube Red Theme
val YouTubeRed = Color(0xFFFF0000)
val YouTubeRedDark = Color(0xFFCC0000)
val YouTubeRedLight = Color(0xFFFF3333)

// Secondary Colors - Modern Blue
val ModernBlue = Color(0xFF2196F3)
val ModernBlueDark = Color(0xFF1976D2)
val ModernBlueLight = Color(0xFF64B5F6)

// Accent Colors
val AccentGreen = Color(0xFF4CAF50)
val AccentOrange = Color(0xFFFF9800)
val AccentPurple = Color(0xFF9C27B0)
val AccentBlue = Color(0xFF2196F3)

// Neutral Colors
val NeutralGray = Color(0xFF9E9E9E)
val LightGray = Color(0xFFF5F5F5)
val DarkGray = Color(0xFF424242)

// Background Colors
val BackgroundLight = Color(0xFFFAFAFA)
val BackgroundDark = Color(0xFF121212)
val SurfaceLight = Color(0xFFFFFFFF)
val SurfaceDark = Color(0xFF1E1E1E)

// Text Colors
val TextPrimary = Color(0xFF212121)
val TextSecondary = Color(0xFF757575)
val TextOnDark = Color(0xFFFFFFFF)
val TextOnLight = Color(0xFF000000)

// Status Colors
val SuccessGreen = Color(0xFF4CAF50)
val WarningOrange = Color(0xFFFF9800)
val ErrorRed = Color(0xFFF44336)
val InfoBlue = Color(0xFF2196F3)

// Gradient Colors
val GradientStart = Color(0xFFFF0000)
val GradientEnd = Color(0xFFFF9800)

// Card Colors
val CardBackground = Color(0xFFFFFFFF)
val CardBackgroundDark = Color(0xFF2C2C2C)
val CardElevation = Color(0x1F000000)

// Button Colors
val ButtonPrimary = YouTubeRed
val ButtonSecondary = ModernBlue
val ButtonDisabled = Color(0xFFBDBDBD)

// Progress Colors
val ProgressBackground = Color(0xFFE0E0E0)
val ProgressForeground = YouTubeRed
