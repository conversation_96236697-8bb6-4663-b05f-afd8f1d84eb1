package com.example.aiagent.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.aiagent.data.repository.UserSettingsRepository
import com.example.aiagent.utils.YouTubeSetupManager
import com.example.aiagent.utils.NotificationHelper
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * ViewModel لشاشة الإعدادات
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val userSettingsRepository: UserSettingsRepository,
    private val youTubeSetupManager: YouTubeSetupManager,
    private val notificationHelper: NotificationHelper
) : ViewModel() {

    private val _uiState = MutableStateFlow(SettingsUiState())
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    init {
        loadSettings()
        checkYouTubeSetup()
    }

    /**
     * تحميل الإعدادات
     */
    private fun loadSettings() {
        viewModelScope.launch {
            userSettingsRepository.getSettingsWithDefaults().collect { settings ->
                _uiState.value = _uiState.value.copy(
                    channelName = settings.channelName,
                    geminiApiKey = settings.geminiApiKey,
                    autoUpload = settings.autoUpload,
                    uploadInterval = settings.uploadSchedule.intervalHours
                )
            }
        }
    }

    /**
     * فحص إعداد YouTube
     */
    private fun checkYouTubeSetup() {
        viewModelScope.launch {
            val status = youTubeSetupManager.checkYouTubeSetupStatus()
            _uiState.value = _uiState.value.copy(
                isYouTubeConfigured = status == com.example.aiagent.utils.YouTubeSetupStatus.CONFIGURED_AND_WORKING
            )
        }
    }

    /**
     * تحديث اسم القناة
     */
    fun updateChannelName(channelName: String) {
        viewModelScope.launch {
            userSettingsRepository.updateChannelName(channelName)
        }
    }

    /**
     * تحديث مفتاح Gemini API
     */
    fun updateGeminiApiKey(apiKey: String) {
        viewModelScope.launch {
            val result = userSettingsRepository.updateGeminiApiKey(apiKey.ifEmpty { null })
            if (result.isSuccess) {
                notificationHelper.showSuccess(
                    "تم التحديث",
                    "تم تحديث مفتاح Gemini API بنجاح"
                )
            } else {
                notificationHelper.showError(
                    "خطأ",
                    "فشل في تحديث مفتاح Gemini API"
                )
            }
        }
    }

    /**
     * تحديث الرفع التلقائي
     */
    fun updateAutoUpload(autoUpload: Boolean) {
        viewModelScope.launch {
            userSettingsRepository.updateAutoUpload(autoUpload)
        }
    }

    /**
     * تحديث فترة الرفع
     */
    fun updateUploadInterval(intervalHours: Int) {
        viewModelScope.launch {
            val currentSettings = userSettingsRepository.getSettingsOnce()
            if (currentSettings != null) {
                val newSchedule = currentSettings.uploadSchedule.copy(
                    intervalHours = intervalHours
                )
                userSettingsRepository.updateUploadSchedule(newSchedule)
            }
        }
    }

    /**
     * إعداد YouTube
     */
    fun setupYouTube() {
        viewModelScope.launch {
            // هنا يمكن فتح نافذة لاختيار ملف Service Account
            // أو توجيه المستخدم لشاشة إعداد YouTube
            notificationHelper.showInfo(
                "إعداد YouTube",
                "يرجى اتباع التعليمات في دليل الإعداد"
            )
        }
    }

    /**
     * إعادة تعيين الإعدادات
     */
    fun resetSettings() {
        viewModelScope.launch {
            val result = userSettingsRepository.resetToDefaults()
            if (result.isSuccess) {
                notificationHelper.showSuccess(
                    "تم إعادة التعيين",
                    "تم إعادة تعيين جميع الإعدادات للقيم الافتراضية"
                )
                loadSettings()
                checkYouTubeSetup()
            } else {
                notificationHelper.showError(
                    "خطأ",
                    "فشل في إعادة تعيين الإعدادات"
                )
            }
        }
    }

    /**
     * تصدير البيانات
     */
    fun exportData() {
        viewModelScope.launch {
            // تنفيذ تصدير البيانات
            notificationHelper.showInfo(
                "تصدير البيانات",
                "ميزة تصدير البيانات قيد التطوير"
            )
        }
    }

    /**
     * استيراد البيانات
     */
    fun importData() {
        viewModelScope.launch {
            // تنفيذ استيراد البيانات
            notificationHelper.showInfo(
                "استيراد البيانات",
                "ميزة استيراد البيانات قيد التطوير"
            )
        }
    }
}

/**
 * حالة واجهة المستخدم للإعدادات
 */
data class SettingsUiState(
    val channelName: String = "",
    val geminiApiKey: String? = null,
    val autoUpload: Boolean = false,
    val uploadInterval: Int = 24,
    val isYouTubeConfigured: Boolean = false,
    val isLoading: Boolean = false,
    val errorMessage: String? = null
)
