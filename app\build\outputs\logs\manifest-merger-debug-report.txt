-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:93:9-101:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:97:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:95:13-64
	android:exported
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:96:13-37
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:94:13-62
provider#androidx.startup.InitializationProvider
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:104:9-113:20
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb36107710ab075c642fcd0ed204d1d8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb36107710ab075c642fcd0ed204d1d8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:108:13-31
	android:authorities
		INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:106:13-68
	android:exported
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:107:13-37
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:105:13-67
manifest
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:2:1-117:12
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:2:1-117:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74b501ce892fe2a4ee48d34996dbeeea\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bf6c416b41879c30e11b33404b1175\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [io.coil-kt:coil-video:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84fe473819812926d66c26d4a09f9a27\transformed\coil-video-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\121c3b7cb40ee446f259a2dbea377c98\transformed\coil-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7d11f5ec1694ae3e95c0b2fe9fa60da\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\008eeafed204ef2edc06d4f922d50d81\transformed\coil-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5dc1a1525553720b2a348a769dadc6\transformed\coil-base-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c853103115d8744e17f4a7a121a7343\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d592e4e76d99b11c1d7a6fa57b286ce0\transformed\appcompat-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90228465555eb2fdbf3fc049513c0124\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfaba32a112438aa463974556402c4f8\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75933f4abd07d8121ed29480522d71e3\transformed\navigation-common-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eea45ed4c501ffab9f6bd210572be7e1\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e592cb5e394b0295add37a54bcc7877b\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bf134f1fd69de10f8b21d0457598058\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee4be5af272621252f162435a18a24bb\transformed\navigation-compose-2.8.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.36.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d0a38e612997db06324233ea5768f6d\transformed\accompanist-permissions-0.36.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e789d1c3cb03c2f66092857ae417ac\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7ae1a6ed431cdfafdd65dd42862cf97\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cff627f52ae28d11dd8fb5fab02ee95f\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65888199de7fd9dc4fed140dc868fc85\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d6456133c97213fb437c1f83dcb39aa\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\255af3d6ef2446ea69dafc797919361a\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1cbac527d871b7e6d27789e9f88bba3\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e26e9695c3c042f49b261c7eda962c10\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b29be5cc48d8d6d9ab7ae3d64a70f1e\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0250d033f6c7fbf34d8deb63d91d80aa\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a34632dfcf5f2ab6ef31e836ce0108ae\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9f2f8ef73099164c430989bc7349e29\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6671061a1fef1cfe09255ef4b5e6ddd6\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f17526fd6e4ce5accb11d555ed69926\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8468e9d0513c4760b9edcbb33a94f0b\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-work:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1be1c0da3b76e30672aec6c40a1cc2df\transformed\hilt-work-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:17:1-145:12
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863333676c1193470ca460ff65c683f3\transformed\hilt-android-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\188c56a923eb2a563725f2186403d812\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:2:1-17:12
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20408f2caab9271f63cb7b7b40fdc83e\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da031785e9e97e2376a969299fa8fafe\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e683a80d9fc885a60ff80d30162f75ed\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d98cefeaf10770a9dbec9b1f0f6bd0f1\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4c032c997389a7d55e2711dcd35655e\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc939b11a2e99300e34800404ecf346c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd62721572eaf8a3192937cb16a8e54\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70ce67f9daa626db9dd6a114f7bbb2ee\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.ai.client.generativeai:generativeai:0.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967098dc902042434ded6f22b081b08a\transformed\generativeai-0.9.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.ai.client.generativeai:common:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d94b540f0c57f7b039a972eee0e460d\transformed\common-0.8.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c2ef9b29029af301fa775fdd6c30824\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b47882e636e3dacc8b2c53ab3b295bc\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f335465505abac0aea66576fea63870f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91bd110b80f798893b3f4dceb5241225\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\085d6b4a5209b95ee37c9951fb1dbd7d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9a01c83285071a0af6dbf435c518e0d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb8d890ca0be06d5e6c1f3248fd98d9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e61e4ecf2e47a9d7112803e5afc1b8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc7e3586f25856c70b7b4cb62637ffca\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e973a8321c1eee7fd106581ee28e0a3\transformed\exoplayer-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c78745d165c7fe915f21893f25ff4335\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edcb080f8f68c24bff3c649b9d670414\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cce6a7dbf0a9c99a0414ae8180de488\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56628244ac018cdf5ed1d398c5c33e32\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e773aa8fe135cf8e64f2a724d4a97259\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3407a89b161c62c55c6c5060d482379b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a10126c0c2cfe9f025de992aa0245b6\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffab5a8d8b684cfd07bdbbfff55beb14\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15da7b4d1af1a3e4d473405d890a22c8\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af84336a47387c527de9bfa14c152dd3\transformed\media-1.6.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00c80b3d31d0c137cf882a6f175f52b9\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\711da17f872cf811b5168a190163ccd2\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747b50e1edb89b3d6794a8da00b7b4e2\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f618f411b67207b5c7b3741b8c47120b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\251a3b03848125287cd9d93cd8f484a3\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc8631716eaa11a799f16e006032459\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9bef5bb877e771ecfecfa7c56f4d08e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\705dca1715fcb73ffba2dd35c08247f5\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5b759aab388421e225a1177a301a528\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8feec93404e5445ca1d4aea0baac440b\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b196e37791d23bd30262dca99e52b786\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8cc7f95d7817ccc9c5362cb018c17c0\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbe3755d4965af3e2d999b014316154\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e48816f26b954e1e448882f69ccb670\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\220df58bbe94a6fa8eab1ff1551126fa\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56e9db0e40842b04f2b585a1e63be574\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb8e86967b23f3817b33afb72264db53\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e2929dfddc09ad699dc46431928d2ba\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4428d2982c8ba397c8682b47863cd62\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce0a09bc308bdaa48d9a361b39ea4691\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\838ed64e7a99405ff2defe5b9492449b\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48ff4bf6c8b9e27d01c53c02f188fa35\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ec9f8195efcaf3a5ba16fa5dc4f3386\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973f8aeca6e111bd3a51e8f2823739b7\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e33aee235c12de75c4c539fae2edd62a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5f141eb8c7f4adca703c6d398212c57\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1fd568d5a1c4f4d2aea1aab03507053\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67ef87c2a457e0feb25e0703fcb0205d\transformed\activity-ktx-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\301d6d5ffec19614278a689a4e9d543d\transformed\activity-compose-1.10.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a90f4ec60d325988b128a1ae91e2bc8f\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c1d0d805d9e12d7556a57b397678396\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c300648a35d770bd9e37c558fcc527d\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e86461544982a48638b090a1a259b74\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bda18a51f8ce966f290039f575158bdc\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b373d185c4a077707aba984a06f57f8\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2df0d7822c1566a2bc555fa4d7660263\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f4bf47396a27e5e864cc944227a9001\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\004080671da35dd20bf89da3f7eda6b7\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\702274ff5bc8e4cbbe7fba0d2da21298\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e87f106ea653c45b6948e05e569cf7\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a665403ef232c9a272830c7eafd7950\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f92e34089c0993c5ab805d88252234b\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eae1f8ef12d6ab51f31f5e9ad5bcae76\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079d4988f6bd95227320eada3e23727a\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb36107710ab075c642fcd0ed204d1d8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb8f1b4d85331b911b10c7401fd95808\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f02e9af73adedc411d844eebfab037c8\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26cefb1a5e1a25926db5428b8ac76b05\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8355c7acbae33e97d411287c0b6d8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1793cb5c265f4039a1646f0589540cf4\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a627882c2dc375becc215d1158a66668\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51245718ad60958efc3d8592073f979a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa117a6b4b9eda00e6aa111b7b8f4a3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e724852da9fb213106e0fc1c723acd80\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82891900694a5337b61b364c2c8de4dc\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\334577b5b7b35302bd1b63525af23ef2\transformed\grpc-android-1.62.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7a4c041de417ca4e9babc237b0ee246\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79a409809141e49abab1f9574c7233d7\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.ai.client.generativeai:generativeai:0.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967098dc902042434ded6f22b081b08a\transformed\generativeai-0.9.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.ai.client.generativeai:generativeai:0.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967098dc902042434ded6f22b081b08a\transformed\generativeai-0.9.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.ai.client.generativeai:common:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d94b540f0c57f7b039a972eee0e460d\transformed\common-0.8.0\AndroidManifest.xml:22:5-67
MERGED from [com.google.ai.client.generativeai:common:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d94b540f0c57f7b039a972eee0e460d\transformed\common-0.8.0\AndroidManifest.xml:22:5-67
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:24:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e773aa8fe135cf8e64f2a724d4a97259\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e773aa8fe135cf8e64f2a724d4a97259\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079d4988f6bd95227320eada3e23727a\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079d4988f6bd95227320eada3e23727a\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:24:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\334577b5b7b35302bd1b63525af23ef2\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\334577b5b7b35302bd1b63525af23ef2\transformed\grpc-android-1.62.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:14:5-75
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:14:22-72
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:15:5-76
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:15:22-73
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:18:5-77
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:26:5-77
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:18:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROCESSING
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:19:5-94
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:19:22-91
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:20:5-68
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:23:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:20:22-65
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:21:5-95
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:21:22-92
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:24:5-77
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:24:22-74
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:27:5-79
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:27:22-76
uses-permission#android.permission.USE_EXACT_ALARM
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:28:5-74
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:28:22-71
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:31:5-81
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:5-81
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:25:5-81
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:31:22-78
application
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:33:5-115:19
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:33:5-115:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74b501ce892fe2a4ee48d34996dbeeea\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74b501ce892fe2a4ee48d34996dbeeea\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bf6c416b41879c30e11b33404b1175\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bf6c416b41879c30e11b33404b1175\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:28:5-143:19
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:7:5-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20408f2caab9271f63cb7b7b40fdc83e\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20408f2caab9271f63cb7b7b40fdc83e\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da031785e9e97e2376a969299fa8fafe\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da031785e9e97e2376a969299fa8fafe\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e683a80d9fc885a60ff80d30162f75ed\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e683a80d9fc885a60ff80d30162f75ed\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d98cefeaf10770a9dbec9b1f0f6bd0f1\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d98cefeaf10770a9dbec9b1f0f6bd0f1\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4c032c997389a7d55e2711dcd35655e\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4c032c997389a7d55e2711dcd35655e\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc939b11a2e99300e34800404ecf346c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc939b11a2e99300e34800404ecf346c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd62721572eaf8a3192937cb16a8e54\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd62721572eaf8a3192937cb16a8e54\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4428d2982c8ba397c8682b47863cd62\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4428d2982c8ba397c8682b47863cd62\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce0a09bc308bdaa48d9a361b39ea4691\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce0a09bc308bdaa48d9a361b39ea4691\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb36107710ab075c642fcd0ed204d1d8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb36107710ab075c642fcd0ed204d1d8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8355c7acbae33e97d411287c0b6d8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8355c7acbae33e97d411287c0b6d8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:43:9-52
	android:roundIcon
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:40:9-54
	android:largeHeap
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:44:9-33
	android:icon
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:38:9-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:41:9-35
	android:label
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:39:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:37:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:45:9-29
	android:allowBackup
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:35:9-35
	android:theme
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:42:9-45
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:36:9-65
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:34:9-43
activity#com.example.aiagent.MainActivity
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:48:9-57:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:52:13-49
	android:exported
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:51:13-49
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:49:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:53:13-56:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:54:17-69
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:54:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:55:17-77
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:55:27-74
service#com.example.aiagent.service.VideoProcessingService
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:60:9-64:63
	android:enabled
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:62:13-35
	android:exported
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:63:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:64:13-60
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:61:13-59
service#com.example.aiagent.service.YouTubeUploadService
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:67:9-71:56
	android:enabled
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:69:13-35
	android:exported
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:70:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:71:13-53
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:68:13-57
receiver#com.example.aiagent.receiver.BootReceiver
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:74:9-84:20
	android:enabled
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:76:13-35
	android:exported
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:77:13-36
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:75:13-50
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.PACKAGE_REPLACED+data:scheme:package
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:78:13-83:29
	android:priority
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:78:28-51
action#android.intent.action.BOOT_COMPLETED
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:79:17-79
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:79:25-76
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:80:17-84
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:80:25-81
action#android.intent.action.PACKAGE_REPLACED
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:81:17-81
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:81:25-78
data
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:82:17-50
	android:scheme
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:82:23-47
receiver#com.example.aiagent.receiver.AlarmReceiver
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:87:9-90:40
	android:enabled
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:90:13-37
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:88:13-51
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:98:13-100:54
	android:resource
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:100:17-51
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:99:17-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:109:13-112:39
REJECTED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:34:13-36:52
	tools:node
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:112:17-36
	android:value
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:111:17-49
	android:name
		ADDED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml:110:17-68
uses-sdk
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74b501ce892fe2a4ee48d34996dbeeea\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\74b501ce892fe2a4ee48d34996dbeeea\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bf6c416b41879c30e11b33404b1175\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62bf6c416b41879c30e11b33404b1175\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-video:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84fe473819812926d66c26d4a09f9a27\transformed\coil-video-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-video:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\84fe473819812926d66c26d4a09f9a27\transformed\coil-video-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\121c3b7cb40ee446f259a2dbea377c98\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\121c3b7cb40ee446f259a2dbea377c98\transformed\coil-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7d11f5ec1694ae3e95c0b2fe9fa60da\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-compose-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7d11f5ec1694ae3e95c0b2fe9fa60da\transformed\coil-compose-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\008eeafed204ef2edc06d4f922d50d81\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\008eeafed204ef2edc06d4f922d50d81\transformed\coil-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5dc1a1525553720b2a348a769dadc6\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [io.coil-kt:coil-base:2.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2a5dc1a1525553720b2a348a769dadc6\transformed\coil-base-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c853103115d8744e17f4a7a121a7343\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1c853103115d8744e17f4a7a121a7343\transformed\appcompat-resources-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d592e4e76d99b11c1d7a6fa57b286ce0\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d592e4e76d99b11c1d7a6fa57b286ce0\transformed\appcompat-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90228465555eb2fdbf3fc049513c0124\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90228465555eb2fdbf3fc049513c0124\transformed\hilt-navigation-compose-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfaba32a112438aa463974556402c4f8\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bfaba32a112438aa463974556402c4f8\transformed\hilt-navigation-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75933f4abd07d8121ed29480522d71e3\transformed\navigation-common-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\75933f4abd07d8121ed29480522d71e3\transformed\navigation-common-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eea45ed4c501ffab9f6bd210572be7e1\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eea45ed4c501ffab9f6bd210572be7e1\transformed\navigation-runtime-2.8.5\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e592cb5e394b0295add37a54bcc7877b\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e592cb5e394b0295add37a54bcc7877b\transformed\navigation-common-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bf134f1fd69de10f8b21d0457598058\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bf134f1fd69de10f8b21d0457598058\transformed\navigation-runtime-ktx-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee4be5af272621252f162435a18a24bb\transformed\navigation-compose-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee4be5af272621252f162435a18a24bb\transformed\navigation-compose-2.8.5\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.36.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d0a38e612997db06324233ea5768f6d\transformed\accompanist-permissions-0.36.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.36.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d0a38e612997db06324233ea5768f6d\transformed\accompanist-permissions-0.36.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e789d1c3cb03c2f66092857ae417ac\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\81e789d1c3cb03c2f66092857ae417ac\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7ae1a6ed431cdfafdd65dd42862cf97\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7ae1a6ed431cdfafdd65dd42862cf97\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cff627f52ae28d11dd8fb5fab02ee95f\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cff627f52ae28d11dd8fb5fab02ee95f\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65888199de7fd9dc4fed140dc868fc85\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\65888199de7fd9dc4fed140dc868fc85\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d6456133c97213fb437c1f83dcb39aa\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0d6456133c97213fb437c1f83dcb39aa\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\255af3d6ef2446ea69dafc797919361a\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\255af3d6ef2446ea69dafc797919361a\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1cbac527d871b7e6d27789e9f88bba3\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c1cbac527d871b7e6d27789e9f88bba3\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e26e9695c3c042f49b261c7eda962c10\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e26e9695c3c042f49b261c7eda962c10\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b29be5cc48d8d6d9ab7ae3d64a70f1e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b29be5cc48d8d6d9ab7ae3d64a70f1e\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0250d033f6c7fbf34d8deb63d91d80aa\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0250d033f6c7fbf34d8deb63d91d80aa\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a34632dfcf5f2ab6ef31e836ce0108ae\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a34632dfcf5f2ab6ef31e836ce0108ae\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9f2f8ef73099164c430989bc7349e29\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9f2f8ef73099164c430989bc7349e29\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6671061a1fef1cfe09255ef4b5e6ddd6\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6671061a1fef1cfe09255ef4b5e6ddd6\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f17526fd6e4ce5accb11d555ed69926\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9f17526fd6e4ce5accb11d555ed69926\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8468e9d0513c4760b9edcbb33a94f0b\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8468e9d0513c4760b9edcbb33a94f0b\transformed\work-runtime-ktx-2.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1be1c0da3b76e30672aec6c40a1cc2df\transformed\hilt-work-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-work:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1be1c0da3b76e30672aec6c40a1cc2df\transformed\hilt-work-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863333676c1193470ca460ff65c683f3\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\863333676c1193470ca460ff65c683f3\transformed\hilt-android-2.52\AndroidManifest.xml:18:3-42
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\188c56a923eb2a563725f2186403d812\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\188c56a923eb2a563725f2186403d812\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20408f2caab9271f63cb7b7b40fdc83e\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\20408f2caab9271f63cb7b7b40fdc83e\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da031785e9e97e2376a969299fa8fafe\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da031785e9e97e2376a969299fa8fafe\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e683a80d9fc885a60ff80d30162f75ed\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:19.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e683a80d9fc885a60ff80d30162f75ed\transformed\firebase-auth-interop-19.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d98cefeaf10770a9dbec9b1f0f6bd0f1\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d98cefeaf10770a9dbec9b1f0f6bd0f1\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4c032c997389a7d55e2711dcd35655e\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a4c032c997389a7d55e2711dcd35655e\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc939b11a2e99300e34800404ecf346c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fc939b11a2e99300e34800404ecf346c\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd62721572eaf8a3192937cb16a8e54\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9bd62721572eaf8a3192937cb16a8e54\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70ce67f9daa626db9dd6a114f7bbb2ee\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70ce67f9daa626db9dd6a114f7bbb2ee\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:generativeai:0.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967098dc902042434ded6f22b081b08a\transformed\generativeai-0.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:generativeai:0.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\967098dc902042434ded6f22b081b08a\transformed\generativeai-0.9.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:common:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d94b540f0c57f7b039a972eee0e460d\transformed\common-0.8.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ai.client.generativeai:common:0.8.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d94b540f0c57f7b039a972eee0e460d\transformed\common-0.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c2ef9b29029af301fa775fdd6c30824\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c2ef9b29029af301fa775fdd6c30824\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b47882e636e3dacc8b2c53ab3b295bc\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b47882e636e3dacc8b2c53ab3b295bc\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f335465505abac0aea66576fea63870f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f335465505abac0aea66576fea63870f\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91bd110b80f798893b3f4dceb5241225\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\91bd110b80f798893b3f4dceb5241225\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\085d6b4a5209b95ee37c9951fb1dbd7d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\085d6b4a5209b95ee37c9951fb1dbd7d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9a01c83285071a0af6dbf435c518e0d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a9a01c83285071a0af6dbf435c518e0d\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb8d890ca0be06d5e6c1f3248fd98d9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9cb8d890ca0be06d5e6c1f3248fd98d9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e61e4ecf2e47a9d7112803e5afc1b8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9e61e4ecf2e47a9d7112803e5afc1b8\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc7e3586f25856c70b7b4cb62637ffca\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc7e3586f25856c70b7b4cb62637ffca\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e973a8321c1eee7fd106581ee28e0a3\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e973a8321c1eee7fd106581ee28e0a3\transformed\exoplayer-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c78745d165c7fe915f21893f25ff4335\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c78745d165c7fe915f21893f25ff4335\transformed\exoplayer-dash-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edcb080f8f68c24bff3c649b9d670414\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\edcb080f8f68c24bff3c649b9d670414\transformed\exoplayer-hls-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cce6a7dbf0a9c99a0414ae8180de488\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cce6a7dbf0a9c99a0414ae8180de488\transformed\exoplayer-rtsp-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56628244ac018cdf5ed1d398c5c33e32\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56628244ac018cdf5ed1d398c5c33e32\transformed\exoplayer-smoothstreaming-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e773aa8fe135cf8e64f2a724d4a97259\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e773aa8fe135cf8e64f2a724d4a97259\transformed\exoplayer-core-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3407a89b161c62c55c6c5060d482379b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3407a89b161c62c55c6c5060d482379b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a10126c0c2cfe9f025de992aa0245b6\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a10126c0c2cfe9f025de992aa0245b6\transformed\exoplayer-ui-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffab5a8d8b684cfd07bdbbfff55beb14\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffab5a8d8b684cfd07bdbbfff55beb14\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15da7b4d1af1a3e4d473405d890a22c8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\15da7b4d1af1a3e4d473405d890a22c8\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af84336a47387c527de9bfa14c152dd3\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.6.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\af84336a47387c527de9bfa14c152dd3\transformed\media-1.6.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00c80b3d31d0c137cf882a6f175f52b9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\00c80b3d31d0c137cf882a6f175f52b9\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\711da17f872cf811b5168a190163ccd2\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\711da17f872cf811b5168a190163ccd2\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747b50e1edb89b3d6794a8da00b7b4e2\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\747b50e1edb89b3d6794a8da00b7b4e2\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f618f411b67207b5c7b3741b8c47120b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f618f411b67207b5c7b3741b8c47120b\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\251a3b03848125287cd9d93cd8f484a3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\251a3b03848125287cd9d93cd8f484a3\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc8631716eaa11a799f16e006032459\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bc8631716eaa11a799f16e006032459\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9bef5bb877e771ecfecfa7c56f4d08e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d9bef5bb877e771ecfecfa7c56f4d08e\transformed\lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\705dca1715fcb73ffba2dd35c08247f5\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\705dca1715fcb73ffba2dd35c08247f5\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5b759aab388421e225a1177a301a528\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d5b759aab388421e225a1177a301a528\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8feec93404e5445ca1d4aea0baac440b\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8feec93404e5445ca1d4aea0baac440b\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b196e37791d23bd30262dca99e52b786\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b196e37791d23bd30262dca99e52b786\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8cc7f95d7817ccc9c5362cb018c17c0\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f8cc7f95d7817ccc9c5362cb018c17c0\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbe3755d4965af3e2d999b014316154\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbe3755d4965af3e2d999b014316154\transformed\lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e48816f26b954e1e448882f69ccb670\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-drawablepainter:0.32.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5e48816f26b954e1e448882f69ccb670\transformed\accompanist-drawablepainter-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\220df58bbe94a6fa8eab1ff1551126fa\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\220df58bbe94a6fa8eab1ff1551126fa\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56e9db0e40842b04f2b585a1e63be574\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56e9db0e40842b04f2b585a1e63be574\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb8e86967b23f3817b33afb72264db53\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb8e86967b23f3817b33afb72264db53\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e2929dfddc09ad699dc46431928d2ba\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e2929dfddc09ad699dc46431928d2ba\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4428d2982c8ba397c8682b47863cd62\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d4428d2982c8ba397c8682b47863cd62\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce0a09bc308bdaa48d9a361b39ea4691\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ce0a09bc308bdaa48d9a361b39ea4691\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\838ed64e7a99405ff2defe5b9492449b\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\838ed64e7a99405ff2defe5b9492449b\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48ff4bf6c8b9e27d01c53c02f188fa35\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\48ff4bf6c8b9e27d01c53c02f188fa35\transformed\lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ec9f8195efcaf3a5ba16fa5dc4f3386\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ec9f8195efcaf3a5ba16fa5dc4f3386\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973f8aeca6e111bd3a51e8f2823739b7\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\973f8aeca6e111bd3a51e8f2823739b7\transformed\lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e33aee235c12de75c4c539fae2edd62a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e33aee235c12de75c4c539fae2edd62a\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5f141eb8c7f4adca703c6d398212c57\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c5f141eb8c7f4adca703c6d398212c57\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1fd568d5a1c4f4d2aea1aab03507053\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1fd568d5a1c4f4d2aea1aab03507053\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67ef87c2a457e0feb25e0703fcb0205d\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\67ef87c2a457e0feb25e0703fcb0205d\transformed\activity-ktx-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\301d6d5ffec19614278a689a4e9d543d\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\301d6d5ffec19614278a689a4e9d543d\transformed\activity-compose-1.10.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a90f4ec60d325988b128a1ae91e2bc8f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a90f4ec60d325988b128a1ae91e2bc8f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c1d0d805d9e12d7556a57b397678396\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5c1d0d805d9e12d7556a57b397678396\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c300648a35d770bd9e37c558fcc527d\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c300648a35d770bd9e37c558fcc527d\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e86461544982a48638b090a1a259b74\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6e86461544982a48638b090a1a259b74\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bda18a51f8ce966f290039f575158bdc\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bda18a51f8ce966f290039f575158bdc\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b373d185c4a077707aba984a06f57f8\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b373d185c4a077707aba984a06f57f8\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2df0d7822c1566a2bc555fa4d7660263\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2df0d7822c1566a2bc555fa4d7660263\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f4bf47396a27e5e864cc944227a9001\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5f4bf47396a27e5e864cc944227a9001\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\004080671da35dd20bf89da3f7eda6b7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\004080671da35dd20bf89da3f7eda6b7\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\702274ff5bc8e4cbbe7fba0d2da21298\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\702274ff5bc8e4cbbe7fba0d2da21298\transformed\exoplayer-datasource-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e87f106ea653c45b6948e05e569cf7\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b9e87f106ea653c45b6948e05e569cf7\transformed\exoplayer-database-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a665403ef232c9a272830c7eafd7950\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a665403ef232c9a272830c7eafd7950\transformed\exoplayer-extractor-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f92e34089c0993c5ab805d88252234b\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7f92e34089c0993c5ab805d88252234b\transformed\exoplayer-decoder-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eae1f8ef12d6ab51f31f5e9ad5bcae76\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-container:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eae1f8ef12d6ab51f31f5e9ad5bcae76\transformed\exoplayer-container-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079d4988f6bd95227320eada3e23727a\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.19.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\079d4988f6bd95227320eada3e23727a\transformed\exoplayer-common-2.19.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb36107710ab075c642fcd0ed204d1d8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb36107710ab075c642fcd0ed204d1d8\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb8f1b4d85331b911b10c7401fd95808\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb8f1b4d85331b911b10c7401fd95808\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f02e9af73adedc411d844eebfab037c8\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f02e9af73adedc411d844eebfab037c8\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26cefb1a5e1a25926db5428b8ac76b05\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26cefb1a5e1a25926db5428b8ac76b05\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8355c7acbae33e97d411287c0b6d8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8355c7acbae33e97d411287c0b6d8ae\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1793cb5c265f4039a1646f0589540cf4\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1793cb5c265f4039a1646f0589540cf4\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a627882c2dc375becc215d1158a66668\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a627882c2dc375becc215d1158a66668\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51245718ad60958efc3d8592073f979a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51245718ad60958efc3d8592073f979a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa117a6b4b9eda00e6aa111b7b8f4a3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa117a6b4b9eda00e6aa111b7b8f4a3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e724852da9fb213106e0fc1c723acd80\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e724852da9fb213106e0fc1c723acd80\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82891900694a5337b61b364c2c8de4dc\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82891900694a5337b61b364c2c8de4dc\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\334577b5b7b35302bd1b63525af23ef2\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.62.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\334577b5b7b35302bd1b63525af23ef2\transformed\grpc-android-1.62.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7a4c041de417ca4e9babc237b0ee246\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.52] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e7a4c041de417ca4e9babc237b0ee246\transformed\dagger-lint-aar-2.52\AndroidManifest.xml:18:3-42
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79a409809141e49abab1f9574c7233d7\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79a409809141e49abab1f9574c7233d7\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\aiagent\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\22c97e0ee13fe06279a44f73545701fd\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\79d094b981c46f6593fb5bd9ffda866d\transformed\ui-test-manifest-1.7.6\AndroidManifest.xml:24:13-63
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.10.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\73e0b08b05a514785d0fcacbe6463302\transformed\work-runtime-2.10.0\AndroidManifest.xml:140:25-85
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:8:9-14:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:10:13-84
meta-data#com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar
ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage-ktx:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\56151d029ef6a85be639116d647cef68\transformed\firebase-storage-ktx-21.0.1\AndroidManifest.xml:13:17-125
meta-data#com.google.firebase.components:com.google.firebase.analytics.ktx.FirebaseAnalyticsLegacyRegistrar
ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:11:13-13:85
	android:value
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:13:17-82
	android:name
		ADDED from [com.google.firebase:firebase-analytics-ktx:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8232c1a3cc2beebda142f668806d9373\transformed\firebase-analytics-ktx-22.1.2\AndroidManifest.xml:12:17-129
meta-data#com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar
ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore-ktx:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e736057e606c16d9d1095c01524bbe07\transformed\firebase-firestore-ktx-25.1.1\AndroidManifest.xml:13:17-129
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1b4942f3bc855cd294028fdbf56a3d18\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:25.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d13fc718b9bb2e54f6a1c3441c2f68d\transformed\firebase-firestore-25.1.1\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e922e556535b13d18252ba6e51de905\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d98cefeaf10770a9dbec9b1f0f6bd0f1\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d98cefeaf10770a9dbec9b1f0f6bd0f1\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ffa267f97e4afa573617e7d8f8da13f9\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2cfbbdfc8828c3705fe0680f662cd783\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\66e8262f023c7dec6e0adb991312f187\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da71baff361dc624a975ae49bc62f460\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3804ad40ccd7a2994c112f17bbf0b5a3\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\32360eb495b2b5290803f5ba748b4692\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\253dc95f04f85c8bb8bbbaacbacd5d32\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9ef319258a0e9e6112e78bb8dfe2f2f\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a51118f7b291e4eda17ebabf7c7f623e\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a7478e3cc73cddc4127d485bacc71e97\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.example.aiagent.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.aiagent.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ddb98e863a1a5c2e78bcacdf9de3476\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9ac2a67ed4005ac46ea5d7a69e175ca5\transformed\lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\24ae29dcf642bf0d2cd047ec3425cdd0\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e3ea35f85e286d63d1d36551d41cc478\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ba371c8e6e80451905d49b7ece1194da\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
