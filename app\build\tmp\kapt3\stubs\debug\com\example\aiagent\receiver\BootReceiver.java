package com.example.aiagent.receiver;

/**
 * مستقبل إعادة تشغيل الجهاز
 * يعيد تشغيل المهام المجدولة بعد إعادة تشغيل الجهاز
 */
@dagger.hilt.android.AndroidEntryPoint()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0018\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000eH\u0016J\u0010\u0010\u000f\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\fH\u0002R\u001e\u0010\u0003\u001a\u00020\u00048\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0005\u0010\u0006\"\u0004\b\u0007\u0010\b\u00a8\u0006\u0010"}, d2 = {"Lcom/example/aiagent/receiver/BootReceiver;", "Landroid/content/BroadcastReceiver;", "()V", "schedulerManager", "Lcom/example/aiagent/utils/SchedulerManager;", "getSchedulerManager", "()Lcom/example/aiagent/utils/SchedulerManager;", "setSchedulerManager", "(Lcom/example/aiagent/utils/SchedulerManager;)V", "onReceive", "", "context", "Landroid/content/Context;", "intent", "Landroid/content/Intent;", "restartScheduledTasks", "app_debug"})
public final class BootReceiver extends android.content.BroadcastReceiver {
    @javax.inject.Inject()
    public com.example.aiagent.utils.SchedulerManager schedulerManager;
    
    public BootReceiver() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.utils.SchedulerManager getSchedulerManager() {
        return null;
    }
    
    public final void setSchedulerManager(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.SchedulerManager p0) {
    }
    
    @java.lang.Override()
    public void onReceive(@org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    android.content.Intent intent) {
    }
    
    /**
     * إعادة تشغيل المهام المجدولة
     */
    private final void restartScheduledTasks(android.content.Context context) {
    }
}