{"logs": [{"outputFile": "com.example.aiagent.app-mergeDebugResources-88:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ba371c8e6e80451905d49b7ece1194da\\transformed\\play-services-basement-18.4.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6295", "endColumns": "142", "endOffsets": "6433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ddb98e863a1a5c2e78bcacdf9de3476\\transformed\\core-1.16.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "55,56,57,58,59,60,61,274", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4067,4163,4265,4364,4463,4569,4673,24244", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "4158,4260,4359,4458,4564,4668,4786,24340"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c1cbac527d871b7e6d27789e9f88bba3\\transformed\\foundation-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,143", "endColumns": "87,90", "endOffsets": "138,229"}, "to": {"startLines": "278,279", "startColumns": "4,4", "startOffsets": "24615,24703", "endColumns": "87,90", "endOffsets": "24698,24789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7478e3cc73cddc4127d485bacc71e97\\transformed\\play-services-base-18.5.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5285,5390,5539,5667,5777,5931,6065,6187,6438,6611,6719,6874,7002,7163,7302,7368,7429", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "5385,5534,5662,5772,5926,6060,6182,6290,6606,6714,6869,6997,7158,7297,7363,7424,7500"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\81e789d1c3cb03c2f66092857ae417ac\\transformed\\material3-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,411,525,625,724,840,976,1094,1242,1328,1430,1524,1622,1744,1864,1971,2106,2243,2378,2550,2679,2796,2914,3035,3130,3227,3345,3484,3587,3689,3800,3938,4078,4189,4292,4369,4464,4562,4672,4758,4845,4958,5038,5123,5224,5327,5421,5523,5609,5715,5811,5919,6036,6116,6222", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "168,285,406,520,620,719,835,971,1089,1237,1323,1425,1519,1617,1739,1859,1966,2101,2238,2373,2545,2674,2791,2909,3030,3125,3222,3340,3479,3582,3684,3795,3933,4073,4184,4287,4364,4459,4557,4667,4753,4840,4953,5033,5118,5219,5322,5416,5518,5604,5710,5806,5914,6031,6111,6217,6314"}, "to": {"startLines": "146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12256,12374,12491,12612,12726,12826,12925,13041,13177,13295,13443,13529,13631,13725,13823,13945,14065,14172,14307,14444,14579,14751,14880,14997,15115,15236,15331,15428,15546,15685,15788,15890,16001,16139,16279,16390,16493,16570,16665,16763,16873,16959,17046,17159,17239,17324,17425,17528,17622,17724,17810,17916,18012,18120,18237,18317,18423", "endColumns": "117,116,120,113,99,98,115,135,117,147,85,101,93,97,121,119,106,134,136,134,171,128,116,117,120,94,96,117,138,102,101,110,137,139,110,102,76,94,97,109,85,86,112,79,84,100,102,93,101,85,105,95,107,116,79,105,96", "endOffsets": "12369,12486,12607,12721,12821,12920,13036,13172,13290,13438,13524,13626,13720,13818,13940,14060,14167,14302,14439,14574,14746,14875,14992,15110,15231,15326,15423,15541,15680,15783,15885,15996,16134,16274,16385,16488,16565,16660,16758,16868,16954,17041,17154,17234,17319,17420,17523,17617,17719,17805,17911,18007,18115,18232,18312,18418,18515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e773aa8fe135cf8e64f2a724d4a97259\\transformed\\exoplayer-core-2.19.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,127,192,270,343,424,499,587,674", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "122,187,265,338,419,494,582,669,754"}, "to": {"startLines": "114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10005,10077,10142,10220,10293,10374,10449,10537,10624", "endColumns": "71,64,77,72,80,74,87,86,84", "endOffsets": "10072,10137,10215,10288,10369,10444,10532,10619,10704"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d592e4e76d99b11c1d7a6fa57b286ce0\\transformed\\appcompat-1.7.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "941,1049,1152,1263,1349,1454,1567,1650,1729,1820,1913,2008,2102,2202,2295,2390,2485,2576,2667,2748,2861,2967,3065,3178,3283,3387,3545,23613", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "1044,1147,1258,1344,1449,1562,1645,1724,1815,1908,2003,2097,2197,2290,2385,2480,2571,2662,2743,2856,2962,3060,3173,3278,3382,3540,3639,23690"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\74b501ce892fe2a4ee48d34996dbeeea\\transformed\\material-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1071,1138,1235,1304,1367,1454,1518,1584,1644,1713,1774,1828,1943,2002,2062,2116,2188,2318,2406,2485,2583,2671,2755,2893,2971,3047,3186,3280,3360,3416,3470,3536,3609,3687,3758,3842,3915,3993,4066,4141,4251,4341,4416,4510,4608,4682,4759,4859,4912,4996,5064,5153,5242,5304,5369,5432,5502,5609,5709,5809,5905,5965,6023,6103,6193,6268", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "268,346,420,504,597,691,790,915,1003,1066,1133,1230,1299,1362,1449,1513,1579,1639,1708,1769,1823,1938,1997,2057,2111,2183,2313,2401,2480,2578,2666,2750,2888,2966,3042,3181,3275,3355,3411,3465,3531,3604,3682,3753,3837,3910,3988,4061,4136,4246,4336,4411,4505,4603,4677,4754,4854,4907,4991,5059,5148,5237,5299,5364,5427,5497,5604,5704,5804,5900,5960,6018,6098,6188,6263,6344"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,88,89,141,142,145,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,263,267,268,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "768,3644,3722,3796,3880,3973,4791,4890,5015,7797,7860,11851,11948,12193,18520,18607,18671,18737,18797,18866,18927,18981,19096,19155,19215,19269,19341,19471,19559,19638,19736,19824,19908,20046,20124,20200,20339,20433,20513,20569,20623,20689,20762,20840,20911,20995,21068,21146,21219,21294,21404,21494,21569,21663,21761,21835,21912,22012,22065,22149,22217,22306,22395,22457,22522,22585,22655,22762,22862,22962,23058,23118,23353,23695,23785,23932", "endLines": "22,50,51,52,53,54,62,63,64,88,89,141,142,145,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,263,267,268,270", "endColumns": "12,77,73,83,92,93,98,124,87,62,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,78,97,87,83,137,77,75,138,93,79,55,53,65,72,77,70,83,72,77,72,74,109,89,74,93,97,73,76,99,52,83,67,88,88,61,64,62,69,106,99,99,95,59,57,79,89,74,80", "endOffsets": "936,3717,3791,3875,3968,4062,4885,5010,5098,7855,7922,11943,12012,12251,18602,18666,18732,18792,18861,18922,18976,19091,19150,19210,19264,19336,19466,19554,19633,19731,19819,19903,20041,20119,20195,20334,20428,20508,20564,20618,20684,20757,20835,20906,20990,21063,21141,21214,21289,21399,21489,21564,21658,21756,21830,21907,22007,22060,22144,22212,22301,22390,22452,22517,22580,22650,22757,22857,22957,23053,23113,23171,23428,23780,23855,24008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0a10126c0c2cfe9f025de992aa0245b6\\transformed\\exoplayer-ui-2.19.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,290,483,673,757,841,920,1018,1120,1205,1270,1369,1468,1533,1598,1662,1729,1857,1986,2113,2188,2267,2341,2426,2522,2618,2685,2751,2804,2865,2913,2974,3040,3119,3183,3251,3315,3376,3442,3508,3574,3626,3688,3764,3840", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,65,65,51,61,75,75,52", "endOffsets": "285,478,668,752,836,915,1013,1115,1200,1265,1364,1463,1528,1593,1657,1724,1852,1981,2108,2183,2262,2336,2421,2517,2613,2680,2746,2799,2860,2908,2969,3035,3114,3178,3246,3310,3371,3437,3503,3569,3621,3683,3759,3835,3888"}, "to": {"startLines": "2,11,15,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,385,578,7927,8011,8095,8174,8272,8374,8459,8524,8623,8722,8787,8852,8916,8983,9111,9240,9367,9442,9521,9595,9680,9776,9872,9939,10709,10762,10823,10871,10932,10998,11077,11141,11209,11273,11334,11400,11466,11532,11584,11646,11722,11798", "endLines": "10,14,18,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "endColumns": "17,12,12,83,83,78,97,101,84,64,98,98,64,64,63,66,127,128,126,74,78,73,84,95,95,66,65,52,60,47,60,65,78,63,67,63,60,65,65,65,51,61,75,75,52", "endOffsets": "380,573,763,8006,8090,8169,8267,8369,8454,8519,8618,8717,8782,8847,8911,8978,9106,9235,9362,9437,9516,9590,9675,9771,9867,9934,10000,10757,10818,10866,10927,10993,11072,11136,11204,11268,11329,11395,11461,11527,11579,11641,11717,11793,11846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1fd568d5a1c4f4d2aea1aab03507053\\transformed\\ui-release\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,287,386,489,579,659,755,845,932,1021,1112,1184,1262,1340,1415,1494,1564", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "196,282,381,484,574,654,750,840,927,1016,1107,1179,1257,1335,1410,1489,1559,1680"}, "to": {"startLines": "65,66,85,86,87,143,144,261,262,264,265,269,271,272,273,275,276,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5103,5199,7505,7604,7707,12017,12097,23176,23266,23433,23522,23860,24013,24091,24169,24345,24424,24494", "endColumns": "95,85,98,102,89,79,95,89,86,88,90,71,77,77,74,78,69,120", "endOffsets": "5194,5280,7599,7702,7792,12092,12188,23261,23348,23517,23608,23927,24086,24164,24239,24419,24489,24610"}}]}]}