package com.example.aiagent.utils;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class VideoProcessor_Factory implements Factory<VideoProcessor> {
  private final Provider<Context> contextProvider;

  public VideoProcessor_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public VideoProcessor get() {
    return newInstance(contextProvider.get());
  }

  public static VideoProcessor_Factory create(Provider<Context> contextProvider) {
    return new VideoProcessor_Factory(contextProvider);
  }

  public static VideoProcessor newInstance(Context context) {
    return new VideoProcessor(context);
  }
}
