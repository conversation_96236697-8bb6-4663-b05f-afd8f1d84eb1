package com.example.aiagent.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.aiagent.ui.theme.*

/**
 * قسم الإجراءات السريعة
 */
@Composable
fun QuickActionsSection(
    onCreateProject: () -> Unit,
    onScheduleProject: () -> Unit,
    onViewAnalytics: () -> Unit,
    onOpenSettings: () -> Unit
) {
    Column {
        Text(
            text = "إجراءات سريعة",
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 12.dp)
        )
        
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            items(
                listOf(
                    QuickAction(
                        title = "إنشاء فيديو",
                        subtitle = "إنشاء فوري",
                        icon = Icons.Default.Add,
                        color = AccentGreen,
                        onClick = onCreateProject
                    ),
                    QuickAction(
                        title = "جدولة فيديو",
                        subtitle = "رفع مؤجل",
                        icon = Icons.Default.Schedule,
                        color = AccentOrange,
                        onClick = onScheduleProject
                    ),
                    QuickAction(
                        title = "الإحصائيات",
                        subtitle = "عرض التقارير",
                        icon = Icons.Default.Analytics,
                        color = AccentPurple,
                        onClick = onViewAnalytics
                    ),
                    QuickAction(
                        title = "الإعدادات",
                        subtitle = "تخصيص التطبيق",
                        icon = Icons.Default.Settings,
                        color = ModernBlue,
                        onClick = onOpenSettings
                    )
                )
            ) { action ->
                QuickActionCard(action = action)
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun QuickActionCard(action: QuickAction) {
    Card(
        onClick = action.onClick,
        modifier = Modifier
            .width(160.dp)
            .height(120.dp),
        colors = CardDefaults.cardColors(
            containerColor = action.color.copy(alpha = 0.1f)
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            verticalArrangement = Arrangement.SpaceBetween,
            horizontalAlignment = Alignment.Start
        ) {
            Icon(
                imageVector = action.icon,
                contentDescription = null,
                tint = action.color,
                modifier = Modifier.size(32.dp)
            )
            
            Column {
                Text(
                    text = action.title,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Text(
                    text = action.subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

data class QuickAction(
    val title: String,
    val subtitle: String,
    val icon: ImageVector,
    val color: androidx.compose.ui.graphics.Color,
    val onClick: () -> Unit
)
