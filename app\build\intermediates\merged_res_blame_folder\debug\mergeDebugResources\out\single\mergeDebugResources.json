[{"merged": "com.example.aiagent.app-debug-90:/drawable_ic_upload.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_upload.xml"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_settings.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_settings.xml"}, {"merged": "com.example.aiagent.app-debug-90:/font_roboto_bold.xml.flat", "source": "com.example.aiagent.app-main-92:/font/roboto_bold.xml"}, {"merged": "com.example.aiagent.app-debug-90:/font_roboto_regular.xml.flat", "source": "com.example.aiagent.app-main-92:/font/roboto_regular.xml"}, {"merged": "com.example.aiagent.app-debug-90:/layout_activity_main.xml.flat", "source": "com.example.aiagent.app-main-92:/layout/activity_main.xml"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_launcher_background.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.example.aiagent.app-main-92:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_error.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_error.xml"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_launcher_foreground.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_permission.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_permission.xml"}, {"merged": "com.example.aiagent.app-debug-90:/xml_backup_rules.xml.flat", "source": "com.example.aiagent.app-main-92:/xml/backup_rules.xml"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_success.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_success.xml"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_stop.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_stop.xml"}, {"merged": "com.example.aiagent.app-debug-90:/xml_data_extraction_rules.xml.flat", "source": "com.example.aiagent.app-main-92:/xml/data_extraction_rules.xml"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_youtube.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_youtube.xml"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.example.aiagent.app-main-92:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_video_processing.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_video_processing.xml"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_app.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_app.xml"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_processing.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_processing.xml"}, {"merged": "com.example.aiagent.app-debug-90:/font_roboto_medium.xml.flat", "source": "com.example.aiagent.app-main-92:/font/roboto_medium.xml"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_info.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_info.xml"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.aiagent.app-debug-90:/drawable_ic_warning.xml.flat", "source": "com.example.aiagent.app-main-92:/drawable/ic_warning.xml"}, {"merged": "com.example.aiagent.app-debug-90:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.aiagent.app-main-92:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.aiagent.app-debug-90:/xml_file_paths.xml.flat", "source": "com.example.aiagent.app-main-92:/xml/file_paths.xml"}]