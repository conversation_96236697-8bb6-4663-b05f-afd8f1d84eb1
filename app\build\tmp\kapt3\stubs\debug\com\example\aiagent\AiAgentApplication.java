package com.example.aiagent;

/**
 * فئة التطبيق الرئيسية مع إعداد Hilt و Work Manager
 */
@dagger.hilt.android.HiltAndroidApp()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0010\u0002\n\u0002\b\u0005\b\u0007\u0018\u0000 \u00192\u00020\u00012\u00020\u0002:\u0001\u0019B\u0005\u00a2\u0006\u0002\u0010\u0003J\b\u0010\u0014\u001a\u00020\u0015H\u0002J\b\u0010\u0016\u001a\u00020\u0015H\u0002J\b\u0010\u0017\u001a\u00020\u0015H\u0016J\b\u0010\u0018\u001a\u00020\u0015H\u0002R\u001e\u0010\u0004\u001a\u00020\u00058\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0004\b\b\u0010\tR\u0014\u0010\n\u001a\u00020\u000b8VX\u0096\u0004\u00a2\u0006\u0006\u001a\u0004\b\f\u0010\rR\u001e\u0010\u000e\u001a\u00020\u000f8\u0006@\u0006X\u0087.\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0010\u0010\u0011\"\u0004\b\u0012\u0010\u0013\u00a8\u0006\u001a"}, d2 = {"Lcom/example/aiagent/AiAgentApplication;", "Landroid/app/Application;", "Landroidx/work/Configuration$Provider;", "()V", "firebaseInitializer", "Lcom/example/aiagent/utils/FirebaseInitializer;", "getFirebaseInitializer", "()Lcom/example/aiagent/utils/FirebaseInitializer;", "setFirebaseInitializer", "(Lcom/example/aiagent/utils/FirebaseInitializer;)V", "workManagerConfiguration", "Landroidx/work/Configuration;", "getWorkManagerConfiguration", "()Landroidx/work/Configuration;", "workerFactory", "Landroidx/hilt/work/HiltWorkerFactory;", "getWorkerFactory", "()Landroidx/hilt/work/HiltWorkerFactory;", "setWorkerFactory", "(Landroidx/hilt/work/HiltWorkerFactory;)V", "createNotificationChannels", "", "initializeFirebase", "onCreate", "setupWorkManager", "Companion", "app_debug"})
public final class AiAgentApplication extends android.app.Application implements androidx.work.Configuration.Provider {
    @javax.inject.Inject()
    public androidx.hilt.work.HiltWorkerFactory workerFactory;
    @javax.inject.Inject()
    public com.example.aiagent.utils.FirebaseInitializer firebaseInitializer;
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_VIDEO_PROCESSING = "video_processing";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_UPLOAD = "upload";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_ERROR = "error";
    @org.jetbrains.annotations.NotNull()
    public static final java.lang.String CHANNEL_SUCCESS = "success";
    public static final int NOTIFICATION_ID_VIDEO_PROCESSING = 1001;
    public static final int NOTIFICATION_ID_UPLOAD = 1002;
    public static final int NOTIFICATION_ID_ERROR = 1003;
    public static final int NOTIFICATION_ID_SUCCESS = 1004;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.aiagent.AiAgentApplication.Companion Companion = null;
    
    public AiAgentApplication() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final androidx.hilt.work.HiltWorkerFactory getWorkerFactory() {
        return null;
    }
    
    public final void setWorkerFactory(@org.jetbrains.annotations.NotNull()
    androidx.hilt.work.HiltWorkerFactory p0) {
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.utils.FirebaseInitializer getFirebaseInitializer() {
        return null;
    }
    
    public final void setFirebaseInitializer(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.FirebaseInitializer p0) {
    }
    
    @java.lang.Override()
    public void onCreate() {
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public androidx.work.Configuration getWorkManagerConfiguration() {
        return null;
    }
    
    private final void setupWorkManager() {
    }
    
    /**
     * تهيئة Firebase
     */
    private final void initializeFirebase() {
    }
    
    /**
     * إنشاء قنوات الإشعارات المطلوبة
     */
    private final void createNotificationChannels() {
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\b\n\u0002\b\u0004\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u000e\u0010\u0003\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0005\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0007\u001a\u00020\u0004X\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000b\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\tX\u0086T\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/example/aiagent/AiAgentApplication$Companion;", "", "()V", "CHANNEL_ERROR", "", "CHANNEL_SUCCESS", "CHANNEL_UPLOAD", "CHANNEL_VIDEO_PROCESSING", "NOTIFICATION_ID_ERROR", "", "NOTIFICATION_ID_SUCCESS", "NOTIFICATION_ID_UPLOAD", "NOTIFICATION_ID_VIDEO_PROCESSING", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
    }
}