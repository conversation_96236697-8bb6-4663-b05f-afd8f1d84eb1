package com.example.aiagent.data.repository

import com.example.aiagent.data.database.UserSettingsDao
import com.example.aiagent.data.model.UserSettings
import com.example.aiagent.data.model.UploadSchedule
import com.example.aiagent.data.model.WatermarkStyle
import com.example.aiagent.data.model.VideoQuality
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مستودع إعدادات المستخدم
 */
@Singleton
class UserSettingsRepository @Inject constructor(
    private val userSettingsDao: UserSettingsDao
) {
    
    /**
     * الحصول على الإعدادات كـ Flow
     */
    fun getSettings(): Flow<UserSettings?> {
        return userSettingsDao.getSettings()
    }
    
    /**
     * الحصول على الإعدادات مرة واحدة
     */
    suspend fun getSettingsOnce(): UserSettings? {
        return userSettingsDao.getSettingsOnce()
    }
    
    /**
     * الحصول على الإعدادات مع القيم الافتراضية
     */
    fun getSettingsWithDefaults(): Flow<UserSettings> {
        return userSettingsDao.getSettings().map { settings ->
            settings ?: getDefaultSettings()
        }
    }
    
    /**
     * حفظ الإعدادات
     */
    suspend fun saveSettings(settings: UserSettings): Result<Unit> {
        return try {
            userSettingsDao.insertSettings(settings)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث اسم القناة
     */
    suspend fun updateChannelName(channelName: String): Result<Unit> {
        return try {
            userSettingsDao.updateChannelName(channelName)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث الرفع التلقائي
     */
    suspend fun updateAutoUpload(autoUpload: Boolean): Result<Unit> {
        return try {
            userSettingsDao.updateAutoUpload(autoUpload)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث طلب تحسين البطارية
     */
    suspend fun updateBatteryOptimizationRequested(requested: Boolean): Result<Unit> {
        return try {
            userSettingsDao.updateBatteryOptimizationRequested(requested)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث مفتاح Gemini API
     */
    suspend fun updateGeminiApiKey(apiKey: String?): Result<Unit> {
        return try {
            userSettingsDao.updateGeminiApiKey(apiKey)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث مسار ملف YouTube Service Account
     */
    suspend fun updateYouTubeServiceAccountPath(path: String?): Result<Unit> {
        return try {
            userSettingsDao.updateYouTubeServiceAccountPath(path)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث جدولة الرفع
     */
    suspend fun updateUploadSchedule(schedule: UploadSchedule): Result<Unit> {
        return try {
            val currentSettings = getSettingsOnce() ?: getDefaultSettings()
            val updatedSettings = currentSettings.copy(uploadSchedule = schedule)
            saveSettings(updatedSettings)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث نمط العلامة المائية
     */
    suspend fun updateWatermarkStyle(style: WatermarkStyle): Result<Unit> {
        return try {
            val currentSettings = getSettingsOnce() ?: getDefaultSettings()
            val updatedSettings = currentSettings.copy(watermarkStyle = style)
            saveSettings(updatedSettings)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث جودة الفيديو
     */
    suspend fun updateVideoQuality(quality: VideoQuality): Result<Unit> {
        return try {
            val currentSettings = getSettingsOnce() ?: getDefaultSettings()
            val updatedSettings = currentSettings.copy(videoQuality = quality)
            saveSettings(updatedSettings)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    suspend fun resetToDefaults(): Result<Unit> {
        return try {
            userSettingsDao.deleteAllSettings()
            saveSettings(getDefaultSettings())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * التحقق من اكتمال الإعداد الأولي
     */
    suspend fun isInitialSetupComplete(): Boolean {
        val settings = getSettingsOnce()
        return settings != null && 
               settings.channelName.isNotEmpty() &&
               (!settings.geminiApiKey.isNullOrEmpty() || !settings.youtubeServiceAccountPath.isNullOrEmpty())
    }
    
    /**
     * الحصول على الإعدادات الافتراضية
     */
    private fun getDefaultSettings(): UserSettings {
        return UserSettings(
            channelName = "",
            watermarkStyle = WatermarkStyle.BOTTOM_RIGHT,
            uploadSchedule = UploadSchedule(
                intervalHours = 24,
                startTime = "09:00",
                enabled = true
            ),
            videoQuality = VideoQuality.MEDIUM,
            autoUpload = false,
            batteryOptimizationRequested = false,
            geminiApiKey = null,
            youtubeServiceAccountPath = null
        )
    }
}
