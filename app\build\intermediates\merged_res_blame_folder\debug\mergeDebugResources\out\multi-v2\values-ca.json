{"logs": [{"outputFile": "com.example.aiagent.app-mergeDebugResources-88:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7478e3cc73cddc4127d485bacc71e97\\transformed\\play-services-base-18.5.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5329,5434,5586,5713,5822,5972,6099,6222,6465,6636,6745,6904,7035,7199,7357,7422,7490", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "5429,5581,5708,5817,5967,6094,6217,6325,6631,6740,6899,7030,7194,7352,7417,7485,7572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\74b501ce892fe2a4ee48d34996dbeeea\\transformed\\material-1.12.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,523,626,718,819,947,1031,1092,1157,1254,1334,1399,1494,1558,1630,1692,1768,1831,1888,2009,2067,2128,2185,2265,2402,2489,2564,2657,2737,2821,2960,3038,3117,3269,3358,3434,3491,3547,3613,3691,3772,3843,3931,4009,4086,4160,4239,4349,4439,4531,4623,4724,4798,4880,4981,5031,5114,5180,5272,5359,5421,5485,5548,5621,5744,5857,5961,6069,6130,6190,6276,6362,6439", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "269,350,430,518,621,713,814,942,1026,1087,1152,1249,1329,1394,1489,1553,1625,1687,1763,1826,1883,2004,2062,2123,2180,2260,2397,2484,2559,2652,2732,2816,2955,3033,3112,3264,3353,3429,3486,3542,3608,3686,3767,3838,3926,4004,4081,4155,4234,4344,4434,4526,4618,4719,4793,4875,4976,5026,5109,5175,5267,5354,5416,5480,5543,5616,5739,5852,5956,6064,6125,6185,6271,6357,6434,6513"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,88,89,141,142,145,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,263,267,268,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "761,3660,3741,3821,3909,4012,4835,4936,5064,7873,7934,11883,11980,12229,18703,18798,18862,18934,18996,19072,19135,19192,19313,19371,19432,19489,19569,19706,19793,19868,19961,20041,20125,20264,20342,20421,20573,20662,20738,20795,20851,20917,20995,21076,21147,21235,21313,21390,21464,21543,21653,21743,21835,21927,22028,22102,22184,22285,22335,22418,22484,22576,22663,22725,22789,22852,22925,23048,23161,23265,23373,23434,23671,24016,24102,24255", "endLines": "22,50,51,52,53,54,62,63,64,88,89,141,142,145,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,263,267,268,270", "endColumns": "12,80,79,87,102,91,100,127,83,60,64,96,79,64,94,63,71,61,75,62,56,120,57,60,56,79,136,86,74,92,79,83,138,77,78,151,88,75,56,55,65,77,80,70,87,77,76,73,78,109,89,91,91,100,73,81,100,49,82,65,91,86,61,63,62,72,122,112,103,107,60,59,85,85,76,78", "endOffsets": "930,3736,3816,3904,4007,4099,4931,5059,5143,7929,7994,11975,12055,12289,18793,18857,18929,18991,19067,19130,19187,19308,19366,19427,19484,19564,19701,19788,19863,19956,20036,20120,20259,20337,20416,20568,20657,20733,20790,20846,20912,20990,21071,21142,21230,21308,21385,21459,21538,21648,21738,21830,21922,22023,22097,22179,22280,22330,22413,22479,22571,22658,22720,22784,22847,22920,23043,23156,23260,23368,23429,23489,23752,24097,24174,24329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ba371c8e6e80451905d49b7ece1194da\\transformed\\play-services-basement-18.4.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6330", "endColumns": "134", "endOffsets": "6460"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c1cbac527d871b7e6d27789e9f88bba3\\transformed\\foundation-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,101", "endOffsets": "148,250"}, "to": {"startLines": "278,279", "startColumns": "4,4", "startOffsets": "24937,25035", "endColumns": "97,101", "endOffsets": "25030,25132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e773aa8fe135cf8e64f2a724d4a97259\\transformed\\exoplayer-core-2.19.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,261,331,407,483,581,676", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "128,187,256,326,402,478,576,671,753"}, "to": {"startLines": "114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "10054,10132,10191,10260,10330,10406,10482,10580,10675", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "10127,10186,10255,10325,10401,10477,10575,10670,10752"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\81e789d1c3cb03c2f66092857ae417ac\\transformed\\material3-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,176,295,414,534,634,732,847,989,1104,1263,1347,1445,1543,1644,1761,1890,1993,2134,2274,2415,2581,2714,2831,2952,3081,3180,3277,3398,3543,3649,3762,3876,4015,4160,4269,4376,4462,4563,4664,4775,4861,4947,5058,5138,5222,5323,5431,5530,5634,5721,5834,5934,6041,6160,6240,6357", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "171,290,409,529,629,727,842,984,1099,1258,1342,1440,1538,1639,1756,1885,1988,2129,2269,2410,2576,2709,2826,2947,3076,3175,3272,3393,3538,3644,3757,3871,4010,4155,4264,4371,4457,4558,4659,4770,4856,4942,5053,5133,5217,5318,5426,5525,5629,5716,5829,5929,6036,6155,6235,6352,6459"}, "to": {"startLines": "146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12294,12415,12534,12653,12773,12873,12971,13086,13228,13343,13502,13586,13684,13782,13883,14000,14129,14232,14373,14513,14654,14820,14953,15070,15191,15320,15419,15516,15637,15782,15888,16001,16115,16254,16399,16508,16615,16701,16802,16903,17014,17100,17186,17297,17377,17461,17562,17670,17769,17873,17960,18073,18173,18280,18399,18479,18596", "endColumns": "120,118,118,119,99,97,114,141,114,158,83,97,97,100,116,128,102,140,139,140,165,132,116,120,128,98,96,120,144,105,112,113,138,144,108,106,85,100,100,110,85,85,110,79,83,100,107,98,103,86,112,99,106,118,79,116,106", "endOffsets": "12410,12529,12648,12768,12868,12966,13081,13223,13338,13497,13581,13679,13777,13878,13995,14124,14227,14368,14508,14649,14815,14948,15065,15186,15315,15414,15511,15632,15777,15883,15996,16110,16249,16394,16503,16610,16696,16797,16898,17009,17095,17181,17292,17372,17456,17557,17665,17764,17868,17955,18068,18168,18275,18394,18474,18591,18698"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d592e4e76d99b11c1d7a6fa57b286ce0\\transformed\\appcompat-1.7.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,228,333,440,523,629,755,839,918,1009,1102,1195,1290,1388,1481,1574,1668,1759,1850,1931,2042,2150,2248,2358,2463,2571,2731,2830", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "223,328,435,518,624,750,834,913,1004,1097,1190,1285,1383,1476,1569,1663,1754,1845,1926,2037,2145,2243,2353,2458,2566,2726,2825,2907"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1058,1163,1270,1353,1459,1585,1669,1748,1839,1932,2025,2120,2218,2311,2404,2498,2589,2680,2761,2872,2980,3078,3188,3293,3401,3561,23934", "endColumns": "122,104,106,82,105,125,83,78,90,92,92,94,97,92,92,93,90,90,80,110,107,97,109,104,107,159,98,81", "endOffsets": "1053,1158,1265,1348,1454,1580,1664,1743,1834,1927,2020,2115,2213,2306,2399,2493,2584,2675,2756,2867,2975,3073,3183,3288,3396,3556,3655,24011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ddb98e863a1a5c2e78bcacdf9de3476\\transformed\\core-1.16.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "55,56,57,58,59,60,61,274", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4104,4200,4302,4401,4498,4604,4709,24563", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "4195,4297,4396,4493,4599,4704,4830,24659"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0a10126c0c2cfe9f025de992aa0245b6\\transformed\\exoplayer-ui-2.19.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,487,666,752,840,923,1022,1120,1201,1267,1380,1490,1563,1632,1698,1769,1879,1990,2099,2168,2256,2331,2413,2502,2593,2657,2721,2774,2832,2880,2941,3006,3075,3140,3212,3276,3333,3399,3463,3529,3582,3642,3716,3790", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,109,110,108,68,87,74,81,88,90,63,63,52,57,47,60,64,68,64,71,63,56,65,63,65,52,59,73,73,56", "endOffsets": "280,482,661,747,835,918,1017,1115,1196,1262,1375,1485,1558,1627,1693,1764,1874,1985,2094,2163,2251,2326,2408,2497,2588,2652,2716,2769,2827,2875,2936,3001,3070,3135,3207,3271,3328,3394,3458,3524,3577,3637,3711,3785,3842"}, "to": {"startLines": "2,11,15,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,582,7999,8085,8173,8256,8355,8453,8534,8600,8713,8823,8896,8965,9031,9102,9212,9323,9432,9501,9589,9664,9746,9835,9926,9990,10757,10810,10868,10916,10977,11042,11111,11176,11248,11312,11369,11435,11499,11565,11618,11678,11752,11826", "endLines": "10,14,18,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "endColumns": "17,12,12,85,87,82,98,97,80,65,112,109,72,68,65,70,109,110,108,68,87,74,81,88,90,63,63,52,57,47,60,64,68,64,71,63,56,65,63,65,52,59,73,73,56", "endOffsets": "375,577,756,8080,8168,8251,8350,8448,8529,8595,8708,8818,8891,8960,9026,9097,9207,9318,9427,9496,9584,9659,9741,9830,9921,9985,10049,10805,10863,10911,10972,11037,11106,11171,11243,11307,11364,11430,11494,11560,11613,11673,11747,11821,11878"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1fd568d5a1c4f4d2aea1aab03507053\\transformed\\ui-release\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,390,493,582,660,751,842,928,1014,1105,1181,1256,1335,1410,1492,1563", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,74,78,74,81,70,119", "endOffsets": "197,281,385,488,577,655,746,837,923,1009,1100,1176,1251,1330,1405,1487,1558,1678"}, "to": {"startLines": "65,66,85,86,87,143,144,261,262,264,265,269,271,272,273,275,276,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5148,5245,7577,7681,7784,12060,12138,23494,23585,23757,23843,24179,24334,24409,24488,24664,24746,24817", "endColumns": "96,83,103,102,88,77,90,90,85,85,90,75,74,78,74,81,70,119", "endOffsets": "5240,5324,7676,7779,7868,12133,12224,23580,23666,23838,23929,24250,24404,24483,24558,24741,24812,24932"}}]}]}