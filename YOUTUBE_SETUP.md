# إعداد YouTube Service Account للوكيل الذكي

## نظرة عامة

لتمكين الوكيل الذكي من رفع الفيديوهات تلقائياً على YouTube، تحتاج إلى إعداد Service Account مع الصلاحيات المناسبة.

## المتطلبات الأساسية

1. **حساب Google Cloud Platform**
2. **مشروع Google Cloud**
3. **قناة YouTube**
4. **صلاحيات إدارية على القناة**

## خطوات الإعداد

### 1. إنشاء مشروع Google Cloud

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. انقر على "Select a project" ثم "New Project"
3. أدخل اسم المشروع: `youtube-ai-agent`
4. <PERSON>خ<PERSON><PERSON> المؤسسة (إذا كان لديك)
5. انقر على "Create"

### 2. تفعيل YouTube Data API

1. في Google Cloud Console، اذهب إلى "APIs & Services" > "Library"
2. ابحث عن "YouTube Data API v3"
3. انقر على النتيجة الأولى
4. انقر على "Enable"

### 3. إنشاء Service Account

1. اذهب إلى "APIs & Services" > "Credentials"
2. انقر على "Create Credentials" > "Service Account"
3. أدخل التفاصيل:
   - **Service account name**: `youtube-uploader`
   - **Service account ID**: `youtube-uploader` (سيتم إنشاؤه تلقائياً)
   - **Description**: `Service account for YouTube video uploads`
4. انقر على "Create and Continue"

### 4. إعداد صلاحيات Service Account

1. في قسم "Grant this service account access to project":
   - اختر Role: "Editor" أو "YouTube Data API Admin"
2. انقر على "Continue"
3. في قسم "Grant users access to this service account" (اختياري)
4. انقر على "Done"

### 5. إنشاء مفتاح JSON

1. في صفحة "Credentials"، ابحث عن Service Account الذي أنشأته
2. انقر على البريد الإلكتروني للـ Service Account
3. اذهب إلى تبويب "Keys"
4. انقر على "Add Key" > "Create new key"
5. اختر "JSON"
6. انقر على "Create"
7. سيتم تحميل ملف JSON - احتفظ به بأمان!

### 6. ربط Service Account بقناة YouTube

هذه الخطوة **مهمة جداً** ومعقدة قليلاً:

#### الطريقة الأولى: استخدام OAuth2 (الأسهل)
1. قم بإنشاء OAuth2 credentials بدلاً من Service Account
2. استخدم OAuth2 flow لتسجيل الدخول بحساب صاحب القناة
3. احصل على refresh token واستخدمه

#### الطريقة الثانية: Domain-wide Delegation (للمؤسسات)
1. في إعدادات Service Account، فعل "Enable Google Workspace Domain-wide Delegation"
2. أضف الصلاحيات المطلوبة في Google Workspace Admin Console

#### الطريقة الثالثة: استخدام YouTube Brand Account
1. إنشاء Brand Account جديد
2. إضافة Service Account كمدير للـ Brand Account

## الصلاحيات المطلوبة

Service Account يحتاج الصلاحيات التالية:

```
https://www.googleapis.com/auth/youtube.upload
https://www.googleapis.com/auth/youtube
https://www.googleapis.com/auth/youtubepartner
```

## بنية ملف Service Account JSON

```json
*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## استخدام الملف في التطبيق

### 1. رفع الملف في التطبيق
1. افتح التطبيق
2. اذهب إلى الإعدادات
3. انقر على "إعداد YouTube"
4. اختر "رفع ملف Service Account"
5. اختر ملف JSON الذي حملته

### 2. التحقق من الإعداد
1. التطبيق سيتحقق تلقائياً من صحة الملف
2. سيختبر الاتصال بـ YouTube API
3. سيعرض رسالة نجاح أو خطأ

## استكشاف الأخطاء الشائعة

### خطأ "Access denied"
**السبب**: Service Account ليس له صلاحية على القناة
**الحل**: 
- تأكد من ربط Service Account بالقناة
- استخدم OAuth2 بدلاً من Service Account
- تحقق من صلاحيات المشروع

### خطأ "Invalid credentials"
**السبب**: ملف JSON غير صحيح أو منتهي الصلاحية
**الحل**:
- تحقق من صحة ملف JSON
- أنشئ مفتاح جديد
- تأكد من تفعيل YouTube Data API

### خطأ "Quota exceeded"
**السبب**: تجاوز حد الاستخدام اليومي
**الحل**:
- انتظر حتى اليوم التالي
- اطلب زيادة الحد من Google Cloud Console

### خطأ "Channel not found"
**السبب**: Service Account لا يمكنه الوصول للقناة
**الحل**:
- تأكد من ربط Service Account بالقناة
- استخدم OAuth2 للمصادقة

## الحدود والقيود

### حدود YouTube API
- **رفع الفيديوهات**: 6 فيديوهات كل 24 ساعة (للحسابات الجديدة)
- **حجم الفيديو**: حد أقصى 256 جيجابايت أو 12 ساعة
- **طول الفيديو**: حد أقصى 15 دقيقة (للحسابات غير المؤكدة)

### حدود Service Account
- **عدد المفاتيح**: حد أقصى 10 مفاتيح لكل Service Account
- **صلاحية المفتاح**: لا تنتهي صلاحيتها تلقائياً
- **الاستخدام**: يجب ربطها بقناة أو استخدام OAuth2

## الأمان

### حماية ملف Service Account
1. **لا تشارك الملف**: احتفظ بالملف سرياً
2. **لا ترفعه على Git**: أضف `*.json` إلى `.gitignore`
3. **استخدم التشفير**: شفر الملف عند التخزين
4. **راقب الاستخدام**: تحقق من سجلات الاستخدام بانتظام

### أفضل الممارسات
1. **صلاحيات محدودة**: أعط أقل صلاحيات ممكنة
2. **مراقبة النشاط**: راقب نشاط Service Account
3. **تدوير المفاتيح**: غير المفاتيح دورياً
4. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية آمنة

## البدائل

### OAuth2 (الموصى به)
```
المزايا:
- أسهل في الإعداد
- صلاحيات كاملة على القناة
- لا يحتاج ربط معقد

العيوب:
- يحتاج تجديد دوري للـ tokens
- يتطلب تفاعل المستخدم أولياً
```

### YouTube Brand Account
```
المزايا:
- إدارة أسهل للصلاحيات
- يمكن إضافة عدة مديرين

العيوب:
- يحتاج إعداد إضافي
- قد يكون معقد للمبتدئين
```

## الدعم

للحصول على المساعدة:
1. [YouTube Data API Documentation](https://developers.google.com/youtube/v3)
2. [Google Cloud Service Accounts](https://cloud.google.com/iam/docs/service-accounts)
3. [Stack Overflow - YouTube API](https://stackoverflow.com/questions/tagged/youtube-api)

## ملاحظات مهمة

⚠️ **تحذير**: Service Account وحده لا يكفي لرفع الفيديوهات. يجب ربطه بقناة YouTube أو استخدام OAuth2.

💡 **نصيحة**: للاستخدام الشخصي، OAuth2 أسهل وأكثر موثوقية من Service Account.

🔒 **أمان**: لا تشارك ملف Service Account مع أي شخص ولا ترفعه على الإنترنت.
