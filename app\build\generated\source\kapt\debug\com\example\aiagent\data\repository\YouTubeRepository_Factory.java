package com.example.aiagent.data.repository;

import android.content.Context;
import com.example.aiagent.data.api.YouTubeApiService;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class YouTubeRepository_Factory implements Factory<YouTubeRepository> {
  private final Provider<YouTubeApiService> youTubeApiServiceProvider;

  private final Provider<Context> contextProvider;

  public YouTubeRepository_Factory(Provider<YouTubeApiService> youTubeApiServiceProvider,
      Provider<Context> contextProvider) {
    this.youTubeApiServiceProvider = youTubeApiServiceProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public YouTubeRepository get() {
    return newInstance(youTubeApiServiceProvider.get(), contextProvider.get());
  }

  public static YouTubeRepository_Factory create(
      Provider<YouTubeApiService> youTubeApiServiceProvider, Provider<Context> contextProvider) {
    return new YouTubeRepository_Factory(youTubeApiServiceProvider, contextProvider);
  }

  public static YouTubeRepository newInstance(YouTubeApiService youTubeApiService,
      Context context) {
    return new YouTubeRepository(youTubeApiService, context);
  }
}
