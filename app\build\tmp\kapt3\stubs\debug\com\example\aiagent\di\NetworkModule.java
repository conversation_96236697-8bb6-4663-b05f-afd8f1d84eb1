package com.example.aiagent.di;

/**
 * وحدة Hilt للشبكة والـ APIs
 */
@dagger.Module()
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u008c\u0001\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c7\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002JB\u0010\u0003\u001a\u00020\u00042\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\n2\u0006\u0010\u000b\u001a\u00020\f2\u0006\u0010\r\u001a\u00020\u000e2\u0006\u0010\u000f\u001a\u00020\u00102\u0006\u0010\u0011\u001a\u00020\u0012H\u0007J\"\u0010\u0013\u001a\u00020\b2\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u0017H\u0007J\b\u0010\u0018\u001a\u00020\u0015H\u0007J\u0012\u0010\u0019\u001a\u00020\u001a2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010\u001b\u001a\u00020\f2\u0006\u0010\u001c\u001a\u00020\u001aH\u0007J\u0012\u0010\u001d\u001a\u00020\u00102\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\b\u0010\u001e\u001a\u00020\u001fH\u0007J\u0010\u0010 \u001a\u00020!2\u0006\u0010\"\u001a\u00020\u001fH\u0007J\u0012\u0010#\u001a\u00020\u000e2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u001a\u0010$\u001a\u00020\u00122\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u000b\u001a\u00020\fH\u0007J\u0010\u0010%\u001a\u00020&2\u0006\u0010'\u001a\u00020(H\u0007J\u001a\u0010)\u001a\u00020\u00172\u0006\u0010\"\u001a\u00020\u001f2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u0010\u0010*\u001a\u00020+2\u0006\u0010,\u001a\u00020!H\u0007J\u0012\u0010-\u001a\u00020.2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\u001a\u0010/\u001a\u0002002\u0006\u0010\u001c\u001a\u00020+2\b\b\u0001\u0010\u0005\u001a\u00020\u0006H\u0007J\"\u00101\u001a\u0002022\b\b\u0001\u0010\u0005\u001a\u00020\u00062\u0006\u00103\u001a\u00020.2\u0006\u0010\u000f\u001a\u00020\u0010H\u0007\u00a8\u00064"}, d2 = {"Lcom/example/aiagent/di/NetworkModule;", "", "()V", "provideAiAgentManager", "Lcom/example/aiagent/core/AiAgentManager;", "context", "Landroid/content/Context;", "firebaseVideoRepository", "Lcom/example/aiagent/data/repository/FirebaseVideoRepository;", "videoProjectRepository", "Lcom/example/aiagent/data/repository/VideoProjectRepository;", "geminiRepository", "Lcom/example/aiagent/data/repository/GeminiRepository;", "schedulerManager", "Lcom/example/aiagent/utils/SchedulerManager;", "notificationHelper", "Lcom/example/aiagent/utils/NotificationHelper;", "smartContentManager", "Lcom/example/aiagent/utils/SmartContentManager;", "provideFirebaseVideoRepository", "firebaseVideoService", "Lcom/example/aiagent/data/firebase/FirebaseVideoService;", "videoDownloader", "Lcom/example/aiagent/utils/VideoDownloader;", "provideFirebaseVideoService", "provideGeminiApiService", "Lcom/example/aiagent/data/api/GeminiApiService;", "provideGeminiRepository", "apiService", "provideNotificationHelper", "provideOkHttpClient", "Lokhttp3/OkHttpClient;", "provideRetrofit", "Lretrofit2/Retrofit;", "okHttpClient", "provideSchedulerManager", "provideSmartContentManager", "provideUserSettingsRepository", "Lcom/example/aiagent/data/repository/UserSettingsRepository;", "userSettingsDao", "Lcom/example/aiagent/data/database/UserSettingsDao;", "provideVideoDownloader", "provideYouTubeApiService", "Lcom/example/aiagent/data/api/YouTubeApiService;", "retrofit", "provideYouTubeAuthManager", "Lcom/example/aiagent/utils/YouTubeAuthManager;", "provideYouTubeRepository", "Lcom/example/aiagent/data/repository/YouTubeRepository;", "provideYouTubeSetupManager", "Lcom/example/aiagent/utils/YouTubeSetupManager;", "youTubeAuthManager", "app_debug"})
@dagger.hilt.InstallIn(value = {dagger.hilt.components.SingletonComponent.class})
public final class NetworkModule {
    @org.jetbrains.annotations.NotNull()
    public static final com.example.aiagent.di.NetworkModule INSTANCE = null;
    
    private NetworkModule() {
        super();
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final okhttp3.OkHttpClient provideOkHttpClient() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final retrofit2.Retrofit provideRetrofit(@org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.api.YouTubeApiService provideYouTubeApiService(@org.jetbrains.annotations.NotNull()
    retrofit2.Retrofit retrofit) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.api.GeminiApiService provideGeminiApiService(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.repository.YouTubeRepository provideYouTubeRepository(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.api.YouTubeApiService apiService, @dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.repository.GeminiRepository provideGeminiRepository(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.api.GeminiApiService apiService) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.utils.VideoDownloader provideVideoDownloader(@org.jetbrains.annotations.NotNull()
    okhttp3.OkHttpClient okHttpClient, @dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.firebase.FirebaseVideoService provideFirebaseVideoService() {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.repository.FirebaseVideoRepository provideFirebaseVideoRepository(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.firebase.FirebaseVideoService firebaseVideoService, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.VideoDownloader videoDownloader) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.utils.SmartContentManager provideSmartContentManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.repository.GeminiRepository geminiRepository) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.repository.UserSettingsRepository provideUserSettingsRepository(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.database.UserSettingsDao userSettingsDao) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.utils.NotificationHelper provideNotificationHelper(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.utils.SchedulerManager provideSchedulerManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.utils.YouTubeSetupManager provideYouTubeSetupManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.YouTubeAuthManager youTubeAuthManager, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.NotificationHelper notificationHelper) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.utils.YouTubeAuthManager provideYouTubeAuthManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context) {
        return null;
    }
    
    @dagger.Provides()
    @javax.inject.Singleton()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.core.AiAgentManager provideAiAgentManager(@dagger.hilt.android.qualifiers.ApplicationContext()
    @org.jetbrains.annotations.NotNull()
    android.content.Context context, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.repository.FirebaseVideoRepository firebaseVideoRepository, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.repository.VideoProjectRepository videoProjectRepository, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.repository.GeminiRepository geminiRepository, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.SchedulerManager schedulerManager, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.NotificationHelper notificationHelper, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.SmartContentManager smartContentManager) {
        return null;
    }
}