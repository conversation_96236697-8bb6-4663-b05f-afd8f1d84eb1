package com.example.aiagent;

import androidx.hilt.work.HiltWorkerFactory;
import com.example.aiagent.utils.FirebaseInitializer;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class AiAgentApplication_MembersInjector implements MembersInjector<AiAgentApplication> {
  private final Provider<HiltWorkerFactory> workerFactoryProvider;

  private final Provider<FirebaseInitializer> firebaseInitializerProvider;

  public AiAgentApplication_MembersInjector(Provider<HiltWorkerFactory> workerFactoryProvider,
      Provider<FirebaseInitializer> firebaseInitializerProvider) {
    this.workerFactoryProvider = workerFactoryProvider;
    this.firebaseInitializerProvider = firebaseInitializerProvider;
  }

  public static MembersInjector<AiAgentApplication> create(
      Provider<HiltWorkerFactory> workerFactoryProvider,
      Provider<FirebaseInitializer> firebaseInitializerProvider) {
    return new AiAgentApplication_MembersInjector(workerFactoryProvider, firebaseInitializerProvider);
  }

  @Override
  public void injectMembers(AiAgentApplication instance) {
    injectWorkerFactory(instance, workerFactoryProvider.get());
    injectFirebaseInitializer(instance, firebaseInitializerProvider.get());
  }

  @InjectedFieldSignature("com.example.aiagent.AiAgentApplication.workerFactory")
  public static void injectWorkerFactory(AiAgentApplication instance,
      HiltWorkerFactory workerFactory) {
    instance.workerFactory = workerFactory;
  }

  @InjectedFieldSignature("com.example.aiagent.AiAgentApplication.firebaseInitializer")
  public static void injectFirebaseInitializer(AiAgentApplication instance,
      FirebaseInitializer firebaseInitializer) {
    instance.firebaseInitializer = firebaseInitializer;
  }
}
