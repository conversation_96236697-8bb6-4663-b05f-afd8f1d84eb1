<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\aiagent\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\aiagent\app\src\main\res"><file name="ic_app" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_app.xml" qualifiers="" type="drawable"/><file name="ic_error" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_error.xml" qualifiers="" type="drawable"/><file name="ic_info" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_info.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_permission" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_permission.xml" qualifiers="" type="drawable"/><file name="ic_processing" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_processing.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_stop" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_stop.xml" qualifiers="" type="drawable"/><file name="ic_success" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_success.xml" qualifiers="" type="drawable"/><file name="ic_upload" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_upload.xml" qualifiers="" type="drawable"/><file name="ic_video_processing" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_video_processing.xml" qualifiers="" type="drawable"/><file name="ic_warning" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_warning.xml" qualifiers="" type="drawable"/><file name="ic_youtube" path="C:\Users\<USER>\aiagent\app\src\main\res\drawable\ic_youtube.xml" qualifiers="" type="drawable"/><file name="roboto_bold" path="C:\Users\<USER>\aiagent\app\src\main\res\font\roboto_bold.xml" qualifiers="" type="font"/><file name="roboto_medium" path="C:\Users\<USER>\aiagent\app\src\main\res\font\roboto_medium.xml" qualifiers="" type="font"/><file name="roboto_regular" path="C:\Users\<USER>\aiagent\app\src\main\res\font\roboto_regular.xml" qualifiers="" type="font"/><file name="activity_main" path="C:\Users\<USER>\aiagent\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\aiagent\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\aiagent\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\aiagent\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">وكيل الفيديو الذكي</string><string name="nav_dashboard">الرئيسية</string><string name="nav_projects">المشاريع</string><string name="nav_analytics">الإحصائيات</string><string name="nav_settings">الإعدادات</string><string name="dashboard_welcome">مرحباً بك في وكيل الفيديو الذكي</string><string name="dashboard_channel">قناة: %s</string><string name="dashboard_agent_active">الوكيل نشط</string><string name="dashboard_agent_inactive">الوكيل متوقف</string><string name="stats_total_videos">إجمالي الفيديوهات</string><string name="stats_uploaded_today">رُفع اليوم</string><string name="stats_pending_videos">في الانتظار</string><string name="stats_success_rate">معدل النجاح</string><string name="action_create_video">إنشاء فيديو</string><string name="action_create_immediate">إنشاء فوري</string><string name="action_schedule_video">جدولة فيديو</string><string name="action_schedule_upload">رفع مؤجل</string><string name="action_view_analytics">الإحصائيات</string><string name="action_view_reports">عرض التقارير</string><string name="action_open_settings">الإعدادات</string><string name="action_customize_app">تخصيص التطبيق</string><string name="recent_activity">النشاط الحديث</string><string name="view_all">عرض الكل</string><string name="no_recent_projects">لا توجد مشاريع حديثة</string><string name="start_new_project">ابدأ بإنشاء مشروع فيديو جديد</string><string name="agent_status">حالة الوكيل الذكي</string><string name="agent_active_working">نشط ويعمل</string><string name="agent_stopped">متوقف</string><string name="current_task">المهمة الحالية</string><string name="next_upload">الرفع التالي</string><string name="status_pending">في الانتظار</string><string name="status_downloading">جاري التحميل</string><string name="status_processing">جاري المعالجة</string><string name="status_generating_content">توليد المحتوى</string><string name="status_ready_to_upload">جاهز للرفع</string><string name="status_uploading">جاري الرفع</string><string name="status_uploaded">تم الرفع</string><string name="status_failed">فشل</string><string name="status_cancelled">ملغي</string><string name="settings_channel">إعدادات القناة</string><string name="settings_ai">إعدادات الذكاء الاصطناعي</string><string name="settings_youtube">إعدادات YouTube</string><string name="settings_upload">إعدادات الرفع</string><string name="settings_app">إعدادات التطبيق</string><string name="channel_name">اسم القناة</string><string name="gemini_api_key">Gemini API Key</string><string name="youtube_setup">ربط YouTube</string><string name="auto_upload">الرفع التلقائي</string><string name="upload_interval">فترة الرفع (ساعات)</string><string name="button_setup">إعداد</string><string name="button_save">حفظ</string><string name="button_cancel">إلغاء</string><string name="button_reset">إعادة تعيين</string><string name="button_export">تصدير</string><string name="button_import">استيراد</string><string name="success_updated">تم التحديث بنجاح</string><string name="error_update_failed">فشل في التحديث</string><string name="info_feature_coming_soon">هذه الميزة قيد التطوير</string><string name="notification_agent_started">تم بدء الوكيل الذكي</string><string name="notification_agent_stopped">تم إيقاف الوكيل الذكي</string><string name="notification_video_processing">جاري معالجة الفيديو</string><string name="notification_video_uploading">جاري رفع الفيديو</string><string name="notification_video_uploaded">تم رفع الفيديو بنجاح</string><string name="notification_error">حدث خطأ</string></file><file path="C:\Users\<USER>\aiagent\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.AiAgent" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.AiAgent" parent="Base.Theme.AiAgent"/></file><file path="C:\Users\<USER>\aiagent\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.AiAgent" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\aiagent\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\aiagent\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\aiagent\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\aiagent\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\aiagent\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\aiagent\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\aiagent\app\build\generated\res\processDebugGoogleServices"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\aiagent\app\build\generated\res\resValues\debug"/><source path="C:\Users\<USER>\aiagent\app\build\generated\res\processDebugGoogleServices"><file path="C:\Users\<USER>\aiagent\app\build\generated\res\processDebugGoogleServices\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">123456789-abcdef.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">123456789</string><string name="google_api_key" translatable="false">AIzaSyDemoKeyForBuildPurposes123456789</string><string name="google_app_id" translatable="false">1:123456789:android:abcdef123456</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyDemoKeyForBuildPurposes123456789</string><string name="google_storage_bucket" translatable="false">ai-video-agent-demo.appspot.com</string><string name="project_id" translatable="false">ai-video-agent-demo</string></file></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="res-processDebugGoogleServices" generated-set="res-processDebugGoogleServices$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>