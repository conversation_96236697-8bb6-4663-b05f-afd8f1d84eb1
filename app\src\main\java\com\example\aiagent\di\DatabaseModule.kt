package com.example.aiagent.di

import android.content.Context
import androidx.room.Room
import com.example.aiagent.data.database.*
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * وحدة Hilt لقاعدة البيانات
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideAiAgentDatabase(@ApplicationContext context: Context): AiAgentDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            AiAgentDatabase::class.java,
            "ai_agent_database"
        )
        .fallbackToDestructiveMigration()
        .build()
    }

    @Provides
    fun provideVideoProjectDao(database: AiAgentDatabase): VideoProjectDao {
        return database.videoProjectDao()
    }

    @Provides
    fun provideUserSettingsDao(database: AiAgentDatabase): UserSettingsDao {
        return database.userSettingsDao()
    }

    @Provides
    fun provideAppStatisticsDao(database: AiAgentDatabase): AppStatisticsDao {
        return database.appStatisticsDao()
    }

    @Provides
    fun provideOperationLogDao(database: AiAgentDatabase): OperationLogDao {
        return database.operationLogDao()
    }
}
