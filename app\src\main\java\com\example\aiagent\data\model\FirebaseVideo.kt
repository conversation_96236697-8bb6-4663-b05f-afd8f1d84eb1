package com.example.aiagent.data.model

import com.google.firebase.firestore.DocumentId
import com.google.firebase.firestore.PropertyName
import kotlinx.serialization.Serializable

/**
 * نموذج الفيديو في Firebase
 */
@Serializable
data class FirebaseVideo(
    @DocumentId
    val id: String = "",
    
    @PropertyName("title")
    val title: String = "",
    
    @PropertyName("description")
    val description: String = "",
    
    @PropertyName("download_url")
    val downloadUrl: String = "",
    
    @PropertyName("thumbnail_url")
    val thumbnailUrl: String = "",
    
    @PropertyName("duration")
    val duration: Int = 0, // بالثواني
    
    @PropertyName("file_size")
    val fileSize: Long = 0, // بالبايت
    
    @PropertyName("resolution")
    val resolution: String = "", // مثل "720x1280"
    
    @PropertyName("category")
    val category: VideoCategory = VideoCategory.FUNNY,
    
    @PropertyName("tags")
    val tags: List<String> = emptyList(),
    
    @PropertyName("language")
    val language: String = "ar", // اللغة
    
    @PropertyName("upload_date")
    val uploadDate: Long = 0, // timestamp
    
    @PropertyName("view_count")
    val viewCount: Int = 0,
    
    @PropertyName("download_count")
    val downloadCount: Int = 0,
    
    @PropertyName("rating")
    val rating: Float = 0f, // من 1 إلى 5
    
    @PropertyName("is_active")
    val isActive: Boolean = true,
    
    @PropertyName("source")
    val source: String = "", // مصدر الفيديو الأصلي
    
    @PropertyName("keywords")
    val keywords: List<String> = emptyList(), // كلمات مفتاحية للبحث
    
    @PropertyName("mood")
    val mood: VideoMood = VideoMood.HAPPY,
    
    @PropertyName("age_rating")
    val ageRating: AgeRating = AgeRating.ALL_AGES
)

/**
 * فئات الفيديو
 */
enum class VideoCategory {
    FUNNY,          // مضحك
    EDUCATIONAL,    // تعليمي
    ENTERTAINMENT,  // ترفيهي
    SPORTS,         // رياضي
    MUSIC,          // موسيقي
    COOKING,        // طبخ
    TRAVEL,         // سفر
    TECHNOLOGY,     // تقنية
    ANIMALS,        // حيوانات
    KIDS,           // أطفال
    LIFESTYLE,      // نمط حياة
    NEWS,           // أخبار
    GAMING,         // ألعاب
    FASHION,        // موضة
    HEALTH,         // صحة
    OTHER           // أخرى
}

/**
 * مزاج الفيديو
 */
enum class VideoMood {
    HAPPY,          // سعيد
    FUNNY,          // مضحك
    EXCITING,       // مثير
    RELAXING,       // مريح
    INSPIRING,      // ملهم
    EMOTIONAL,      // عاطفي
    ENERGETIC,      // نشيط
    CALM,           // هادئ
    MYSTERIOUS,     // غامض
    ROMANTIC        // رومانسي
}

/**
 * تصنيف عمري
 */
enum class AgeRating {
    ALL_AGES,       // جميع الأعمار
    TEENS,          // المراهقين
    ADULTS,         // البالغين
    MATURE          // ناضج
}

/**
 * معايير البحث في Firebase
 */
@Serializable
data class VideoSearchCriteria(
    val category: VideoCategory? = null,
    val mood: VideoMood? = null,
    val language: String? = null,
    val maxDuration: Int? = null, // بالثواني
    val minRating: Float? = null,
    val tags: List<String> = emptyList(),
    val keywords: List<String> = emptyList(),
    val excludeUsedVideos: Boolean = true,
    val limit: Int = 10
)

/**
 * إحصائيات الفيديو
 */
@Serializable
data class VideoStats(
    val totalVideos: Int = 0,
    val videosByCategory: Map<VideoCategory, Int> = emptyMap(),
    val videosByMood: Map<VideoMood, Int> = emptyMap(),
    val averageRating: Float = 0f,
    val totalDownloads: Int = 0,
    val mostPopularTags: List<String> = emptyList()
)

/**
 * سجل استخدام الفيديو
 */
@Serializable
data class VideoUsageLog(
    @DocumentId
    val id: String = "",
    
    @PropertyName("video_id")
    val videoId: String = "",
    
    @PropertyName("user_id")
    val userId: String = "",
    
    @PropertyName("download_date")
    val downloadDate: Long = System.currentTimeMillis(),
    
    @PropertyName("upload_date")
    val uploadDate: Long? = null,
    
    @PropertyName("youtube_video_id")
    val youtubeVideoId: String? = null,
    
    @PropertyName("success")
    val success: Boolean = false,
    
    @PropertyName("error_message")
    val errorMessage: String? = null
)
