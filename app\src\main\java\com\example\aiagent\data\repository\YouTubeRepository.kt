package com.example.aiagent.data.repository

import android.content.Context
import android.util.Log
import com.example.aiagent.data.api.YouTubeApiService
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.utils.YouTubeAuthManager
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مستودع YouTube
 * يدير رفع الفيديوهات على YouTube باستخدام Service Account
 */
@Singleton
class YouTubeRepository @Inject constructor(
    private val youTubeApiService: YouTubeApiService,
    @ApplicationContext private val context: Context
) {
    
    private val authManager = YouTubeAuthManager(context)
    
    companion object {
        private const val TAG = "YouTubeRepository"
        private const val MAX_RETRIES = 3
        private const val RETRY_DELAY = 5000L // 5 ثواني
    }

    /**
     * رفع فيديو على YouTube
     */
    suspend fun uploadVideo(
        project: VideoProject,
        onProgress: (Int) -> Unit = {}
    ): Result<String> = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "Starting YouTube upload for project: ${project.id}")
            
            // التحقق من وجود ملف الفيديو
            val videoFile = File(project.processedVideoPath ?: "")
            if (!videoFile.exists()) {
                return@withContext Result.failure(Exception("ملف الفيديو غير موجود"))
            }
            
            // الحصول على رمز الوصول
            onProgress(10)
            val accessToken = authManager.getAccessToken()
                ?: return@withContext Result.failure(Exception("فشل في الحصول على رمز الوصول"))
            
            // إعداد بيانات الفيديو
            onProgress(20)
            val videoMetadata = prepareVideoMetadata(project)
            val videoStatus = prepareVideoStatus()
            
            // رفع الفيديو
            onProgress(30)
            val uploadResult = uploadVideoWithRetry(
                accessToken = accessToken,
                videoFile = videoFile,
                metadata = videoMetadata,
                status = videoStatus,
                onProgress = { progress ->
                    // تحويل تقدم الرفع إلى نطاق 30-90
                    val adjustedProgress = 30 + (progress * 60 / 100)
                    onProgress(adjustedProgress)
                }
            )
            
            if (uploadResult.isSuccess) {
                val videoId = uploadResult.getOrNull()!!
                onProgress(100)
                
                Log.d(TAG, "Video uploaded successfully: $videoId")
                Result.success(videoId)
            } else {
                Result.failure(uploadResult.exceptionOrNull() ?: Exception("فشل في رفع الفيديو"))
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error uploading video", e)
            Result.failure(e)
        }
    }

    /**
     * رفع الفيديو مع إعادة المحاولة
     */
    private suspend fun uploadVideoWithRetry(
        accessToken: String,
        videoFile: File,
        metadata: String,
        status: String,
        onProgress: (Int) -> Unit,
        retryCount: Int = 0
    ): Result<String> {
        return try {
            val result = performVideoUpload(accessToken, videoFile, metadata, status, onProgress)
            
            if (result.isSuccess) {
                result
            } else if (retryCount < MAX_RETRIES) {
                Log.w(TAG, "Upload failed, retrying... (${retryCount + 1}/$MAX_RETRIES)")
                kotlinx.coroutines.delay(RETRY_DELAY)
                uploadVideoWithRetry(accessToken, videoFile, metadata, status, onProgress, retryCount + 1)
            } else {
                result
            }
        } catch (e: Exception) {
            if (retryCount < MAX_RETRIES) {
                Log.w(TAG, "Upload exception, retrying... (${retryCount + 1}/$MAX_RETRIES)", e)
                kotlinx.coroutines.delay(RETRY_DELAY)
                uploadVideoWithRetry(accessToken, videoFile, metadata, status, onProgress, retryCount + 1)
            } else {
                Result.failure(e)
            }
        }
    }

    /**
     * تنفيذ رفع الفيديو الفعلي
     */
    private suspend fun performVideoUpload(
        accessToken: String,
        videoFile: File,
        metadata: String,
        status: String,
        onProgress: (Int) -> Unit
    ): Result<String> {
        return try {
            // إنشاء أجزاء الطلب
            val metadataPart = metadata.toRequestBody("application/json".toMediaTypeOrNull())
            val statusPart = status.toRequestBody("application/json".toMediaTypeOrNull())
            
            // إنشاء جزء الفيديو مع مراقبة التقدم
            val videoRequestBody = ProgressRequestBody(
                videoFile.asRequestBody("video/mp4".toMediaTypeOrNull()),
                onProgress
            )
            val videoPart = MultipartBody.Part.createFormData("video", videoFile.name, videoRequestBody)
            
            // تنفيذ الطلب
            val response = youTubeApiService.uploadVideo(
                authorization = "Bearer $accessToken",
                snippet = metadataPart,
                status = statusPart,
                video = videoPart
            )
            
            if (response.isSuccessful) {
                val videoResponse = response.body()
                if (videoResponse != null) {
                    Result.success(videoResponse.id)
                } else {
                    Result.failure(Exception("استجابة فارغة من YouTube"))
                }
            } else {
                val errorMessage = response.errorBody()?.string() ?: "خطأ غير معروف"
                Result.failure(Exception("فشل الرفع: ${response.code()} - $errorMessage"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * إعداد بيانات الفيديو
     */
    private fun prepareVideoMetadata(project: VideoProject): String {
        val metadata = mapOf(
            "title" to project.title,
            "description" to project.description,
            "tags" to project.hashtags,
            "categoryId" to "23", // Comedy category
            "defaultLanguage" to "ar",
            "defaultAudioLanguage" to "ar"
        )
        
        return com.google.gson.Gson().toJson(metadata)
    }

    /**
     * إعداد حالة الفيديو
     */
    private fun prepareVideoStatus(): String {
        val status = mapOf(
            "privacyStatus" to "public",
            "embeddable" to true,
            "license" to "youtube",
            "publicStatsViewable" to true,
            "madeForKids" to false
        )
        
        return com.google.gson.Gson().toJson(status)
    }

    /**
     * تحديث معلومات الفيديو
     */
    suspend fun updateVideoInfo(
        videoId: String,
        title: String? = null,
        description: String? = null,
        tags: List<String>? = null
    ): Result<Unit> = withContext(Dispatchers.IO) {
        try {
            val accessToken = authManager.getAccessToken()
                ?: return@withContext Result.failure(Exception("فشل في الحصول على رمز الوصول"))
            
            val updateRequest = com.example.aiagent.data.api.YouTubeVideoUpdateRequest(
                id = videoId,
                snippet = if (title != null || description != null || tags != null) {
                    com.example.aiagent.data.api.YouTubeVideoSnippet(
                        title = title ?: "",
                        description = description ?: "",
                        tags = tags,
                        categoryId = "23"
                    )
                } else null,
                status = null
            )
            
            val response = youTubeApiService.updateVideo(
                authorization = "Bearer $accessToken",
                part = "snippet",
                videoUpdate = updateRequest
            )
            
            if (response.isSuccessful) {
                Result.success(Unit)
            } else {
                Result.failure(Exception("فشل في تحديث الفيديو: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * الحصول على معلومات الفيديو
     */
    suspend fun getVideoInfo(videoId: String): Result<com.example.aiagent.data.api.YouTubeVideoResponse> = withContext(Dispatchers.IO) {
        try {
            val accessToken = authManager.getAccessToken()
                ?: return@withContext Result.failure(Exception("فشل في الحصول على رمز الوصول"))
            
            val response = youTubeApiService.getVideo(
                authorization = "Bearer $accessToken",
                videoId = videoId
            )
            
            if (response.isSuccessful) {
                val videoList = response.body()
                if (videoList != null && videoList.items.isNotEmpty()) {
                    Result.success(videoList.items.first())
                } else {
                    Result.failure(Exception("الفيديو غير موجود"))
                }
            } else {
                Result.failure(Exception("فشل في الحصول على معلومات الفيديو: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * الحصول على معلومات القناة
     */
    suspend fun getChannelInfo(): Result<com.example.aiagent.data.api.YouTubeChannelResponse> = withContext(Dispatchers.IO) {
        try {
            val accessToken = authManager.getAccessToken()
                ?: return@withContext Result.failure(Exception("فشل في الحصول على رمز الوصول"))
            
            val response = youTubeApiService.getChannel(
                authorization = "Bearer $accessToken"
            )
            
            if (response.isSuccessful) {
                val channelList = response.body()
                if (channelList != null && channelList.items.isNotEmpty()) {
                    Result.success(channelList.items.first())
                } else {
                    Result.failure(Exception("القناة غير موجودة"))
                }
            } else {
                Result.failure(Exception("فشل في الحصول على معلومات القناة: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تهيئة المصادقة
     */
    fun initializeAuth(serviceAccountJsonPath: String): Boolean {
        return authManager.initialize(serviceAccountJsonPath)
    }

    /**
     * التحقق من صحة المصادقة
     */
    suspend fun validateAuth(): Boolean {
        return authManager.getAccessToken() != null
    }
}
