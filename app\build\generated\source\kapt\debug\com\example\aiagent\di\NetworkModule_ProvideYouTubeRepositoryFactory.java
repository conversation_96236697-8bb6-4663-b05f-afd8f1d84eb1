package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.data.api.YouTubeApiService;
import com.example.aiagent.data.repository.YouTubeRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideYouTubeRepositoryFactory implements Factory<YouTubeRepository> {
  private final Provider<YouTubeApiService> apiServiceProvider;

  private final Provider<Context> contextProvider;

  public NetworkModule_ProvideYouTubeRepositoryFactory(
      Provider<YouTubeApiService> apiServiceProvider, Provider<Context> contextProvider) {
    this.apiServiceProvider = apiServiceProvider;
    this.contextProvider = contextProvider;
  }

  @Override
  public YouTubeRepository get() {
    return provideYouTubeRepository(apiServiceProvider.get(), contextProvider.get());
  }

  public static NetworkModule_ProvideYouTubeRepositoryFactory create(
      Provider<YouTubeApiService> apiServiceProvider, Provider<Context> contextProvider) {
    return new NetworkModule_ProvideYouTubeRepositoryFactory(apiServiceProvider, contextProvider);
  }

  public static YouTubeRepository provideYouTubeRepository(YouTubeApiService apiService,
      Context context) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideYouTubeRepository(apiService, context));
  }
}
