package com.example.aiagent.receiver;

import com.example.aiagent.utils.SchedulerManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class BootReceiver_MembersInjector implements MembersInjector<BootReceiver> {
  private final Provider<SchedulerManager> schedulerManagerProvider;

  public BootReceiver_MembersInjector(Provider<SchedulerManager> schedulerManagerProvider) {
    this.schedulerManagerProvider = schedulerManagerProvider;
  }

  public static MembersInjector<BootReceiver> create(
      Provider<SchedulerManager> schedulerManagerProvider) {
    return new BootReceiver_MembersInjector(schedulerManagerProvider);
  }

  @Override
  public void injectMembers(BootReceiver instance) {
    injectSchedulerManager(instance, schedulerManagerProvider.get());
  }

  @InjectedFieldSignature("com.example.aiagent.receiver.BootReceiver.schedulerManager")
  public static void injectSchedulerManager(BootReceiver instance,
      SchedulerManager schedulerManager) {
    instance.schedulerManager = schedulerManager;
  }
}
