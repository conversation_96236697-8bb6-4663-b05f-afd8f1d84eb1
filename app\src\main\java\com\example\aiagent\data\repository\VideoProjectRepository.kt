package com.example.aiagent.data.repository

import com.example.aiagent.data.database.VideoProjectDao
import com.example.aiagent.data.database.OperationLogDao
import com.example.aiagent.data.model.VideoProject
import com.example.aiagent.data.model.VideoStatus
import com.example.aiagent.data.model.OperationLog
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * مستودع إدارة مشاريع الفيديو
 */
@Singleton
class VideoProjectRepository @Inject constructor(
    private val videoProjectDao: VideoProjectDao,
    private val operationLogDao: OperationLogDao
) {
    
    /**
     * الحصول على جميع المشاريع
     */
    fun getAllProjects(): Flow<List<VideoProject>> {
        return videoProjectDao.getAllProjects()
    }
    
    /**
     * الحصول على مشروع بالمعرف
     */
    suspend fun getProjectById(id: String): VideoProject? {
        return videoProjectDao.getProjectById(id)
    }
    
    /**
     * الحصول على المشاريع حسب الحالة
     */
    suspend fun getProjectsByStatus(status: VideoStatus): List<VideoProject> {
        return videoProjectDao.getProjectsByStatus(status)
    }
    
    /**
     * الحصول على المشاريع المجدولة للرفع
     */
    suspend fun getScheduledProjects(): List<VideoProject> {
        val currentTime = System.currentTimeMillis()
        return videoProjectDao.getScheduledProjects(currentTime)
    }
    
    /**
     * إنشاء مشروع جديد
     */
    suspend fun createProject(project: VideoProject): Result<String> {
        return try {
            videoProjectDao.insertProject(project)
            logOperation(project.id, "CREATE_PROJECT", "SUCCESS", "تم إنشاء المشروع بنجاح")
            Result.success(project.id)
        } catch (e: Exception) {
            logOperation(project.id, "CREATE_PROJECT", "ERROR", "خطأ في إنشاء المشروع: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * تحديث مشروع
     */
    suspend fun updateProject(project: VideoProject): Result<Unit> {
        return try {
            videoProjectDao.updateProject(project)
            logOperation(project.id, "UPDATE_PROJECT", "SUCCESS", "تم تحديث المشروع")
            Result.success(Unit)
        } catch (e: Exception) {
            logOperation(project.id, "UPDATE_PROJECT", "ERROR", "خطأ في تحديث المشروع: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * تحديث حالة المشروع
     */
    suspend fun updateProjectStatus(id: String, status: VideoStatus, errorMessage: String? = null): Result<Unit> {
        return try {
            if (errorMessage != null) {
                videoProjectDao.updateProjectStatusWithError(id, status, errorMessage)
                logOperation(id, "UPDATE_STATUS", "ERROR", "تغيير الحالة إلى $status: $errorMessage")
            } else {
                videoProjectDao.updateProjectStatus(id, status)
                logOperation(id, "UPDATE_STATUS", "SUCCESS", "تغيير الحالة إلى $status")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            logOperation(id, "UPDATE_STATUS", "ERROR", "خطأ في تحديث الحالة: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * تحديث تقدم الرفع
     */
    suspend fun updateUploadProgress(id: String, progress: Int): Result<Unit> {
        return try {
            videoProjectDao.updateUploadProgress(id, progress)
            if (progress % 25 == 0) { // سجل كل 25%
                logOperation(id, "UPLOAD_PROGRESS", "INFO", "تقدم الرفع: $progress%")
            }
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * تحديث معرف فيديو يوتيوب
     */
    suspend fun updateYouTubeVideoId(id: String, youtubeVideoId: String): Result<Unit> {
        return try {
            videoProjectDao.updateYouTubeVideoId(id, youtubeVideoId, VideoStatus.UPLOADED)
            logOperation(id, "YOUTUBE_UPLOAD", "SUCCESS", "تم رفع الفيديو بنجاح. معرف يوتيوب: $youtubeVideoId")
            Result.success(Unit)
        } catch (e: Exception) {
            logOperation(id, "YOUTUBE_UPLOAD", "ERROR", "خطأ في تحديث معرف يوتيوب: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * زيادة عداد المحاولات
     */
    suspend fun incrementRetryCount(id: String): Result<Unit> {
        return try {
            videoProjectDao.incrementRetryCount(id)
            logOperation(id, "RETRY", "INFO", "زيادة عداد المحاولات")
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    /**
     * حذف مشروع
     */
    suspend fun deleteProject(id: String): Result<Unit> {
        return try {
            videoProjectDao.deleteProjectById(id)
            operationLogDao.deleteLogsByProjectId(id)
            logOperation(id, "DELETE_PROJECT", "SUCCESS", "تم حذف المشروع")
            Result.success(Unit)
        } catch (e: Exception) {
            logOperation(id, "DELETE_PROJECT", "ERROR", "خطأ في حذف المشروع: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * حذف المشاريع القديمة
     */
    suspend fun deleteOldProjects(daysOld: Int = 30): Result<Int> {
        return try {
            val timestamp = System.currentTimeMillis() - (daysOld * 24 * 60 * 60 * 1000L)
            val countBefore = videoProjectDao.getTotalProjectCount()
            videoProjectDao.deleteOldProjects(timestamp)
            operationLogDao.deleteOldLogs(timestamp)
            val countAfter = videoProjectDao.getTotalProjectCount()
            val deletedCount = countBefore - countAfter
            
            logOperation("SYSTEM", "CLEANUP", "SUCCESS", "تم حذف $deletedCount مشروع قديم")
            Result.success(deletedCount)
        } catch (e: Exception) {
            logOperation("SYSTEM", "CLEANUP", "ERROR", "خطأ في تنظيف المشاريع القديمة: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * الحصول على إحصائيات المشاريع
     */
    suspend fun getProjectStatistics(): ProjectStatistics {
        return try {
            ProjectStatistics(
                total = videoProjectDao.getTotalProjectCount(),
                pending = videoProjectDao.getProjectCountByStatus(VideoStatus.PENDING),
                processing = videoProjectDao.getProjectCountByStatus(VideoStatus.PROCESSING),
                uploading = videoProjectDao.getProjectCountByStatus(VideoStatus.UPLOADING),
                uploaded = videoProjectDao.getProjectCountByStatus(VideoStatus.UPLOADED),
                failed = videoProjectDao.getProjectCountByStatus(VideoStatus.FAILED),
                today = videoProjectDao.getTodayProjectCount(),
                yesterday = videoProjectDao.getYesterdayProjectCount()
            )
        } catch (e: Exception) {
            ProjectStatistics()
        }
    }
    
    /**
     * تسجيل عملية في السجل
     */
    private suspend fun logOperation(
        projectId: String,
        operation: String,
        status: String,
        message: String,
        duration: Long? = null
    ) {
        try {
            val log = OperationLog(
                videoProjectId = projectId,
                operation = operation,
                status = status,
                message = message,
                duration = duration
            )
            operationLogDao.insertLog(log)
        } catch (e: Exception) {
            // تجاهل أخطاء التسجيل لتجنب التأثير على العمليات الأساسية
        }
    }
}

/**
 * إحصائيات المشاريع
 */
data class ProjectStatistics(
    val total: Int = 0,
    val pending: Int = 0,
    val processing: Int = 0,
    val uploading: Int = 0,
    val uploaded: Int = 0,
    val failed: Int = 0,
    val today: Int = 0,
    val yesterday: Int = 0
)
