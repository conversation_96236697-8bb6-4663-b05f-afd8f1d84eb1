package com.example.aiagent.data.repository;

import com.example.aiagent.data.database.OperationLogDao;
import com.example.aiagent.data.database.VideoProjectDao;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class VideoProjectRepository_Factory implements Factory<VideoProjectRepository> {
  private final Provider<VideoProjectDao> videoProjectDaoProvider;

  private final Provider<OperationLogDao> operationLogDaoProvider;

  public VideoProjectRepository_Factory(Provider<VideoProjectDao> videoProjectDaoProvider,
      Provider<OperationLogDao> operationLogDaoProvider) {
    this.videoProjectDaoProvider = videoProjectDaoProvider;
    this.operationLogDaoProvider = operationLogDaoProvider;
  }

  @Override
  public VideoProjectRepository get() {
    return newInstance(videoProjectDaoProvider.get(), operationLogDaoProvider.get());
  }

  public static VideoProjectRepository_Factory create(
      Provider<VideoProjectDao> videoProjectDaoProvider,
      Provider<OperationLogDao> operationLogDaoProvider) {
    return new VideoProjectRepository_Factory(videoProjectDaoProvider, operationLogDaoProvider);
  }

  public static VideoProjectRepository newInstance(VideoProjectDao videoProjectDao,
      OperationLogDao operationLogDao) {
    return new VideoProjectRepository(videoProjectDao, operationLogDao);
  }
}
