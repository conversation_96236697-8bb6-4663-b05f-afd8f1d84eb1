package com.example.aiagent.worker;

import android.content.Context;
import androidx.work.WorkerParameters;
import com.example.aiagent.data.repository.VideoProjectRepository;
import com.example.aiagent.utils.NotificationHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class ScheduledUploadWorker_Factory {
  private final Provider<VideoProjectRepository> videoProjectRepositoryProvider;

  private final Provider<NotificationHelper> notificationHelperProvider;

  public ScheduledUploadWorker_Factory(
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    this.videoProjectRepositoryProvider = videoProjectRepositoryProvider;
    this.notificationHelperProvider = notificationHelperProvider;
  }

  public ScheduledUploadWorker get(Context context, WorkerParameters workerParams) {
    return newInstance(context, workerParams, videoProjectRepositoryProvider.get(), notificationHelperProvider.get());
  }

  public static ScheduledUploadWorker_Factory create(
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<NotificationHelper> notificationHelperProvider) {
    return new ScheduledUploadWorker_Factory(videoProjectRepositoryProvider, notificationHelperProvider);
  }

  public static ScheduledUploadWorker newInstance(Context context, WorkerParameters workerParams,
      VideoProjectRepository videoProjectRepository, NotificationHelper notificationHelper) {
    return new ScheduledUploadWorker(context, workerParams, videoProjectRepository, notificationHelper);
  }
}
