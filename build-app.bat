@echo off
echo Building AI Video Agent...

REM Set Java Home
set "JAVA_HOME=C:\Program Files\Java\jdk-24"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo Using Java: %JAVA_HOME%

REM Clean and build
echo Cleaning project...
call gradlew.bat clean

echo Building debug APK...
call gradlew.bat assembleDebug

REM Check if APK was created
if exist "app\build\outputs\apk\debug\app-debug.apk" (
    echo APK built successfully!
    echo Copying to Desktop...
    copy "app\build\outputs\apk\debug\app-debug.apk" "%USERPROFILE%\Desktop\ai-video-agent-debug.apk" /Y
    echo APK copied to Desktop as ai-video-agent-debug.apk
) else (
    echo APK build failed or file not found!
)

pause
