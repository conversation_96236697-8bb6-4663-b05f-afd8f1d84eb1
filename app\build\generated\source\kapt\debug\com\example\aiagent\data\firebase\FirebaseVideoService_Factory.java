package com.example.aiagent.data.firebase;

import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class FirebaseVideoService_Factory implements Factory<FirebaseVideoService> {
  @Override
  public FirebaseVideoService get() {
    return newInstance();
  }

  public static FirebaseVideoService_Factory create() {
    return InstanceHolder.INSTANCE;
  }

  public static FirebaseVideoService newInstance() {
    return new FirebaseVideoService();
  }

  private static final class InstanceHolder {
    private static final FirebaseVideoService_Factory INSTANCE = new FirebaseVideoService_Factory();
  }
}
