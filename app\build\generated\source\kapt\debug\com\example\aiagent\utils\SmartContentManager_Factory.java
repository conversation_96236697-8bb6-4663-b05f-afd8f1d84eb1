package com.example.aiagent.utils;

import android.content.Context;
import com.example.aiagent.data.repository.GeminiRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class SmartContentManager_Factory implements Factory<SmartContentManager> {
  private final Provider<Context> contextProvider;

  private final Provider<GeminiRepository> geminiRepositoryProvider;

  public SmartContentManager_Factory(Provider<Context> contextProvider,
      Provider<GeminiRepository> geminiRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.geminiRepositoryProvider = geminiRepositoryProvider;
  }

  @Override
  public SmartContentManager get() {
    return newInstance(contextProvider.get(), geminiRepositoryProvider.get());
  }

  public static SmartContentManager_Factory create(Provider<Context> contextProvider,
      Provider<GeminiRepository> geminiRepositoryProvider) {
    return new SmartContentManager_Factory(contextProvider, geminiRepositoryProvider);
  }

  public static SmartContentManager newInstance(Context context,
      GeminiRepository geminiRepository) {
    return new SmartContentManager(context, geminiRepository);
  }
}
