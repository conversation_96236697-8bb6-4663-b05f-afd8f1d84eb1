package com.example.aiagent.di;

import android.content.Context;
import com.example.aiagent.data.repository.GeminiRepository;
import com.example.aiagent.utils.SmartContentManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class NetworkModule_ProvideSmartContentManagerFactory implements Factory<SmartContentManager> {
  private final Provider<Context> contextProvider;

  private final Provider<GeminiRepository> geminiRepositoryProvider;

  public NetworkModule_ProvideSmartContentManagerFactory(Provider<Context> contextProvider,
      Provider<GeminiRepository> geminiRepositoryProvider) {
    this.contextProvider = contextProvider;
    this.geminiRepositoryProvider = geminiRepositoryProvider;
  }

  @Override
  public SmartContentManager get() {
    return provideSmartContentManager(contextProvider.get(), geminiRepositoryProvider.get());
  }

  public static NetworkModule_ProvideSmartContentManagerFactory create(
      Provider<Context> contextProvider, Provider<GeminiRepository> geminiRepositoryProvider) {
    return new NetworkModule_ProvideSmartContentManagerFactory(contextProvider, geminiRepositoryProvider);
  }

  public static SmartContentManager provideSmartContentManager(Context context,
      GeminiRepository geminiRepository) {
    return Preconditions.checkNotNullFromProvides(NetworkModule.INSTANCE.provideSmartContentManager(context, geminiRepository));
  }
}
