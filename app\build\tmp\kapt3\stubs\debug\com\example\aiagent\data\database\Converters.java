package com.example.aiagent.data.database;

/**
 * محولات الأنواع لـ Room
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010 \n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0006\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0016\u0010\u0005\u001a\u00020\u00062\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\bH\u0007J\u0010\u0010\t\u001a\u00020\u00062\u0006\u0010\n\u001a\u00020\u000bH\u0007J\u0010\u0010\f\u001a\u00020\u00062\u0006\u0010\r\u001a\u00020\u000eH\u0007J\u0010\u0010\u000f\u001a\u00020\u00062\u0006\u0010\u0010\u001a\u00020\u0011H\u0007J\u0010\u0010\u0012\u001a\u00020\u00062\u0006\u0010\u0013\u001a\u00020\u0014H\u0007J\u0016\u0010\u0015\u001a\b\u0012\u0004\u0012\u00020\u00060\b2\u0006\u0010\u0007\u001a\u00020\u0006H\u0007J\u0010\u0010\u0016\u001a\u00020\u000b2\u0006\u0010\n\u001a\u00020\u0006H\u0007J\u0010\u0010\u0017\u001a\u00020\u000e2\u0006\u0010\r\u001a\u00020\u0006H\u0007J\u0010\u0010\u0018\u001a\u00020\u00112\u0006\u0010\u0010\u001a\u00020\u0006H\u0007J\u0010\u0010\u0019\u001a\u00020\u00142\u0006\u0010\u0013\u001a\u00020\u0006H\u0007R\u000e\u0010\u0003\u001a\u00020\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u001a"}, d2 = {"Lcom/example/aiagent/data/database/Converters;", "", "()V", "json", "Lkotlinx/serialization/json/Json;", "fromStringList", "", "value", "", "fromUploadSchedule", "schedule", "Lcom/example/aiagent/data/model/UploadSchedule;", "fromVideoQuality", "quality", "Lcom/example/aiagent/data/model/VideoQuality;", "fromVideoStatus", "status", "Lcom/example/aiagent/data/model/VideoStatus;", "fromWatermarkStyle", "style", "Lcom/example/aiagent/data/model/WatermarkStyle;", "toStringList", "toUploadSchedule", "toVideoQuality", "toVideoStatus", "toWatermarkStyle", "app_debug"})
public final class Converters {
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.serialization.json.Json json = null;
    
    public Converters() {
        super();
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromStringList(@org.jetbrains.annotations.NotNull()
    java.util.List<java.lang.String> value) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.util.List<java.lang.String> toStringList(@org.jetbrains.annotations.NotNull()
    java.lang.String value) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromVideoStatus(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoStatus status) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.model.VideoStatus toVideoStatus(@org.jetbrains.annotations.NotNull()
    java.lang.String status) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromWatermarkStyle(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.WatermarkStyle style) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.model.WatermarkStyle toWatermarkStyle(@org.jetbrains.annotations.NotNull()
    java.lang.String style) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromVideoQuality(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.VideoQuality quality) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.model.VideoQuality toVideoQuality(@org.jetbrains.annotations.NotNull()
    java.lang.String quality) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String fromUploadSchedule(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.model.UploadSchedule schedule) {
        return null;
    }
    
    @androidx.room.TypeConverter()
    @org.jetbrains.annotations.NotNull()
    public final com.example.aiagent.data.model.UploadSchedule toUploadSchedule(@org.jetbrains.annotations.NotNull()
    java.lang.String schedule) {
        return null;
    }
}