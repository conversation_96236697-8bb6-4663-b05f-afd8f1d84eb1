package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.example.aiagent.AiAgentApplication",
    rootPackage = "com.example.aiagent",
    originatingRoot = "com.example.aiagent.AiAgentApplication",
    originatingRootPackage = "com.example.aiagent",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "AiAgentApplication",
    originatingRootSimpleNames = "AiAgentApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_example_aiagent_AiAgentApplication {
}
