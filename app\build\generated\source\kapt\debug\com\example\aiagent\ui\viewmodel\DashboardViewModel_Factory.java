package com.example.aiagent.ui.viewmodel;

import com.example.aiagent.core.AiAgentManager;
import com.example.aiagent.data.repository.UserSettingsRepository;
import com.example.aiagent.data.repository.VideoProjectRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation"
})
public final class DashboardViewModel_Factory implements Factory<DashboardViewModel> {
  private final Provider<AiAgentManager> aiAgentManagerProvider;

  private final Provider<VideoProjectRepository> videoProjectRepositoryProvider;

  private final Provider<UserSettingsRepository> userSettingsRepositoryProvider;

  public DashboardViewModel_Factory(Provider<AiAgentManager> aiAgentManagerProvider,
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<UserSettingsRepository> userSettingsRepositoryProvider) {
    this.aiAgentManagerProvider = aiAgentManagerProvider;
    this.videoProjectRepositoryProvider = videoProjectRepositoryProvider;
    this.userSettingsRepositoryProvider = userSettingsRepositoryProvider;
  }

  @Override
  public DashboardViewModel get() {
    return newInstance(aiAgentManagerProvider.get(), videoProjectRepositoryProvider.get(), userSettingsRepositoryProvider.get());
  }

  public static DashboardViewModel_Factory create(Provider<AiAgentManager> aiAgentManagerProvider,
      Provider<VideoProjectRepository> videoProjectRepositoryProvider,
      Provider<UserSettingsRepository> userSettingsRepositoryProvider) {
    return new DashboardViewModel_Factory(aiAgentManagerProvider, videoProjectRepositoryProvider, userSettingsRepositoryProvider);
  }

  public static DashboardViewModel newInstance(AiAgentManager aiAgentManager,
      VideoProjectRepository videoProjectRepository,
      UserSettingsRepository userSettingsRepository) {
    return new DashboardViewModel(aiAgentManager, videoProjectRepository, userSettingsRepository);
  }
}
