package com.example.aiagent.ui.viewmodel;

/**
 * ViewModel لشاشة الإعدادات
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000N\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\b\n\u0000\b\u0007\u0018\u00002\u00020\u0001B\u001f\b\u0007\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\b\u0010\u0010\u001a\u00020\u0011H\u0002J\u0006\u0010\u0012\u001a\u00020\u0011J\u0006\u0010\u0013\u001a\u00020\u0011J\b\u0010\u0014\u001a\u00020\u0011H\u0002J\u0006\u0010\u0015\u001a\u00020\u0011J\u0006\u0010\u0016\u001a\u00020\u0011J\u000e\u0010\u0017\u001a\u00020\u00112\u0006\u0010\u0018\u001a\u00020\u0019J\u000e\u0010\u001a\u001a\u00020\u00112\u0006\u0010\u001b\u001a\u00020\u001cJ\u000e\u0010\u001d\u001a\u00020\u00112\u0006\u0010\u001e\u001a\u00020\u001cJ\u000e\u0010\u001f\u001a\u00020\u00112\u0006\u0010 \u001a\u00020!R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\""}, d2 = {"Lcom/example/aiagent/ui/viewmodel/SettingsViewModel;", "Landroidx/lifecycle/ViewModel;", "userSettingsRepository", "Lcom/example/aiagent/data/repository/UserSettingsRepository;", "youTubeSetupManager", "Lcom/example/aiagent/utils/YouTubeSetupManager;", "notificationHelper", "Lcom/example/aiagent/utils/NotificationHelper;", "(Lcom/example/aiagent/data/repository/UserSettingsRepository;Lcom/example/aiagent/utils/YouTubeSetupManager;Lcom/example/aiagent/utils/NotificationHelper;)V", "_uiState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/example/aiagent/ui/viewmodel/SettingsUiState;", "uiState", "Lkotlinx/coroutines/flow/StateFlow;", "getUiState", "()Lkotlinx/coroutines/flow/StateFlow;", "checkYouTubeSetup", "", "exportData", "importData", "loadSettings", "resetSettings", "setupYouTube", "updateAutoUpload", "autoUpload", "", "updateChannelName", "channelName", "", "updateGeminiApiKey", "apiKey", "updateUploadInterval", "intervalHours", "", "app_debug"})
@dagger.hilt.android.lifecycle.HiltViewModel()
public final class SettingsViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.data.repository.UserSettingsRepository userSettingsRepository = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.utils.YouTubeSetupManager youTubeSetupManager = null;
    @org.jetbrains.annotations.NotNull()
    private final com.example.aiagent.utils.NotificationHelper notificationHelper = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.MutableStateFlow<com.example.aiagent.ui.viewmodel.SettingsUiState> _uiState = null;
    @org.jetbrains.annotations.NotNull()
    private final kotlinx.coroutines.flow.StateFlow<com.example.aiagent.ui.viewmodel.SettingsUiState> uiState = null;
    
    @javax.inject.Inject()
    public SettingsViewModel(@org.jetbrains.annotations.NotNull()
    com.example.aiagent.data.repository.UserSettingsRepository userSettingsRepository, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.YouTubeSetupManager youTubeSetupManager, @org.jetbrains.annotations.NotNull()
    com.example.aiagent.utils.NotificationHelper notificationHelper) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final kotlinx.coroutines.flow.StateFlow<com.example.aiagent.ui.viewmodel.SettingsUiState> getUiState() {
        return null;
    }
    
    /**
     * تحميل الإعدادات
     */
    private final void loadSettings() {
    }
    
    /**
     * فحص إعداد YouTube
     */
    private final void checkYouTubeSetup() {
    }
    
    /**
     * تحديث اسم القناة
     */
    public final void updateChannelName(@org.jetbrains.annotations.NotNull()
    java.lang.String channelName) {
    }
    
    /**
     * تحديث مفتاح Gemini API
     */
    public final void updateGeminiApiKey(@org.jetbrains.annotations.NotNull()
    java.lang.String apiKey) {
    }
    
    /**
     * تحديث الرفع التلقائي
     */
    public final void updateAutoUpload(boolean autoUpload) {
    }
    
    /**
     * تحديث فترة الرفع
     */
    public final void updateUploadInterval(int intervalHours) {
    }
    
    /**
     * إعداد YouTube
     */
    public final void setupYouTube() {
    }
    
    /**
     * إعادة تعيين الإعدادات
     */
    public final void resetSettings() {
    }
    
    /**
     * تصدير البيانات
     */
    public final void exportData() {
    }
    
    /**
     * استيراد البيانات
     */
    public final void importData() {
    }
}