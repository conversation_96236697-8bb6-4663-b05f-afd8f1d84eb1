{"logs": [{"outputFile": "com.example.aiagent.app-mergeDebugResources-88:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d592e4e76d99b11c1d7a6fa57b286ce0\\transformed\\appcompat-1.7.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,266", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1028,1125,1230,1316,1416,1529,1607,1684,1775,1868,1962,2056,2156,2249,2344,2438,2529,2620,2699,2809,2912,3008,3119,3221,3331,3490,23172", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "1023,1120,1225,1311,1411,1524,1602,1679,1770,1863,1957,2051,2151,2244,2339,2433,2524,2615,2694,2804,2907,3003,3114,3216,3326,3485,3582,23247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a1fd568d5a1c4f4d2aea1aab03507053\\transformed\\ui-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,281,373,472,560,638,736,824,908,987,1068,1140,1215,1290,1365,1445,1511", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "193,276,368,467,555,633,731,819,903,982,1063,1135,1210,1285,1360,1440,1506,1624"}, "to": {"startLines": "65,66,85,86,87,143,144,261,262,264,265,269,271,272,273,275,276,277", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5045,5138,7486,7578,7677,11792,11870,22763,22851,23012,23091,23413,23558,23633,23708,23884,23964,24030", "endColumns": "92,82,91,98,87,77,97,87,83,78,80,71,74,74,74,79,65,117", "endOffsets": "5133,5216,7573,7672,7760,11865,11963,22846,22930,23086,23167,23480,23628,23703,23778,23959,24025,24143"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a7478e3cc73cddc4127d485bacc71e97\\transformed\\play-services-base-18.5.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5221,5328,5501,5631,5740,5887,6016,6129,6383,6545,6654,6827,6959,7112,7273,7338,7404", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "5323,5496,5626,5735,5882,6011,6124,6227,6540,6649,6822,6954,7107,7268,7333,7399,7481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ba371c8e6e80451905d49b7ece1194da\\transformed\\play-services-basement-18.4.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6232", "endColumns": "150", "endOffsets": "6378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e773aa8fe135cf8e64f2a724d4a97259\\transformed\\exoplayer-core-2.19.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,268,334,409,475,574,670", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "121,186,263,329,404,470,569,665,750"}, "to": {"startLines": "114,115,116,117,118,119,120,121,122", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9825,9896,9961,10038,10104,10179,10245,10344,10440", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "9891,9956,10033,10099,10174,10240,10339,10435,10520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\0a10126c0c2cfe9f025de992aa0245b6\\transformed\\exoplayer-ui-2.19.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,667,756,843,926,1017,1111,1182,1245,1336,1427,1491,1554,1614,1682,1790,1907,2020,2090,2166,2237,2308,2394,2478,2544,2607,2660,2718,2766,2827,2887,2959,3021,3083,3144,3206,3271,3335,3401,3453,3513,3587,3661", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "279,482,662,751,838,921,1012,1106,1177,1240,1331,1422,1486,1549,1609,1677,1785,1902,2015,2085,2161,2232,2303,2389,2473,2539,2602,2655,2713,2761,2822,2882,2954,3016,3078,3139,3201,3266,3330,3396,3448,3508,3582,3656,3708"}, "to": {"startLines": "2,11,15,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,582,7885,7974,8061,8144,8235,8329,8400,8463,8554,8645,8709,8772,8832,8900,9008,9125,9238,9308,9384,9455,9526,9612,9696,9762,10525,10578,10636,10684,10745,10805,10877,10939,11001,11062,11124,11189,11253,11319,11371,11431,11505,11579", "endLines": "10,14,18,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "374,577,757,7969,8056,8139,8230,8324,8395,8458,8549,8640,8704,8767,8827,8895,9003,9120,9233,9303,9379,9450,9521,9607,9691,9757,9820,10573,10631,10679,10740,10800,10872,10934,10996,11057,11119,11184,11248,11314,11366,11426,11500,11574,11626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\74b501ce892fe2a4ee48d34996dbeeea\\transformed\\material-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1416,1479,1541,1599,1663,1724,1778,1892,1950,2010,2064,2134,2261,2342,2432,2531,2628,2707,2842,2918,2995,3124,3208,3290,3345,3400,3466,3535,3612,3683,3762,3830,3906,3976,4041,4143,4238,4311,4405,4498,4572,4641,4735,4791,4874,4941,5025,5113,5175,5239,5302,5369,5466,5572,5663,5765,5824,5883,5960,6045,6121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1411,1474,1536,1594,1658,1719,1773,1887,1945,2005,2059,2129,2256,2337,2427,2526,2623,2702,2837,2913,2990,3119,3203,3285,3340,3395,3461,3530,3607,3678,3757,3825,3901,3971,4036,4138,4233,4306,4400,4493,4567,4636,4730,4786,4869,4936,5020,5108,5170,5234,5297,5364,5461,5567,5658,5760,5819,5878,5955,6040,6116,6189"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,88,89,141,142,145,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,263,267,268,270", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "762,3587,3664,3743,3824,3923,4742,4850,4962,7765,7821,11631,11723,11968,18211,18296,18359,18421,18479,18543,18604,18658,18772,18830,18890,18944,19014,19141,19222,19312,19411,19508,19587,19722,19798,19875,20004,20088,20170,20225,20280,20346,20415,20492,20563,20642,20710,20786,20856,20921,21023,21118,21191,21285,21378,21452,21521,21615,21671,21754,21821,21905,21993,22055,22119,22182,22249,22346,22452,22543,22645,22704,22935,23252,23337,23485", "endLines": "22,50,51,52,53,54,62,63,64,88,89,141,142,145,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,263,267,268,270", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "920,3659,3738,3819,3918,4007,4845,4957,5040,7816,7880,11718,11787,12022,18291,18354,18416,18474,18538,18599,18653,18767,18825,18885,18939,19009,19136,19217,19307,19406,19503,19582,19717,19793,19870,19999,20083,20165,20220,20275,20341,20410,20487,20558,20637,20705,20781,20851,20916,21018,21113,21186,21280,21373,21447,21516,21610,21666,21749,21816,21900,21988,22050,22114,22177,22244,22341,22447,22538,22640,22699,22758,23007,23332,23408,23553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\81e789d1c3cb03c2f66092857ae417ac\\transformed\\material3-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,291,407,526,623,724,842,980,1104,1247,1332,1435,1525,1622,1734,1855,1963,2098,2235,2366,2532,2658,2773,2892,3012,3103,3199,3318,3454,3556,3659,3765,3897,4035,4146,4245,4321,4418,4519,4631,4716,4804,4903,4983,5067,5167,5266,5361,5459,5545,5646,5744,5846,5961,6041,6143", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "167,286,402,521,618,719,837,975,1099,1242,1327,1430,1520,1617,1729,1850,1958,2093,2230,2361,2527,2653,2768,2887,3007,3098,3194,3313,3449,3551,3654,3760,3892,4030,4141,4240,4316,4413,4514,4626,4711,4799,4898,4978,5062,5162,5261,5356,5454,5540,5641,5739,5841,5956,6036,6138,6234"}, "to": {"startLines": "146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12027,12144,12263,12379,12498,12595,12696,12814,12952,13076,13219,13304,13407,13497,13594,13706,13827,13935,14070,14207,14338,14504,14630,14745,14864,14984,15075,15171,15290,15426,15528,15631,15737,15869,16007,16118,16217,16293,16390,16491,16603,16688,16776,16875,16955,17039,17139,17238,17333,17431,17517,17618,17716,17818,17933,18013,18115", "endColumns": "116,118,115,118,96,100,117,137,123,142,84,102,89,96,111,120,107,134,136,130,165,125,114,118,119,90,95,118,135,101,102,105,131,137,110,98,75,96,100,111,84,87,98,79,83,99,98,94,97,85,100,97,101,114,79,101,95", "endOffsets": "12139,12258,12374,12493,12590,12691,12809,12947,13071,13214,13299,13402,13492,13589,13701,13822,13930,14065,14202,14333,14499,14625,14740,14859,14979,15070,15166,15285,15421,15523,15626,15732,15864,16002,16113,16212,16288,16385,16486,16598,16683,16771,16870,16950,17034,17134,17233,17328,17426,17512,17613,17711,17813,17928,18008,18110,18206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c1cbac527d871b7e6d27789e9f88bba3\\transformed\\foundation-release\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "278,279", "startColumns": "4,4", "startOffsets": "24148,24235", "endColumns": "86,86", "endOffsets": "24230,24317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9ddb98e863a1a5c2e78bcacdf9de3476\\transformed\\core-1.16.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,558,656,785", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "148,250,350,451,553,651,780,881"}, "to": {"startLines": "55,56,57,58,59,60,61,274", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4012,4110,4212,4312,4413,4515,4613,23783", "endColumns": "97,101,99,100,101,97,128,100", "endOffsets": "4105,4207,4307,4408,4510,4608,4737,23879"}}]}]}